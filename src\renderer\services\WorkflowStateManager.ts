import { create } from 'zustand';
import { persist, subscribeWithSelector } from 'zustand/middleware';
import { NavigationView } from '../stores/navigation';
// import { QueryCondition, VisualQuery } from '../stores/query';
interface QueryCondition { id: string; field: string; operator: string; value: string; }
interface VisualQuery { conditions: QueryCondition[]; }
import { SearchResult } from '../../shared/types';

// 工作流状态接口定义
export interface WorkflowState {
  // 连接相关状态
  connectionId: string;
  
  // 导航状态
  currentView: NavigationView;
  previousView?: NavigationView;
  
  // 查询状态
  queryState: {
    selectedIndex: string;
    queryMode: 'visual' | 'dsl';
    visualQuery: VisualQuery;
    dslQuery: string;
    currentPage: number;
    pageSize: number;
    lastExecutedQuery?: any;
    lastExecutionTime?: Date;
  };
  
  // 搜索结果状态
  searchState: {
    results: SearchResult | null;
    isSearching: boolean;
    searchError: string | null;
    resultCount: number;
  };
  
  // UI状态
  uiState: {
    sidebarCollapsed: boolean;
    activeTab?: string;
    scrollPosition: { [key: string]: number };
    expandedSections: string[];
    selectedDocuments: string[];
  };
  
  // 编辑器状态
  editorState: {
    openDocuments: Array<{
      id: string;
      index: string;
      document: any;
      isDirty: boolean;
      cursorPosition?: number;
    }>;
    activeDocumentId?: string;
  };
  
  // 时间戳
  lastUpdated: Date;
  lastAccessed: Date;
}

// 多连接状态管理接口
export interface MultiConnectionWorkflowState {
  // 当前活动连接
  activeConnectionId?: string;
  
  // 每个连接的独立状态
  connectionStates: { [connectionId: string]: WorkflowState };
  
  // 全局UI偏好
  globalPreferences: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    autoSave: boolean;
    autoSaveInterval: number; // 秒
  };
  
  // 应用级别状态
  appState: {
    windowSize: { width: number; height: number };
    windowPosition: { x: number; y: number };
    isMaximized: boolean;
    lastOpenedConnections: string[];
  };
}

// 状态持久化管理器
class WorkflowStateManager {
  private static instance: WorkflowStateManager;
  private autoSaveTimer?: NodeJS.Timeout;
  private stateChangeListeners: Array<(state: MultiConnectionWorkflowState) => void> = [];
  
  private constructor() {
    this.initializeAutoSave();
  }
  
  public static getInstance(): WorkflowStateManager {
    if (!WorkflowStateManager.instance) {
      WorkflowStateManager.instance = new WorkflowStateManager();
    }
    return WorkflowStateManager.instance;
  }
  
  // 初始化自动保存
  private initializeAutoSave() {
    const store = useWorkflowStateStore.getState();
    const interval = store.globalPreferences.autoSaveInterval * 1000;
    
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }
    
    this.autoSaveTimer = setInterval(() => {
      this.performAutoSave();
    }, interval);
  }
  
  // 执行自动保存
  private performAutoSave() {
    const store = useWorkflowStateStore.getState();
    if (!store.globalPreferences.autoSave) return;
    
    // 更新最后访问时间
    const currentTime = new Date();
    const { activeConnectionId, connectionStates } = store;
    
    if (activeConnectionId && connectionStates[activeConnectionId]) {
      store.updateConnectionState(activeConnectionId, {
        lastAccessed: currentTime,
        lastUpdated: currentTime,
      });
    }
    
    console.log('🔄 Auto-save completed at', currentTime.toISOString());
  }
  
  // 保存当前工作状态
  public saveCurrentWorkflowState(connectionId: string, partialState: Partial<WorkflowState>) {
    const store = useWorkflowStateStore.getState();
    const currentState = store.connectionStates[connectionId];
    
    const updatedState: WorkflowState = {
      ...currentState,
      ...partialState,
      connectionId,
      lastUpdated: new Date(),
      lastAccessed: new Date(),
    };
    
    store.updateConnectionState(connectionId, updatedState);
    
    // 通知监听器
    this.notifyStateChange(store);
  }
  
  // 恢复工作状态
  public restoreWorkflowState(connectionId: string): WorkflowState | null {
    const store = useWorkflowStateStore.getState();
    const state = store.connectionStates[connectionId];
    
    if (state) {
      // 更新最后访问时间
      store.updateConnectionState(connectionId, {
        ...state,
        lastAccessed: new Date(),
      });
      
      return state;
    }
    
    return null;
  }
  
  // 创建新连接的默认状态
  public createDefaultWorkflowState(connectionId: string): WorkflowState {
    const defaultState: WorkflowState = {
      connectionId,
      currentView: 'connections',
      queryState: {
        selectedIndex: '',
        queryMode: 'visual',
        visualQuery: { conditions: [] },
        dslQuery: '{\n  "query": {\n    "match_all": {}\n  }\n}',
        currentPage: 1,
        pageSize: 20,
      },
      searchState: {
        results: null,
        isSearching: false,
        searchError: null,
        resultCount: 0,
      },
      uiState: {
        sidebarCollapsed: false,
        scrollPosition: {},
        expandedSections: [],
        selectedDocuments: [],
      },
      editorState: {
        openDocuments: [],
      },
      lastUpdated: new Date(),
      lastAccessed: new Date(),
    };
    
    const store = useWorkflowStateStore.getState();
    store.updateConnectionState(connectionId, defaultState);
    
    return defaultState;
  }
  
  // 切换活动连接
  public switchActiveConnection(connectionId: string) {
    const store = useWorkflowStateStore.getState();
    
    // 保存当前连接的状态
    if (store.activeConnectionId) {
      this.saveCurrentWorkflowState(store.activeConnectionId, {
        lastAccessed: new Date(),
      });
    }
    
    // 切换到新连接
    store.setActiveConnection(connectionId);
    
    // 如果新连接没有状态，创建默认状态
    if (!store.connectionStates[connectionId]) {
      this.createDefaultWorkflowState(connectionId);
    }
    
    // 恢复新连接的状态
    return this.restoreWorkflowState(connectionId);
  }
  
  // 清理连接状态
  public cleanupConnectionState(connectionId: string) {
    const store = useWorkflowStateStore.getState();
    store.removeConnectionState(connectionId);
  }
  
  // 获取所有连接的状态摘要
  public getConnectionStatesSummary() {
    const store = useWorkflowStateStore.getState();
    const summary: Array<{
      connectionId: string;
      lastAccessed: Date;
      currentView: NavigationView;
      selectedIndex: string;
      hasUnsavedChanges: boolean;
    }> = [];
    
    Object.entries(store.connectionStates).forEach(([connectionId, state]) => {
      summary.push({
        connectionId,
        lastAccessed: state.lastAccessed,
        currentView: state.currentView,
        selectedIndex: state.queryState.selectedIndex,
        hasUnsavedChanges: state.editorState.openDocuments.some(doc => doc.isDirty),
      });
    });
    
    return summary.sort((a, b) => b.lastAccessed.getTime() - a.lastAccessed.getTime());
  }
  
  // 导出状态数据
  public exportWorkflowStates(): string {
    const store = useWorkflowStateStore.getState();
    const exportData = {
      version: '1.0',
      exportedAt: new Date().toISOString(),
      globalPreferences: store.globalPreferences,
      connectionStates: store.connectionStates,
    };
    
    return JSON.stringify(exportData, null, 2);
  }
  
  // 导入状态数据
  public importWorkflowStates(data: string): { success: boolean; error?: string } {
    try {
      const importData = JSON.parse(data);
      const store = useWorkflowStateStore.getState();
      
      // 验证数据格式
      if (!importData.version || !importData.connectionStates) {
        return { success: false, error: '无效的状态数据格式' };
      }
      
      // 导入全局偏好
      if (importData.globalPreferences) {
        store.updateGlobalPreferences(importData.globalPreferences);
      }
      
      // 导入连接状态
      Object.entries(importData.connectionStates).forEach(([connectionId, state]) => {
        store.updateConnectionState(connectionId, state as WorkflowState);
      });
      
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: `导入失败: ${error instanceof Error ? error.message : '未知错误'}` 
      };
    }
  }
  
  // 添加状态变化监听器
  public addStateChangeListener(listener: (state: MultiConnectionWorkflowState) => void) {
    this.stateChangeListeners.push(listener);
  }
  
  // 移除状态变化监听器
  public removeStateChangeListener(listener: (state: MultiConnectionWorkflowState) => void) {
    const index = this.stateChangeListeners.indexOf(listener);
    if (index > -1) {
      this.stateChangeListeners.splice(index, 1);
    }
  }
  
  // 通知状态变化
  private notifyStateChange(state: MultiConnectionWorkflowState) {
    this.stateChangeListeners.forEach(listener => {
      try {
        listener(state);
      } catch (error) {
        console.error('State change listener error:', error);
      }
    });
  }
  
  // 清理资源
  public destroy() {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = undefined;
    }
    this.stateChangeListeners = [];
  }
}

// Zustand store for workflow state management
export const useWorkflowStateStore = create<MultiConnectionWorkflowState & {
  // Actions
  setActiveConnection: (connectionId: string) => void;
  updateConnectionState: (connectionId: string, state: Partial<WorkflowState>) => void;
  removeConnectionState: (connectionId: string) => void;
  updateGlobalPreferences: (preferences: Partial<MultiConnectionWorkflowState['globalPreferences']>) => void;
  updateAppState: (appState: Partial<MultiConnectionWorkflowState['appState']>) => void;
}>()(
  persist(
    subscribeWithSelector((set, get) => ({
      // Initial state
      activeConnectionId: undefined,
      connectionStates: {},
      globalPreferences: {
        theme: 'auto',
        language: 'zh-CN',
        autoSave: true,
        autoSaveInterval: 30, // 30 seconds
      },
      appState: {
        windowSize: { width: 1200, height: 800 },
        windowPosition: { x: 100, y: 100 },
        isMaximized: false,
        lastOpenedConnections: [],
      },
      
      // Actions
      setActiveConnection: (connectionId: string) => {
        set(state => ({
          activeConnectionId: connectionId,
          appState: {
            ...state.appState,
            lastOpenedConnections: [
              connectionId,
              ...state.appState.lastOpenedConnections.filter(id => id !== connectionId)
            ].slice(0, 5), // Keep only last 5 connections
          },
        }));
      },
      
      updateConnectionState: (connectionId: string, stateUpdate: Partial<WorkflowState>) => {
        set(state => ({
          connectionStates: {
            ...state.connectionStates,
            [connectionId]: {
              ...state.connectionStates[connectionId],
              ...stateUpdate,
              connectionId,
              lastUpdated: new Date(),
            } as WorkflowState,
          },
        }));
      },
      
      removeConnectionState: (connectionId: string) => {
        set(state => {
          const newConnectionStates = { ...state.connectionStates };
          delete newConnectionStates[connectionId];
          
          return {
            connectionStates: newConnectionStates,
            activeConnectionId: state.activeConnectionId === connectionId 
              ? undefined 
              : state.activeConnectionId,
            appState: {
              ...state.appState,
              lastOpenedConnections: state.appState.lastOpenedConnections.filter(
                id => id !== connectionId
              ),
            },
          };
        });
      },
      
      updateGlobalPreferences: (preferences) => {
        set(state => ({
          globalPreferences: {
            ...state.globalPreferences,
            ...preferences,
          },
        }));
      },
      
      updateAppState: (appState) => {
        set(state => ({
          appState: {
            ...state.appState,
            ...appState,
          },
        }));
      },
    })),
    {
      name: 'workflow-state-storage',
      // 只持久化必要的数据
      partialize: (state) => ({
        activeConnectionId: state.activeConnectionId,
        connectionStates: state.connectionStates,
        globalPreferences: state.globalPreferences,
        appState: state.appState,
      }),
    }
  )
);

// 导出单例实例
export const workflowStateManager = WorkflowStateManager.getInstance();

// 工具函数：获取当前活动连接的状态
export const getCurrentWorkflowState = (): WorkflowState | null => {
  const store = useWorkflowStateStore.getState();
  const { activeConnectionId, connectionStates } = store;
  
  if (!activeConnectionId || !connectionStates[activeConnectionId]) {
    return null;
  }
  
  return connectionStates[activeConnectionId];
};

// 工具函数：更新当前活动连接的状态
export const updateCurrentWorkflowState = (stateUpdate: Partial<WorkflowState>) => {
  const store = useWorkflowStateStore.getState();
  const { activeConnectionId } = store;
  
  if (activeConnectionId) {
    store.updateConnectionState(activeConnectionId, stateUpdate);
  }
};