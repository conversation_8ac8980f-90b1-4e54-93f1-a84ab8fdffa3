"use strict";
(global["webpackChunkes_client"] = global["webpackChunkes_client"] || []).push([["src_renderer_components_Index_IndexList_tsx"],{

/***/ "./node_modules/lucide-react/dist/esm/icons/arrow-down-wide-narrow.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/arrow-down-wide-narrow.mjs ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ ArrowDownWideNarrow)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const ArrowDownWideNarrow = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("ArrowDownWideNarrow", [
  ["path", { d: "m3 16 4 4 4-4", key: "1co6wj" }],
  ["path", { d: "M7 20V4", key: "1yoxec" }],
  ["path", { d: "M11 4h10", key: "1w87gc" }],
  ["path", { d: "M11 8h7", key: "djye34" }],
  ["path", { d: "M11 12h4", key: "q8tih4" }]
]);


//# sourceMappingURL=arrow-down-wide-narrow.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ ArrowUpNarrowWide)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const ArrowUpNarrowWide = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("ArrowUpNarrowWide", [
  ["path", { d: "m3 8 4-4 4 4", key: "11wl7u" }],
  ["path", { d: "M7 4v16", key: "1glfcx" }],
  ["path", { d: "M11 12h4", key: "q8tih4" }],
  ["path", { d: "M11 16h7", key: "uosisv" }],
  ["path", { d: "M11 20h10", key: "jvxblo" }]
]);


//# sourceMappingURL=arrow-up-narrow-wide.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/chevrons-up-down.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevrons-up-down.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ ChevronsUpDown)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const ChevronsUpDown = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("ChevronsUpDown", [
  ["path", { d: "m7 15 5 5 5-5", key: "1hf1tw" }],
  ["path", { d: "m7 9 5-5 5 5", key: "sgt6xg" }]
]);


//# sourceMappingURL=chevrons-up-down.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/hard-drive.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/hard-drive.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ HardDrive)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const HardDrive = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("HardDrive", [
  ["line", { x1: "22", x2: "2", y1: "12", y2: "12", key: "1y58io" }],
  [
    "path",
    {
      d: "M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",
      key: "oot6mr"
    }
  ],
  ["line", { x1: "6", x2: "6.01", y1: "16", y2: "16", key: "sgf278" }],
  ["line", { x1: "10", x2: "10.01", y1: "16", y2: "16", key: "1l4acy" }]
]);


//# sourceMappingURL=hard-drive.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/pen-square.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/pen-square.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ PenSquare)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const PenSquare = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("PenSquare", [
  [
    "path",
    {
      d: "M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",
      key: "1qinfi"
    }
  ],
  [
    "path",
    { d: "M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z", key: "w2jsv5" }
  ]
]);


//# sourceMappingURL=pen-square.mjs.map


/***/ }),

/***/ "./src/renderer/components/Index/IndexDetails.tsx":
/*!********************************************************!*\
  !*** ./src/renderer/components/Index/IndexDetails.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   IndexDetails: () => (/* binding */ IndexDetails)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _UI_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../UI/Button */ "./src/renderer/components/UI/Button.tsx");
/* harmony import */ var _UI_Modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../UI/Modal */ "./src/renderer/components/UI/Modal.tsx");
/* harmony import */ var _services_elasticsearch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../services/elasticsearch */ "./src/renderer/services/elasticsearch.ts");
/* harmony import */ var _hooks_useToast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../hooks/useToast */ "./src/renderer/hooks/useToast.ts");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/file-text.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/pen-square.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/save.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/settings.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/x.mjs");







const IndexDetails = ({ indexName, isOpen, onClose, }) => {
    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('mappings');
    const [mappings, setMappings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [editingMappings, setEditingMappings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [editingSettings, setEditingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [mappingsJson, setMappingsJson] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [settingsJson, setSettingsJson] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const { success: showSuccess, error: showError } = (0,_hooks_useToast__WEBPACK_IMPORTED_MODULE_5__.useToast)();
    const esService = _services_elasticsearch__WEBPACK_IMPORTED_MODULE_4__.ElasticsearchService.getInstance();
    const loadIndexData = async () => {
        setLoading(true);
        try {
            const [mappingData, settingData] = await Promise.all([
                esService.getIndexMapping(indexName),
                esService.getIndexSettings(indexName),
            ]);
            setMappings(mappingData);
            setSettings(settingData);
            setMappingsJson(JSON.stringify(mappingData[indexName]?.mappings || {}, null, 2));
            setSettingsJson(JSON.stringify(settingData[indexName]?.settings || {}, null, 2));
        }
        catch (error) {
            showError('加载索引详情失败', error.message || '无法获取索引信息');
        }
        finally {
            setLoading(false);
        }
    };
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
        if (isOpen && indexName) {
            loadIndexData();
        }
    }, [isOpen, indexName]);
    const handleSaveMappings = async () => {
        try {
            const parsedMappings = JSON.parse(mappingsJson);
            await esService.updateIndexMapping(indexName, parsedMappings);
            showSuccess('映射更新成功', '索引映射已成功更新');
            setEditingMappings(false);
            loadIndexData();
        }
        catch (error) {
            if (error instanceof SyntaxError) {
                showError('JSON 格式错误', '请检查映射配置的 JSON 格式');
            }
            else {
                showError('映射更新失败', error.message || '无法更新索引映射');
            }
        }
    };
    const handleSaveSettings = async () => {
        try {
            const parsedSettings = JSON.parse(settingsJson);
            await esService.updateIndexSettings(indexName, parsedSettings);
            showSuccess('设置更新成功', '索引设置已成功更新');
            setEditingSettings(false);
            loadIndexData();
        }
        catch (error) {
            if (error instanceof SyntaxError) {
                showError('JSON 格式错误', '请检查设置配置的 JSON 格式');
            }
            else {
                showError('设置更新失败', error.message || '无法更新索引设置');
            }
        }
    };
    const handleCancelEdit = (type) => {
        if (type === 'mappings') {
            setEditingMappings(false);
            setMappingsJson(JSON.stringify(mappings[indexName]?.mappings || {}, null, 2));
        }
        else {
            setEditingSettings(false);
            setSettingsJson(JSON.stringify(settings[indexName]?.settings || {}, null, 2));
        }
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Modal__WEBPACK_IMPORTED_MODULE_3__.Modal, { isOpen: isOpen, onClose: onClose, title: `索引详情 - ${indexName}`, size: "lg", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4 -m-6 p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex border-b border-neutral-200 dark:border-neutral-700", children: [
                        { key: 'mappings', label: '字段映射', icon: lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"] },
                        { key: 'settings', label: '索引设置', icon: lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"] },
                    ].map(tab => {
                        const Icon = tab.icon;
                        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", { onClick: () => setActiveTab(tab.key), className: `flex items-center space-x-2 px-4 py-2 text-sm font-medium border-b-2 transition-colors ${activeTab === tab.key
                                ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                                : 'border-transparent text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-300'}`, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Icon, { className: "h-4 w-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: tab.label })] }, tab.key));
                    }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "min-h-96", children: [activeTab === 'mappings' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-between items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium text-neutral-900 dark:text-dark-text-primary", children: "\u5B57\u6BB5\u6620\u5C04\u914D\u7F6E" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex space-x-2", children: editingMappings ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_2__.Button, { variant: "secondary", size: "sm", onClick: () => handleCancelEdit('mappings'), className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_10__["default"], { className: "h-4 w-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u53D6\u6D88" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_2__.Button, { variant: "primary", size: "sm", onClick: handleSaveMappings, className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_8__["default"], { className: "h-4 w-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u4FDD\u5B58" })] })] })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_2__.Button, { variant: "secondary", size: "sm", onClick: () => setEditingMappings(true), className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"], { className: "h-4 w-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u7F16\u8F91" })] })) })] }), loading ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center justify-center h-64", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-neutral-500", children: "\u52A0\u8F7D\u4E2D..." }) })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "border border-neutral-300 dark:border-neutral-600 rounded-md", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("textarea", { value: mappingsJson, onChange: e => setMappingsJson(e.target.value), readOnly: !editingMappings, className: `w-full h-64 px-3 py-2 text-sm font-mono resize-none ${editingMappings
                                            ? 'bg-white dark:bg-dark-secondary border-0 focus:outline-none focus:ring-2 focus:ring-primary-500'
                                            : 'bg-neutral-50 dark:bg-dark-tertiary border-0 cursor-default'}` }) }))] })), activeTab === 'settings' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-between items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium text-neutral-900 dark:text-dark-text-primary", children: "\u7D22\u5F15\u8BBE\u7F6E\u914D\u7F6E" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex space-x-2", children: editingSettings ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_2__.Button, { variant: "secondary", size: "sm", onClick: () => handleCancelEdit('settings'), className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_10__["default"], { className: "h-4 w-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u53D6\u6D88" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_2__.Button, { variant: "primary", size: "sm", onClick: handleSaveSettings, className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_8__["default"], { className: "h-4 w-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u4FDD\u5B58" })] })] })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_2__.Button, { variant: "secondary", size: "sm", onClick: () => setEditingSettings(true), className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"], { className: "h-4 w-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u7F16\u8F91" })] })) })] }), loading ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center justify-center h-64", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-neutral-500", children: "\u52A0\u8F7D\u4E2D..." }) })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "border border-neutral-300 dark:border-neutral-600 rounded-md", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("textarea", { value: settingsJson, onChange: e => setSettingsJson(e.target.value), readOnly: !editingSettings, className: `w-full h-64 px-3 py-2 text-sm font-mono resize-none ${editingSettings
                                            ? 'bg-white dark:bg-dark-secondary border-0 focus:outline-none focus:ring-2 focus:ring-primary-500'
                                            : 'bg-neutral-50 dark:bg-dark-tertiary border-0 cursor-default'}` }) }))] }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex justify-end pt-4 border-t border-neutral-200 dark:border-neutral-700", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_2__.Button, { variant: "secondary", onClick: onClose, children: "\u5173\u95ED" }) })] }) }));
};


/***/ }),

/***/ "./src/renderer/components/Index/IndexList.tsx":
/*!*****************************************************!*\
  !*** ./src/renderer/components/Index/IndexList.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   IndexList: () => (/* binding */ IndexList)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _UI_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../UI/Card */ "./src/renderer/components/UI/Card.tsx");
/* harmony import */ var _UI_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../UI/Button */ "./src/renderer/components/UI/Button.tsx");
/* harmony import */ var _UI_HealthIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../UI/HealthIndicator */ "./src/renderer/components/UI/HealthIndicator.tsx");
/* harmony import */ var _UI_Spinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../UI/Spinner */ "./src/renderer/components/UI/Spinner.tsx");
/* harmony import */ var _UI_Badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../UI/Badge */ "./src/renderer/components/UI/Badge.tsx");
/* harmony import */ var _services_elasticsearch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../services/elasticsearch */ "./src/renderer/services/elasticsearch.ts");
/* harmony import */ var _stores_connection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../stores/connection */ "./src/renderer/stores/connection.ts");
/* harmony import */ var _hooks_useToast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../hooks/useToast */ "./src/renderer/hooks/useToast.ts");
/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../utils/cn */ "./src/renderer/utils/cn.ts");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/chevron-down.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/chevron-up.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/chevrons-up-down.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/database.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/eye.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/file-text.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/hard-drive.mjs");
/* harmony import */ var _IndexOperations__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./IndexOperations */ "./src/renderer/components/Index/IndexOperations.tsx");
/* harmony import */ var _IndexDetails__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./IndexDetails */ "./src/renderer/components/Index/IndexDetails.tsx");
/* harmony import */ var _IndexManagementEnhancements__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./IndexManagementEnhancements */ "./src/renderer/components/Index/IndexManagementEnhancements.tsx");















const IndexList = () => {
    const [indices, setIndices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [healthFilter, setHealthFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');
    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');
    const [showSystemIndices, setShowSystemIndices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [showIndexDetails, setShowIndexDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [sortField, setSortField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [sortDirection, setSortDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('desc');
    const { getActiveConnection } = (0,_stores_connection__WEBPACK_IMPORTED_MODULE_8__.useConnectionStore)();
    const activeConnection = getActiveConnection();
    const { error: showError, success } = (0,_hooks_useToast__WEBPACK_IMPORTED_MODULE_9__.useToast)();
    // 临时直接使用浏览器服务
    const esService = _services_elasticsearch__WEBPACK_IMPORTED_MODULE_7__.ElasticsearchService.getInstance().browserService || _services_elasticsearch__WEBPACK_IMPORTED_MODULE_7__.ElasticsearchService.getInstance();
    console.log('Using ES service:', esService.constructor.name);
    // 将parseSize函数移到这里，避免初始化问题
    const parseSize = (size) => {
        if (!size || size === '0b')
            return 0;
        const units = {
            'b': 1,
            'kb': 1024,
            'mb': 1024 * 1024,
            'gb': 1024 * 1024 * 1024,
            'tb': 1024 * 1024 * 1024 * 1024
        };
        const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*([a-z]+)$/);
        if (!match)
            return 0;
        const value = parseFloat(match[1]);
        const unit = match[2];
        return value * (units[unit] || 1);
    };
    const loadIndices = async (forceRefresh = false) => {
        console.log('🔍 loadIndices called, forceRefresh:', forceRefresh);
        console.log('activeConnection:', activeConnection);
        console.log('esService.isConnected():', esService.isConnected());
        if (!activeConnection) {
            console.log('❌ Cannot load indices - no active connection');
            return;
        }
        // 如果服务未连接，先尝试连接
        if (!esService.isConnected()) {
            console.log('🔌 Service not connected, trying to connect...');
            try {
                await esService.connect(activeConnection);
                console.log('✅ Service connected successfully');
            }
            catch (error) {
                console.error('❌ Failed to connect service:', error);
                return;
            }
        }
        setLoading(true);
        try {
            console.log('📡 Calling esService.listIndices()...');
            const indexList = await esService.listIndices(forceRefresh);
            console.log('✅ Received indices:', indexList);
            setIndices(indexList);
            if (forceRefresh) {
                success(`索引列表已刷新，成功加载 ${indexList.length} 个索引`);
            }
        }
        catch (error) {
            console.error('❌ Error loading indices:', error);
            showError('加载索引失败', error.message || '无法获取索引列表');
        }
        finally {
            setLoading(false);
        }
    };
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
        console.log('🔄 useEffect triggered for activeConnection change');
        loadIndices();
    }, [activeConnection]);
    const filteredIndices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {
        let filtered = indices.filter(index => {
            // 过滤掉系统索引（除非明确显示）
            if (!showSystemIndices && index.name.startsWith('.')) {
                return false;
            }
            const matchesSearch = index.name
                .toLowerCase()
                .includes(searchTerm.toLowerCase());
            const matchesHealth = healthFilter === 'all' || index.health === healthFilter;
            const matchesStatus = statusFilter === 'all' || index.status === statusFilter;
            return matchesSearch && matchesHealth && matchesStatus;
        });
        // 应用排序
        if (sortField) {
            filtered.sort((a, b) => {
                let aValue, bValue;
                switch (sortField) {
                    case 'storeSize':
                        aValue = parseSize(a.storeSize);
                        bValue = parseSize(b.storeSize);
                        break;
                    case 'docsCount':
                        aValue = a.docsCount || 0;
                        bValue = b.docsCount || 0;
                        break;
                    case 'name':
                        aValue = a.name.toLowerCase();
                        bValue = b.name.toLowerCase();
                        break;
                    case 'health':
                        const healthOrder = { 'green': 3, 'yellow': 2, 'red': 1 };
                        aValue = healthOrder[a.health] || 0;
                        bValue = healthOrder[b.health] || 0;
                        break;
                    case 'primaryShards':
                        aValue = a.primaryShards || 0;
                        bValue = b.primaryShards || 0;
                        break;
                    case 'replicaShards':
                        aValue = a.replicaShards || 0;
                        bValue = b.replicaShards || 0;
                        break;
                    default:
                        return 0;
                }
                if (aValue < bValue)
                    return sortDirection === 'asc' ? -1 : 1;
                if (aValue > bValue)
                    return sortDirection === 'asc' ? 1 : -1;
                return 0;
            });
        }
        return filtered;
    }, [indices, searchTerm, healthFilter, statusFilter, showSystemIndices, sortField, sortDirection]);
    const formatSize = (size) => {
        if (!size || size === '0b')
            return '0 B';
        return size;
    };
    const formatCount = (count) => {
        if (count >= 1000000) {
            return `${(count / 1000000).toFixed(1)}M`;
        }
        else if (count >= 1000) {
            return `${(count / 1000).toFixed(1)}K`;
        }
        return count.toString();
    };
    // parseSize函数已移到组件顶部
    const handleSort = (field) => {
        if (sortField === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        }
        else {
            setSortField(field);
            setSortDirection('desc');
        }
    };
    const getHealthStats = () => {
        const stats = indices.reduce((acc, index) => {
            acc[index.health] = (acc[index.health] || 0) + 1;
            return acc;
        }, {});
        return {
            green: stats.green || 0,
            yellow: stats.yellow || 0,
            red: stats.red || 0,
            total: indices.length,
        };
    };
    const healthStats = getHealthStats();
    if (!activeConnection) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.Card, { className: "h-full", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, { className: "flex items-center justify-center h-full", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_14__["default"], { className: "h-12 w-12 text-neutral-400 mx-auto mb-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-neutral-500 dark:text-dark-text-secondary", children: "\u8BF7\u5148\u8FDE\u63A5\u5230 Elasticsearch \u96C6\u7FA4" })] }) }) }));
    }
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "h-full flex flex-col space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_IndexManagementEnhancements__WEBPACK_IMPORTED_MODULE_20__.IndexManagementEnhancements, { indices: indices, searchTerm: searchTerm, onSearchChange: setSearchTerm, healthFilter: healthFilter, onHealthFilterChange: setHealthFilter, statusFilter: statusFilter, onStatusFilterChange: setStatusFilter, showSystemIndices: showSystemIndices, onToggleSystemIndices: setShowSystemIndices, sortField: sortField, sortDirection: sortDirection, onSort: handleSort, onRefresh: () => loadIndices(true), isLoading: loading }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.Card, { className: "p-4", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium text-gray-900 dark:text-white", children: "\u7D22\u5F15\u64CD\u4F5C" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_IndexOperations__WEBPACK_IMPORTED_MODULE_18__.IndexOperations, { onIndexCreated: () => loadIndices(true), showCreateButton: true })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.Card, { className: "flex-1", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, { className: "p-0", children: loading ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center justify-center h-96", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Spinner__WEBPACK_IMPORTED_MODULE_5__.Spinner, { size: "lg" }) })) : filteredIndices.length === 0 ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center justify-center h-96", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_16__["default"], { className: "h-16 w-16 text-neutral-400 mx-auto mb-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg text-neutral-500 dark:text-dark-text-secondary mb-4", children: searchTerm ||
                                        healthFilter !== 'all' ||
                                        statusFilter !== 'all'
                                        ? '没有找到匹配的索引'
                                        : '暂无索引' }), (searchTerm || healthFilter !== 'all' || statusFilter !== 'all') && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-400 dark:text-neutral-500", children: "\u5C1D\u8BD5\u8C03\u6574\u641C\u7D22\u6761\u4EF6\u6216\u8FC7\u6EE4\u5668" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "outline", size: "sm", onClick: () => {
                                                setSearchTerm('');
                                                setHealthFilter('all');
                                                setStatusFilter('all');
                                            }, children: "\u6E05\u9664\u6240\u6709\u8FC7\u6EE4\u5668" })] })), !searchTerm && healthFilter === 'all' && statusFilter === 'all' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-400 dark:text-neutral-500", children: "\u96C6\u7FA4\u4E2D\u6CA1\u6709\u7D22\u5F15\uFF0C\u6216\u8005\u7D22\u5F15\u52A0\u8F7D\u5931\u8D25" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "outline", size: "sm", onClick: () => loadIndices(true), loading: loading, children: "\u91CD\u65B0\u52A0\u8F7D" })] }))] }) })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "overflow-hidden", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "md:hidden space-y-3 p-4", children: filteredIndices.map((index) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.Card, { className: "p-4 hover:shadow-md transition-shadow", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between mb-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2 min-w-0 flex-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_14__["default"], { className: "h-4 w-4 text-neutral-400 flex-shrink-0" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "font-medium text-neutral-900 dark:text-dark-text-primary truncate", children: index.name })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_HealthIndicator__WEBPACK_IMPORTED_MODULE_4__.HealthIndicator, { status: index.health, size: "sm" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-2 text-sm mb-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-neutral-500 dark:text-neutral-400", children: "\u72B6\u6001:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Badge__WEBPACK_IMPORTED_MODULE_6__.Badge, { variant: index.status === 'open' ? 'success' : 'secondary', size: "sm", children: index.status === 'open' ? '开启' : '关闭' })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-neutral-500 dark:text-neutral-400", children: "\u6587\u6863\u6570:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-neutral-700 dark:text-neutral-300 font-medium", children: formatCount(index.docsCount) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-neutral-500 dark:text-neutral-400", children: "\u5B58\u50A8\u5927\u5C0F:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-neutral-700 dark:text-neutral-300 font-mono text-xs", children: formatSize(index.storeSize) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-neutral-500 dark:text-neutral-400", children: "\u5206\u7247\u914D\u7F6E:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "text-neutral-700 dark:text-neutral-300", children: [index.primaryShards || 0, " \u4E3B / ", index.replicaShards || 0, " \u526F\u672C"] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-end space-x-2 pt-2 border-t border-neutral-100 dark:border-neutral-700", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "ghost", size: "sm", onClick: () => {
                                                        setSelectedIndex(index.name);
                                                        setShowIndexDetails(true);
                                                    }, className: "flex items-center space-x-1 px-3 py-1.5 text-xs", title: "\u67E5\u770B\u8BE6\u60C5", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_15__["default"], { className: "h-3 w-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u8BE6\u60C5" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_IndexOperations__WEBPACK_IMPORTED_MODULE_18__.IndexOperations, { indexName: index.name, onIndexDeleted: loadIndices, showCreateButton: false })] })] }, index.name))) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "hidden md:block overflow-hidden", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-neutral-50 dark:bg-dark-tertiary border-b border-neutral-200 dark:border-neutral-700 sticky top-0 z-10", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-12 gap-3 px-4 py-3 text-sm font-medium text-neutral-700 dark:text-dark-text-primary", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "col-span-3 flex items-center cursor-pointer hover:text-neutral-900 dark:hover:text-dark-text-primary", onClick: () => handleSort('name'), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_14__["default"], { className: "h-4 w-4 mr-2" }), "\u7D22\u5F15\u540D\u79F0", (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "ml-1", children: sortField === 'name' ? (sortDirection === 'asc' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_12__["default"], { className: "h-4 w-4" })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_11__["default"], { className: "h-4 w-4" }))) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_13__["default"], { className: "h-4 w-4 text-neutral-400" })) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "col-span-1 flex items-center cursor-pointer hover:text-neutral-900 dark:hover:text-dark-text-primary", onClick: () => handleSort('health'), children: ["\u5065\u5EB7\u72B6\u6001", (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "ml-1", children: sortField === 'health' ? (sortDirection === 'asc' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_12__["default"], { className: "h-4 w-4" })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_11__["default"], { className: "h-4 w-4" }))) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_13__["default"], { className: "h-4 w-4 text-neutral-400" })) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "col-span-1 flex items-center", children: "\u72B6\u6001" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "col-span-2 flex items-center cursor-pointer hover:text-neutral-900 dark:hover:text-dark-text-primary", onClick: () => handleSort('docsCount'), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_16__["default"], { className: "h-4 w-4 mr-1" }), "\u6587\u6863\u6570", (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "ml-1", children: sortField === 'docsCount' ? (sortDirection === 'asc' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_12__["default"], { className: "h-4 w-4" })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_11__["default"], { className: "h-4 w-4" }))) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_13__["default"], { className: "h-4 w-4 text-neutral-400" })) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "col-span-2 flex items-center cursor-pointer hover:text-neutral-900 dark:hover:text-dark-text-primary", onClick: () => handleSort('storeSize'), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_17__["default"], { className: "h-4 w-4 mr-1" }), "\u5B58\u50A8\u5927\u5C0F", (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "ml-1", children: sortField === 'storeSize' ? (sortDirection === 'asc' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_12__["default"], { className: "h-4 w-4" })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_11__["default"], { className: "h-4 w-4" }))) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_13__["default"], { className: "h-4 w-4 text-neutral-400" })) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "col-span-1 flex items-center cursor-pointer hover:text-neutral-900 dark:hover:text-dark-text-primary text-center", onClick: () => handleSort('primaryShards'), children: ["\u4E3B\u5206\u7247", (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "ml-1", children: sortField === 'primaryShards' ? (sortDirection === 'asc' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_12__["default"], { className: "h-4 w-4" })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_11__["default"], { className: "h-4 w-4" }))) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_13__["default"], { className: "h-4 w-4 text-neutral-400" })) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "col-span-1 flex items-center cursor-pointer hover:text-neutral-900 dark:hover:text-dark-text-primary text-center", onClick: () => handleSort('replicaShards'), children: ["\u526F\u672C", (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "ml-1", children: sortField === 'replicaShards' ? (sortDirection === 'asc' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_12__["default"], { className: "h-4 w-4" })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_11__["default"], { className: "h-4 w-4" }))) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_13__["default"], { className: "h-4 w-4 text-neutral-400" })) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "col-span-1 flex items-center justify-center", children: "\u64CD\u4F5C" })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "divide-y divide-neutral-200 dark:divide-neutral-700 overflow-y-auto", style: { maxHeight: 'calc(100vh - 400px)' }, children: filteredIndices.map((index, i) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_10__.cn)('px-4 py-3 hover:bg-neutral-50 dark:hover:bg-dark-tertiary transition-colors cursor-pointer', i % 2 === 0
                                                ? 'bg-white dark:bg-dark-secondary'
                                                : 'bg-neutral-50/50 dark:bg-dark-tertiary/50'), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-12 gap-3 items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "col-span-3", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2 min-w-0", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_14__["default"], { className: "h-4 w-4 text-neutral-400 flex-shrink-0" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "font-medium text-neutral-900 dark:text-dark-text-primary truncate", title: index.name, children: index.name })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "col-span-1", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_HealthIndicator__WEBPACK_IMPORTED_MODULE_4__.HealthIndicator, { status: index.health, size: "sm" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "col-span-1", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Badge__WEBPACK_IMPORTED_MODULE_6__.Badge, { variant: index.status === 'open' ? 'success' : 'secondary', size: "sm", children: index.status === 'open' ? '开启' : '关闭' }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "col-span-2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_16__["default"], { className: "h-4 w-4 text-neutral-400 flex-shrink-0" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-neutral-600 dark:text-dark-text-secondary", children: formatCount(index.docsCount) })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "col-span-2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_17__["default"], { className: "h-4 w-4 text-neutral-400 flex-shrink-0" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-neutral-600 dark:text-dark-text-secondary font-mono", children: formatSize(index.storeSize) })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "col-span-1", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-neutral-600 dark:text-dark-text-secondary text-center", children: index.primaryShards || 0 }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "col-span-1", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-neutral-600 dark:text-dark-text-secondary text-center", children: index.replicaShards || 0 }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "col-span-1", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "ghost", size: "sm", onClick: () => {
                                                                        setSelectedIndex(index.name);
                                                                        setShowIndexDetails(true);
                                                                    }, className: "p-1.5 h-7 w-7 hover:bg-gray-50 hover:text-gray-600 dark:hover:bg-gray-900/20 dark:hover:text-gray-400 transition-colors", title: "\u67E5\u770B\u8BE6\u60C5", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_15__["default"], { className: "h-3 w-3" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_IndexOperations__WEBPACK_IMPORTED_MODULE_18__.IndexOperations, { indexName: index.name, onIndexDeleted: loadIndices, showCreateButton: false })] }) })] }) }, index.name))) })] })] })) }) }), selectedIndex && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_IndexDetails__WEBPACK_IMPORTED_MODULE_19__.IndexDetails, { indexName: selectedIndex, isOpen: showIndexDetails, onClose: () => {
                    setShowIndexDetails(false);
                    setSelectedIndex(null);
                } }))] }));
};


/***/ }),

/***/ "./src/renderer/components/Index/IndexManagementEnhancements.tsx":
/*!***********************************************************************!*\
  !*** ./src/renderer/components/Index/IndexManagementEnhancements.tsx ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   IndexManagementEnhancements: () => (/* binding */ IndexManagementEnhancements)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _UI_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../UI/Card */ "./src/renderer/components/UI/Card.tsx");
/* harmony import */ var _UI_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../UI/Button */ "./src/renderer/components/UI/Button.tsx");
/* harmony import */ var _UI_Badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../UI/Badge */ "./src/renderer/components/UI/Badge.tsx");
/* harmony import */ var _UI_Input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../UI/Input */ "./src/renderer/components/UI/Input.tsx");
/* harmony import */ var _UI_Dropdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../UI/Dropdown */ "./src/renderer/components/UI/Dropdown.tsx");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/alert-triangle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/arrow-down-wide-narrow.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/check-circle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/database.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/eye-off.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/eye.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/filter.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/refresh-cw.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/search.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/x-circle.mjs");








const IndexManagementEnhancements = ({ indices, searchTerm, onSearchChange, healthFilter, onHealthFilterChange, statusFilter, onStatusFilterChange, showSystemIndices, onToggleSystemIndices, sortField, sortDirection, onSort, onRefresh, isLoading }) => {
    const [showAdvancedFilters, setShowAdvancedFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    // 计算统计信息
    const stats = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo(() => {
        const total = indices.length;
        const systemIndices = indices.filter(idx => idx.name.startsWith('.')).length;
        const userIndices = total - systemIndices;
        const healthStats = indices.reduce((acc, idx) => {
            acc[idx.health] = (acc[idx.health] || 0) + 1;
            return acc;
        }, {});
        const statusStats = indices.reduce((acc, idx) => {
            acc[idx.status] = (acc[idx.status] || 0) + 1;
            return acc;
        }, {});
        const totalDocs = indices.reduce((sum, idx) => sum + (idx.docsCount || 0), 0);
        return {
            total,
            systemIndices,
            userIndices,
            health: {
                green: healthStats.green || 0,
                yellow: healthStats.yellow || 0,
                red: healthStats.red || 0
            },
            status: {
                open: statusStats.open || 0,
                close: statusStats.close || 0
            },
            totalDocs
        };
    }, [indices]);
    const formatNumber = (num) => {
        if (num >= 1000000)
            return `${(num / 1000000).toFixed(1)}M`;
        if (num >= 1000)
            return `${(num / 1000).toFixed(1)}K`;
        return num.toString();
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.Card, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_11__["default"], { className: "h-5 w-5" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u7D22\u5F15\u6982\u89C8" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "outline", size: "sm", onClick: onRefresh, disabled: isLoading, className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_15__["default"], { className: `h-4 w-4 ${isLoading ? 'animate-spin' : ''}` }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u5237\u65B0" })] })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-2xl font-bold text-gray-900 dark:text-white", children: stats.total }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-gray-500 dark:text-gray-400", children: "\u603B\u7D22\u5F15\u6570" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-2xl font-bold text-blue-600", children: stats.userIndices }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-gray-500 dark:text-gray-400", children: "\u7528\u6237\u7D22\u5F15" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_10__["default"], { className: "h-4 w-4 text-green-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-2xl font-bold text-green-600", children: stats.health.green })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-gray-500 dark:text-gray-400", children: "\u5065\u5EB7" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"], { className: "h-4 w-4 text-yellow-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-2xl font-bold text-yellow-600", children: stats.health.yellow })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-gray-500 dark:text-gray-400", children: "\u8B66\u544A" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_17__["default"], { className: "h-4 w-4 text-red-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-2xl font-bold text-red-600", children: stats.health.red })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-gray-500 dark:text-gray-400", children: "\u9519\u8BEF" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-2xl font-bold text-purple-600", children: formatNumber(stats.totalDocs) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-gray-500 dark:text-gray-400", children: "\u603B\u6587\u6863\u6570" })] })] }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.Card, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, { className: "p-4", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-1 relative", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Input__WEBPACK_IMPORTED_MODULE_5__.Input, { placeholder: "\u641C\u7D22\u7D22\u5F15\u540D\u79F0...", value: searchTerm, onChange: (e) => onSearchChange(e.target.value), leftIcon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_16__["default"], { className: "h-4 w-4" }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "outline", size: "sm", onClick: () => setShowAdvancedFilters(!showAdvancedFilters), className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_14__["default"], { className: "h-4 w-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u8FC7\u6EE4\u5668" }), showAdvancedFilters ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_12__["default"], { className: "h-4 w-4" }) : (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_13__["default"], { className: "h-4 w-4" })] })] }), showAdvancedFilters && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2", children: "\u5065\u5EB7\u72B6\u6001" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Dropdown__WEBPACK_IMPORTED_MODULE_6__.Dropdown, { options: [
                                                    { value: 'all', label: '所有状态' },
                                                    { value: 'green', label: '健康 (绿色)' },
                                                    { value: 'yellow', label: '警告 (黄色)' },
                                                    { value: 'red', label: '错误 (红色)' }
                                                ], value: healthFilter, onChange: (value) => onHealthFilterChange(value) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2", children: "\u7D22\u5F15\u72B6\u6001" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Dropdown__WEBPACK_IMPORTED_MODULE_6__.Dropdown, { options: [
                                                    { value: 'all', label: '所有状态' },
                                                    { value: 'open', label: '开启' },
                                                    { value: 'close', label: '关闭' }
                                                ], value: statusFilter, onChange: (value) => onStatusFilterChange(value) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2", children: "\u663E\u793A\u9009\u9879" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("label", { className: "flex items-center space-x-2 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 cursor-pointer", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "checkbox", checked: showSystemIndices, onChange: (e) => onToggleSystemIndices(e.target.checked), className: "rounded border-gray-300 text-blue-600 focus:ring-blue-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm", children: "\u663E\u793A\u7CFB\u7EDF\u7D22\u5F15" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Badge__WEBPACK_IMPORTED_MODULE_4__.Badge, { variant: "outline", size: "sm", children: stats.systemIndices })] })] })] })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-wrap gap-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: healthFilter === 'green' ? 'default' : 'outline', size: "sm", onClick: () => onHealthFilterChange(healthFilter === 'green' ? 'all' : 'green'), className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_10__["default"], { className: "h-3 w-3 text-green-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u5065\u5EB7" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Badge__WEBPACK_IMPORTED_MODULE_4__.Badge, { variant: "secondary", size: "sm", children: stats.health.green })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: healthFilter === 'yellow' ? 'default' : 'outline', size: "sm", onClick: () => onHealthFilterChange(healthFilter === 'yellow' ? 'all' : 'yellow'), className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"], { className: "h-3 w-3 text-yellow-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u8B66\u544A" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Badge__WEBPACK_IMPORTED_MODULE_4__.Badge, { variant: "secondary", size: "sm", children: stats.health.yellow })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: healthFilter === 'red' ? 'default' : 'outline', size: "sm", onClick: () => onHealthFilterChange(healthFilter === 'red' ? 'all' : 'red'), className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_17__["default"], { className: "h-3 w-3 text-red-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u9519\u8BEF" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Badge__WEBPACK_IMPORTED_MODULE_4__.Badge, { variant: "secondary", size: "sm", children: stats.health.red })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: statusFilter === 'close' ? 'default' : 'outline', size: "sm", onClick: () => onStatusFilterChange(statusFilter === 'close' ? 'all' : 'close'), className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_17__["default"], { className: "h-3 w-3 text-gray-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u5DF2\u5173\u95ED" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Badge__WEBPACK_IMPORTED_MODULE_4__.Badge, { variant: "secondary", size: "sm", children: stats.status.close })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-600 dark:text-gray-400", children: "\u6392\u5E8F:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex space-x-1", children: [
                                            { field: 'name', label: '名称' },
                                            { field: 'health', label: '健康状态' },
                                            { field: 'docsCount', label: '文档数' },
                                            { field: 'storeSize', label: '大小' }
                                        ].map(({ field, label }) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: sortField === field ? 'default' : 'ghost', size: "sm", onClick: () => onSort(field), className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: label }), sortField === field && (sortDirection === 'asc' ?
                                                    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"], { className: "h-3 w-3" }) :
                                                    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_8__["default"], { className: "h-3 w-3" }))] }, field))) })] })] }) }) }), (searchTerm || healthFilter !== 'all' || statusFilter !== 'all' || showSystemIndices) && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.Card, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, { className: "p-3", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2 flex-wrap", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-600 dark:text-gray-400", children: "\u6D3B\u52A8\u8FC7\u6EE4\u5668:" }), searchTerm && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Badge__WEBPACK_IMPORTED_MODULE_4__.Badge, { variant: "outline", className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_16__["default"], { className: "h-3 w-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { children: ["\u641C\u7D22: ", searchTerm] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => onSearchChange(''), className: "ml-1 hover:text-red-500", children: "\u00D7" })] })), healthFilter !== 'all' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Badge__WEBPACK_IMPORTED_MODULE_4__.Badge, { variant: "outline", className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { children: ["\u5065\u5EB7: ", healthFilter] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => onHealthFilterChange('all'), className: "ml-1 hover:text-red-500", children: "\u00D7" })] })), statusFilter !== 'all' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Badge__WEBPACK_IMPORTED_MODULE_4__.Badge, { variant: "outline", className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { children: ["\u72B6\u6001: ", statusFilter] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => onStatusFilterChange('all'), className: "ml-1 hover:text-red-500", children: "\u00D7" })] })), showSystemIndices && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Badge__WEBPACK_IMPORTED_MODULE_4__.Badge, { variant: "outline", className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u663E\u793A\u7CFB\u7EDF\u7D22\u5F15" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => onToggleSystemIndices(false), className: "ml-1 hover:text-red-500", children: "\u00D7" })] }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "ghost", size: "sm", onClick: () => {
                                    onSearchChange('');
                                    onHealthFilterChange('all');
                                    onStatusFilterChange('all');
                                    onToggleSystemIndices(false);
                                }, className: "text-xs", children: "\u6E05\u9664\u6240\u6709" })] }) }) }))] }));
};


/***/ }),

/***/ "./src/renderer/components/Index/IndexOperations.tsx":
/*!***********************************************************!*\
  !*** ./src/renderer/components/Index/IndexOperations.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   IndexOperations: () => (/* binding */ IndexOperations)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _UI_Modal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../UI/Modal */ "./src/renderer/components/UI/Modal.tsx");
/* harmony import */ var _UI_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../UI/Button */ "./src/renderer/components/UI/Button.tsx");
/* harmony import */ var _UI_Input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../UI/Input */ "./src/renderer/components/UI/Input.tsx");
/* harmony import */ var _services_elasticsearch__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../services/elasticsearch */ "./src/renderer/services/elasticsearch.ts");
/* harmony import */ var _hooks_useToast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../hooks/useToast */ "./src/renderer/hooks/useToast.ts");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/alert-triangle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/pen-square.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/plus.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/trash-2.mjs");








const IndexOperations = ({ indexName, onIndexCreated, onIndexDeleted, onIndexUpdated, showCreateButton = true, }) => {
    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [showDeleteModal, setShowDeleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const { success: showSuccess, error: showError } = (0,_hooks_useToast__WEBPACK_IMPORTED_MODULE_6__.useToast)();
    const esService = _services_elasticsearch__WEBPACK_IMPORTED_MODULE_5__.ElasticsearchService.getInstance();
    const handleCreateIndex = async (newIndexName, settings, mappings) => {
        setLoading(true);
        try {
            await esService.createIndex(newIndexName, settings, mappings);
            showSuccess('索引创建成功', `索引 "${newIndexName}" 已成功创建`);
            setShowCreateModal(false);
            onIndexCreated?.();
        }
        catch (error) {
            showError('索引创建失败', error.message || '无法创建索引');
        }
        finally {
            setLoading(false);
        }
    };
    const EditIndexModal = ({ isOpen, onClose, onConfirm, indexName, loading, }) => {
        const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
        const [mappings, setMappings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
        const [showAdvanced, setShowAdvanced] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
        const [loadingData, setLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
        const { error: showError } = (0,_hooks_useToast__WEBPACK_IMPORTED_MODULE_6__.useToast)();
        const esService = _services_elasticsearch__WEBPACK_IMPORTED_MODULE_5__.ElasticsearchService.getInstance();
        // Load current index settings and mappings when modal opens
        react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(() => {
            if (isOpen && indexName) {
                setLoadingData(true);
                Promise.all([
                    esService.getIndexSettings(indexName),
                    esService.getIndexMapping(indexName)
                ])
                    .then(([settingsData, mappingsData]) => {
                    // Extract settings (remove read-only settings)
                    const currentSettings = settingsData[indexName]?.settings?.index || {};
                    const editableSettings = { ...currentSettings };
                    // Remove read-only settings
                    delete editableSettings.creation_date;
                    delete editableSettings.uuid;
                    delete editableSettings.version;
                    delete editableSettings.provided_name;
                    setSettings(JSON.stringify(editableSettings, null, 2));
                    setMappings(JSON.stringify(mappingsData[indexName]?.mappings || {}, null, 2));
                })
                    .catch((error) => {
                    showError('加载索引详情失败', error.message || '无法获取索引信息');
                })
                    .finally(() => {
                    setLoadingData(false);
                });
            }
        }, [isOpen, indexName]);
        const handleClose = () => {
            setSettings('');
            setMappings('');
            setShowAdvanced(false);
            setLoadingData(false);
            onClose();
        };
        const handleSubmit = () => {
            let parsedSettings, parsedMappings;
            try {
                if (settings.trim()) {
                    parsedSettings = JSON.parse(settings);
                }
                if (mappings.trim()) {
                    parsedMappings = JSON.parse(mappings);
                }
            }
            catch (error) {
                alert('JSON 格式错误，请检查设置和映射的格式');
                return;
            }
            onConfirm(parsedSettings, parsedMappings);
        };
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Modal__WEBPACK_IMPORTED_MODULE_2__.Modal, { isOpen: isOpen, onClose: handleClose, title: `编辑索引 "${indexName}"`, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-neutral-600 dark:text-neutral-400", children: "\u6CE8\u610F\uFF1AElasticsearch \u4E0D\u652F\u6301\u76F4\u63A5\u4FEE\u6539\u7D22\u5F15\u7ED3\u6784\u3002\u6B64\u64CD\u4F5C\u4EC5\u80FD\u66F4\u65B0\u90E8\u5206\u7D22\u5F15\u8BBE\u7F6E\u3002" }), loadingData && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-center py-4", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "inline-flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-neutral-600 dark:text-neutral-400", children: "\u6B63\u5728\u52A0\u8F7D\u7D22\u5F15\u8BE6\u60C5..." })] }) })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "checkbox", id: "showAdvanced", checked: showAdvanced, onChange: (e) => setShowAdvanced(e.target.checked), className: "rounded border-neutral-300 dark:border-neutral-600" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { htmlFor: "showAdvanced", className: "text-sm font-medium", children: "\u663E\u793A\u9AD8\u7EA7\u8BBE\u7F6E" })] }), showAdvanced && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium mb-2", children: "\u7D22\u5F15\u8BBE\u7F6E (JSON)" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("textarea", { value: settings, onChange: (e) => setSettings(e.target.value), placeholder: `{
  "number_of_replicas": 1,
  "refresh_interval": "1s"
}`, className: "w-full h-32 px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-dark-secondary text-sm font-mono" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium mb-2", children: "\u5B57\u6BB5\u6620\u5C04 (JSON) - \u4EC5\u4F9B\u53C2\u8003\uFF0C\u65E0\u6CD5\u4FEE\u6539" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("textarea", { value: mappings, onChange: (e) => setMappings(e.target.value), placeholder: `{
  "properties": {
    "field_name": {
      "type": "text"
    }
  }
}`, disabled: true, className: "w-full h-32 px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md bg-neutral-50 dark:bg-neutral-800 text-sm font-mono opacity-60" })] })] })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-end space-x-3 pt-4 border-t border-neutral-200 dark:border-neutral-700", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "secondary", onClick: handleClose, children: "\u53D6\u6D88" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "primary", onClick: handleSubmit, loading: loading, children: "\u66F4\u65B0\u7D22\u5F15" })] })] }) }));
    };
    const handleEditIndex = async (settings, mappings) => {
        if (!indexName)
            return;
        setLoading(true);
        try {
            // 注意：Elasticsearch不支持直接编辑索引结构，这里可能需要重新索引
            // 这是一个简化的实现，实际应用中可能需要更复杂的逻辑
            await esService.updateIndexSettings(indexName, settings);
            showSuccess('索引更新成功', `索引 "${indexName}" 设置已更新`);
            setShowEditModal(false);
            onIndexUpdated?.();
        }
        catch (error) {
            showError('索引更新失败', error.message || '无法更新索引设置');
        }
        finally {
            setLoading(false);
        }
    };
    const handleDeleteIndex = async () => {
        if (!indexName)
            return;
        setLoading(true);
        try {
            await esService.deleteIndex(indexName);
            showSuccess('索引删除成功', `索引 "${indexName}" 已成功删除`);
            setShowDeleteModal(false);
            onIndexDeleted?.();
        }
        catch (error) {
            showError('索引删除失败', error.message || '无法删除索引');
        }
        finally {
            setLoading(false);
        }
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1", children: [showCreateButton && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "primary", size: "sm", onClick: () => setShowCreateModal(true), className: "flex items-center space-x-1 px-3 py-1.5 text-xs h-8", title: "\u521B\u5EFA\u65B0\u7D22\u5F15", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"], { className: "h-4 w-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u521B\u5EFA\u7D22\u5F15" })] })), indexName && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "ghost", size: "sm", onClick: () => setShowEditModal(true), className: "p-1.5 h-8 w-8 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-900/20 dark:hover:text-blue-400 transition-colors", title: `编辑索引 "${indexName}"`, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_8__["default"], { className: "h-4 w-4" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "ghost", size: "sm", onClick: () => setShowDeleteModal(true), className: "p-1.5 h-8 w-8 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-900/20 dark:hover:text-red-400 transition-colors", title: `删除索引 "${indexName}"`, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_10__["default"], { className: "h-4 w-4" }) })] }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(CreateIndexModal, { isOpen: showCreateModal, onClose: () => setShowCreateModal(false), onConfirm: handleCreateIndex, loading: loading }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(EditIndexModal, { isOpen: showEditModal, onClose: () => setShowEditModal(false), onConfirm: handleEditIndex, indexName: indexName, loading: loading }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DeleteIndexModal, { isOpen: showDeleteModal, onClose: () => setShowDeleteModal(false), onConfirm: handleDeleteIndex, indexName: indexName, loading: loading })] }));
};
const CreateIndexModal = ({ isOpen, onClose, onConfirm, loading, }) => {
    const [indexName, setIndexName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [settingsJson, setSettingsJson] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [mappingsJson, setMappingsJson] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('basic');
    const handleSubmit = () => {
        if (!indexName.trim())
            return;
        let settings, mappings;
        try {
            if (settingsJson.trim()) {
                settings = JSON.parse(settingsJson);
            }
            if (mappingsJson.trim()) {
                mappings = JSON.parse(mappingsJson);
            }
        }
        catch (error) {
            alert('JSON 格式错误，请检查设置和映射配置');
            return;
        }
        onConfirm(indexName.trim(), settings, mappings);
    };
    const resetForm = () => {
        setIndexName('');
        setSettingsJson('');
        setMappingsJson('');
        setActiveTab('basic');
    };
    const handleClose = () => {
        resetForm();
        onClose();
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Modal__WEBPACK_IMPORTED_MODULE_2__.Modal, { isOpen: isOpen, onClose: handleClose, title: "\u521B\u5EFA\u65B0\u7D22\u5F15", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4 -m-6 p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex border-b border-neutral-200 dark:border-neutral-700", children: [
                        { key: 'basic', label: '基本信息' },
                        { key: 'settings', label: '索引设置' },
                        { key: 'mappings', label: '字段映射' },
                    ].map(tab => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => setActiveTab(tab.key), className: `px-4 py-2 text-sm font-medium border-b-2 transition-colors ${activeTab === tab.key
                            ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                            : 'border-transparent text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-300'}`, children: tab.label }, tab.key))) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "min-h-64", children: [activeTab === 'basic' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-4", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Input__WEBPACK_IMPORTED_MODULE_4__.Input, { label: "\u7D22\u5F15\u540D\u79F0", value: indexName, onChange: e => setIndexName(e.target.value), placeholder: "\u8F93\u5165\u7D22\u5F15\u540D\u79F0...", helperText: "\u7D22\u5F15\u540D\u79F0\u5FC5\u987B\u5C0F\u5199\uFF0C\u4E0D\u80FD\u5305\u542B\u7A7A\u683C\u548C\u7279\u6B8A\u5B57\u7B26" }) })), activeTab === 'settings' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-4", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-neutral-700 dark:text-dark-text-primary mb-2", children: "\u7D22\u5F15\u8BBE\u7F6E (JSON)" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("textarea", { value: settingsJson, onChange: e => setSettingsJson(e.target.value), placeholder: `{
  "number_of_shards": 1,
  "number_of_replicas": 0
}`, className: "w-full h-40 px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-dark-secondary text-sm font-mono" })] }) })), activeTab === 'mappings' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-4", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-neutral-700 dark:text-dark-text-primary mb-2", children: "\u5B57\u6BB5\u6620\u5C04 (JSON)" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("textarea", { value: mappingsJson, onChange: e => setMappingsJson(e.target.value), placeholder: `{
  "properties": {
    "title": {
      "type": "text"
    },
    "timestamp": {
      "type": "date"
    }
  }
}`, className: "w-full h-40 px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-dark-secondary text-sm font-mono" })] }) }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-end space-x-3 pt-4 border-t border-neutral-200 dark:border-neutral-700", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "secondary", onClick: handleClose, children: "\u53D6\u6D88" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "primary", onClick: handleSubmit, loading: loading, disabled: !indexName.trim(), children: "\u521B\u5EFA\u7D22\u5F15" })] })] }) }));
};
const DeleteIndexModal = ({ isOpen, onClose, onConfirm, indexName, loading, }) => {
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Modal__WEBPACK_IMPORTED_MODULE_2__.Modal, { isOpen: isOpen, onClose: onClose, title: "\u5220\u9664\u7D22\u5F15", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4 -m-6 p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"], { className: "h-6 w-6 text-red-500 flex-shrink-0 mt-0.5" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-neutral-900 dark:text-dark-text-primary", children: ["\u786E\u5B9A\u8981\u5220\u9664\u7D22\u5F15 ", (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("strong", { children: ["\"", indexName, "\""] }), " \u5417\uFF1F"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-600 dark:text-dark-text-secondary mt-2", children: "\u6B64\u64CD\u4F5C\u5C06\u6C38\u4E45\u5220\u9664\u7D22\u5F15\u53CA\u5176\u6240\u6709\u6570\u636E\uFF0C\u65E0\u6CD5\u64A4\u9500\u3002" })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-end space-x-3 pt-4 border-t border-neutral-200 dark:border-neutral-700", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "secondary", onClick: onClose, children: "\u53D6\u6D88" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "danger", onClick: onConfirm, loading: loading, children: "\u786E\u8BA4\u5220\u9664" })] })] }) }));
};


/***/ })

}]);
//# sourceMappingURL=src_renderer_components_Index_IndexList_tsx.renderer.js.map