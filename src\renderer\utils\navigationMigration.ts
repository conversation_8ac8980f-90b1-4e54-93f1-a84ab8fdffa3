/**
 * 导航迁移工具
 * 处理从旧的分离式导航到新的统一数据管理页面的迁移
 */

import { NavigationView } from '../stores/navigation';

export interface MigrationConfig {
  enableRedirection: boolean;
  showMigrationNotice: boolean;
  logMigrationEvents: boolean;
}

export interface MigrationEvent {
  timestamp: Date;
  fromView: NavigationView;
  toView: NavigationView;
  source: 'auto' | 'manual' | 'fallback';
  userAgent?: string;
}

export class NavigationMigrationManager {
  private config: MigrationConfig;
  private migrationHistory: MigrationEvent[] = [];
  private readonly MIGRATION_STORAGE_KEY = 'es-client-navigation-migration';

  constructor(config: Partial<MigrationConfig> = {}) {
    this.config = {
      enableRedirection: true,
      showMigrationNotice: true,
      logMigrationEvents: true,
      ...config
    };

    this.loadMigrationHistory();
  }

  /**
   * 检查是否需要迁移
   */
  shouldMigrate(view: NavigationView): boolean {
    return this.config.enableRedirection && (view === 'indices' || view === 'data');
  }

  /**
   * 执行导航迁移
   */
  migrate(fromView: NavigationView, source: 'auto' | 'manual' | 'fallback' = 'auto'): NavigationView {
    if (!this.shouldMigrate(fromView)) {
      return fromView;
    }

    const toView: NavigationView = 'unified-data';
    
    // 记录迁移事件
    const migrationEvent: MigrationEvent = {
      timestamp: new Date(),
      fromView,
      toView,
      source,
      userAgent: navigator.userAgent
    };

    this.recordMigration(migrationEvent);

    if (this.config.logMigrationEvents) {
      console.log(`🔄 导航迁移: ${fromView} -> ${toView} (${source})`);
    }

    if (this.config.showMigrationNotice && source === 'auto') {
      this.showMigrationNotice(fromView, toView);
    }

    return toView;
  }

  /**
   * 记录迁移事件
   */
  private recordMigration(event: MigrationEvent): void {
    this.migrationHistory.push(event);
    
    // 限制历史记录数量
    if (this.migrationHistory.length > 100) {
      this.migrationHistory = this.migrationHistory.slice(-50);
    }

    this.saveMigrationHistory();
  }

  /**
   * 显示迁移通知
   */
  private showMigrationNotice(fromView: NavigationView, toView: NavigationView): void {
    const viewNames = {
      'indices': '索引管理',
      'data': '数据操作',
      'unified-data': '统一数据管理'
    };

    const message = `📢 页面已升级！${viewNames[fromView as keyof typeof viewNames]} 已整合到 ${viewNames[toView as keyof typeof viewNames]} 中，提供更好的用户体验。`;
    
    // 使用 toast 通知（如果可用）
    if (typeof window !== 'undefined' && (window as any).showToast) {
      (window as any).showToast(message, 'info', 5000);
    } else {
      console.info(message);
    }
  }

  /**
   * 获取迁移统计
   */
  getMigrationStats(): {
    totalMigrations: number;
    migrationsBySource: Record<string, number>;
    migrationsByFromView: Record<string, number>;
    recentMigrations: MigrationEvent[];
  } {
    const migrationsBySource: Record<string, number> = {};
    const migrationsByFromView: Record<string, number> = {};

    this.migrationHistory.forEach(event => {
      migrationsBySource[event.source] = (migrationsBySource[event.source] || 0) + 1;
      migrationsByFromView[event.fromView] = (migrationsByFromView[event.fromView] || 0) + 1;
    });

    return {
      totalMigrations: this.migrationHistory.length,
      migrationsBySource,
      migrationsByFromView,
      recentMigrations: this.migrationHistory.slice(-10)
    };
  }

  /**
   * 清除迁移历史
   */
  clearMigrationHistory(): void {
    this.migrationHistory = [];
    this.saveMigrationHistory();
  }

  /**
   * 保存迁移历史到本地存储
   */
  private saveMigrationHistory(): void {
    try {
      localStorage.setItem(this.MIGRATION_STORAGE_KEY, JSON.stringify(this.migrationHistory));
    } catch (error) {
      console.warn('无法保存迁移历史:', error);
    }
  }

  /**
   * 从本地存储加载迁移历史
   */
  private loadMigrationHistory(): void {
    try {
      const stored = localStorage.getItem(this.MIGRATION_STORAGE_KEY);
      if (stored) {
        this.migrationHistory = JSON.parse(stored).map((event: any) => ({
          ...event,
          timestamp: new Date(event.timestamp)
        }));
      }
    } catch (error) {
      console.warn('无法加载迁移历史:', error);
      this.migrationHistory = [];
    }
  }

  /**
   * 检查用户是否已经适应新界面
   */
  isUserAdapted(): boolean {
    const recentMigrations = this.migrationHistory.filter(
      event => Date.now() - event.timestamp.getTime() < 7 * 24 * 60 * 60 * 1000 // 7天内
    );
    
    // 如果7天内没有迁移事件，认为用户已适应
    return recentMigrations.length === 0;
  }

  /**
   * 获取推荐的默认视图
   */
  getRecommendedDefaultView(): NavigationView {
    if (this.isUserAdapted()) {
      return 'unified-data';
    }
    return 'connections';
  }
}

// 单例实例
export const navigationMigrationManager = new NavigationMigrationManager();

/**
 * 导航迁移 Hook
 */
export const useNavigationMigration = () => {
  const migrate = (fromView: NavigationView, source: 'auto' | 'manual' | 'fallback' = 'auto') => {
    return navigationMigrationManager.migrate(fromView, source);
  };

  const shouldMigrate = (view: NavigationView) => {
    return navigationMigrationManager.shouldMigrate(view);
  };

  const getStats = () => {
    return navigationMigrationManager.getMigrationStats();
  };

  const isUserAdapted = () => {
    return navigationMigrationManager.isUserAdapted();
  };

  return {
    migrate,
    shouldMigrate,
    getStats,
    isUserAdapted,
    getRecommendedDefaultView: () => navigationMigrationManager.getRecommendedDefaultView()
  };
};

/**
 * 路由重定向映射
 */
export const ROUTE_REDIRECTS: Record<string, NavigationView> = {
  '/indices': 'unified-data',
  '/data': 'unified-data',
  '/index': 'unified-data',
  '/data-operations': 'unified-data'
};

/**
 * 获取重定向目标
 */
export const getRedirectTarget = (path: string): NavigationView | null => {
  return ROUTE_REDIRECTS[path] || null;
};
