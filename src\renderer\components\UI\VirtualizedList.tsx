import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';

// 虚拟化列表项接口
interface VirtualizedListItem {
  id: string | number;
  data: any;
  height?: number;
}

// 虚拟化列表属性接口
interface VirtualizedListProps<T = any> {
  items: VirtualizedListItem[];
  itemHeight: number | ((item: VirtualizedListItem, index: number) => number);
  containerHeight: number;
  renderItem: (item: VirtualizedListItem, index: number, style: React.CSSProperties) => React.ReactNode;
  overscan?: number;
  scrollToIndex?: number;
  onScroll?: (scrollTop: number, scrollLeft: number) => void;
  onItemsRendered?: (startIndex: number, endIndex: number, visibleItems: VirtualizedListItem[]) => void;
  className?: string;
  style?: React.CSSProperties;
  estimatedItemHeight?: number;
  enableSmoothScrolling?: boolean;
  loadMoreThreshold?: number;
  onLoadMore?: () => void;
  isLoading?: boolean;
  loadingComponent?: React.ReactNode;
  emptyComponent?: React.ReactNode;
}

// 渲染范围接口
interface RenderRange {
  startIndex: number;
  endIndex: number;
  visibleStartIndex: number;
  visibleEndIndex: number;
}

// 滚动状态接口
interface ScrollState {
  scrollTop: number;
  scrollLeft: number;
  isScrolling: boolean;
}

export const VirtualizedList = <T = any>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  scrollToIndex,
  onScroll,
  onItemsRendered,
  className = '',
  style = {},
  estimatedItemHeight = 50,
  enableSmoothScrolling = true,
  loadMoreThreshold = 5,
  onLoadMore,
  isLoading = false,
  loadingComponent,
  emptyComponent
}: VirtualizedListProps<T>) => {
  // 状态管理
  const [scrollState, setScrollState] = useState<ScrollState>({
    scrollTop: 0,
    scrollLeft: 0,
    isScrolling: false
  });

  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const itemHeightsRef = useRef<Map<number, number>>(new Map());
  const totalHeightRef = useRef<number>(0);

  // 计算项目高度
  const getItemHeight = useCallback((item: VirtualizedListItem, index: number): number => {
    if (typeof itemHeight === 'function') {
      return itemHeight(item, index);
    }
    return itemHeight;
  }, [itemHeight]);

  // 计算总高度
  const calculateTotalHeight = useCallback((): number => {
    if (items.length === 0) return 0;

    let totalHeight = 0;
    for (let i = 0; i < items.length; i++) {
      const cachedHeight = itemHeightsRef.current.get(i);
      if (cachedHeight !== undefined) {
        totalHeight += cachedHeight;
      } else {
        totalHeight += getItemHeight(items[i], i);
      }
    }
    return totalHeight;
  }, [items, getItemHeight]);

  // 更新总高度
  useEffect(() => {
    totalHeightRef.current = calculateTotalHeight();
  }, [calculateTotalHeight]);

  // 计算渲染范围
  const calculateRenderRange = useCallback((scrollTop: number): RenderRange => {
    if (items.length === 0) {
      return {
        startIndex: 0,
        endIndex: 0,
        visibleStartIndex: 0,
        visibleEndIndex: 0
      };
    }

    let currentOffset = 0;
    let startIndex = 0;
    let visibleStartIndex = 0;
    let visibleEndIndex = 0;
    let endIndex = 0;

    // 找到第一个可见项目
    for (let i = 0; i < items.length; i++) {
      const height = itemHeightsRef.current.get(i) || getItemHeight(items[i], i);
      
      if (currentOffset + height > scrollTop) {
        visibleStartIndex = i;
        startIndex = Math.max(0, i - overscan);
        break;
      }
      currentOffset += height;
    }

    // 找到最后一个可见项目
    currentOffset = 0;
    for (let i = 0; i < items.length; i++) {
      const height = itemHeightsRef.current.get(i) || getItemHeight(items[i], i);
      currentOffset += height;
      
      if (currentOffset > scrollTop + containerHeight) {
        visibleEndIndex = i;
        endIndex = Math.min(items.length - 1, i + overscan);
        break;
      }
    }

    // 如果没有找到结束索引，说明滚动到了底部
    if (visibleEndIndex === 0) {
      visibleEndIndex = items.length - 1;
      endIndex = items.length - 1;
    }

    return {
      startIndex,
      endIndex,
      visibleStartIndex,
      visibleEndIndex
    };
  }, [items, containerHeight, overscan, getItemHeight]);

  // 计算项目偏移量
  const calculateItemOffset = useCallback((index: number): number => {
    let offset = 0;
    for (let i = 0; i < index; i++) {
      const height = itemHeightsRef.current.get(i) || getItemHeight(items[i], i);
      offset += height;
    }
    return offset;
  }, [items, getItemHeight]);

  // 当前渲染范围
  const renderRange = useMemo(() => {
    return calculateRenderRange(scrollState.scrollTop);
  }, [scrollState.scrollTop, calculateRenderRange]);

  // 可见项目
  const visibleItems = useMemo(() => {
    return items.slice(renderRange.startIndex, renderRange.endIndex + 1);
  }, [items, renderRange.startIndex, renderRange.endIndex]);

  // 处理滚动事件
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollLeft } = event.currentTarget;
    
    setScrollState(prev => ({
      ...prev,
      scrollTop,
      scrollLeft,
      isScrolling: true
    }));

    // 清除之前的超时
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // 设置滚动结束超时
    scrollTimeoutRef.current = setTimeout(() => {
      setScrollState(prev => ({
        ...prev,
        isScrolling: false
      }));
    }, 150);

    // 触发回调
    onScroll?.(scrollTop, scrollLeft);

    // 检查是否需要加载更多
    if (onLoadMore && !isLoading) {
      const { visibleEndIndex } = calculateRenderRange(scrollTop);
      if (items.length - visibleEndIndex <= loadMoreThreshold) {
        onLoadMore();
      }
    }
  }, [onScroll, onLoadMore, isLoading, items.length, loadMoreThreshold, calculateRenderRange]);

  // 滚动到指定索引
  const scrollToItem = useCallback((index: number, align: 'start' | 'center' | 'end' = 'start') => {
    if (!containerRef.current || index < 0 || index >= items.length) return;

    const itemOffset = calculateItemOffset(index);
    const itemHeight = getItemHeight(items[index], index);
    
    let scrollTop = itemOffset;
    
    switch (align) {
      case 'center':
        scrollTop = itemOffset - (containerHeight - itemHeight) / 2;
        break;
      case 'end':
        scrollTop = itemOffset - containerHeight + itemHeight;
        break;
      case 'start':
      default:
        scrollTop = itemOffset;
        break;
    }

    scrollTop = Math.max(0, Math.min(scrollTop, totalHeightRef.current - containerHeight));

    if (enableSmoothScrolling) {
      containerRef.current.scrollTo({
        top: scrollTop,
        behavior: 'smooth'
      });
    } else {
      containerRef.current.scrollTop = scrollTop;
    }
  }, [items, calculateItemOffset, getItemHeight, containerHeight, enableSmoothScrolling]);

  // 处理 scrollToIndex 属性变化
  useEffect(() => {
    if (scrollToIndex !== undefined && scrollToIndex >= 0 && scrollToIndex < items.length) {
      scrollToItem(scrollToIndex);
    }
  }, [scrollToIndex, items.length, scrollToItem]);

  // 触发 onItemsRendered 回调
  useEffect(() => {
    if (onItemsRendered) {
      const visibleItemsData = items.slice(renderRange.visibleStartIndex, renderRange.visibleEndIndex + 1);
      onItemsRendered(renderRange.visibleStartIndex, renderRange.visibleEndIndex, visibleItemsData);
    }
  }, [renderRange.visibleStartIndex, renderRange.visibleEndIndex, items, onItemsRendered]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // 渲染空状态
  if (items.length === 0) {
    return (
      <div 
        className={`virtualized-list-empty ${className}`}
        style={{ height: containerHeight, ...style }}
      >
        {emptyComponent || (
          <div className="flex items-center justify-center h-full text-gray-500">
            暂无数据
          </div>
        )}
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`virtualized-list ${className} ${scrollState.isScrolling ? 'is-scrolling' : ''}`}
      style={{
        height: containerHeight,
        overflow: 'auto',
        position: 'relative',
        ...style
      }}
      onScroll={handleScroll}
    >
      {/* 总高度占位符 */}
      <div
        style={{
          height: totalHeightRef.current,
          position: 'relative'
        }}
      >
        {/* 渲染可见项目 */}
        {visibleItems.map((item, index) => {
          const actualIndex = renderRange.startIndex + index;
          const itemOffset = calculateItemOffset(actualIndex);
          const itemHeightValue = getItemHeight(item, actualIndex);
          
          const itemStyle: React.CSSProperties = {
            position: 'absolute',
            top: itemOffset,
            left: 0,
            right: 0,
            height: itemHeightValue,
            zIndex: 1
          };

          return (
            <div key={item.id} style={itemStyle}>
              {renderItem(item, actualIndex, itemStyle)}
            </div>
          );
        })}

        {/* 加载更多指示器 */}
        {isLoading && loadingComponent && (
          <div
            style={{
              position: 'absolute',
              top: totalHeightRef.current,
              left: 0,
              right: 0,
              zIndex: 2
            }}
          >
            {loadingComponent}
          </div>
        )}
      </div>
    </div>
  );
};

// 导出类型
export type { VirtualizedListProps, VirtualizedListItem };
