{"version": 3, "file": "src_renderer_components_Connection_ConnectionManager_tsx.renderer.js", "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;;AAEuD;;AAEvD,YAAY,iEAAgB;AAC5B;AACA;AACA,MAAM;AACN;AACA;;AAE0B;AAC1B;;;;;;;;;;;;;;;;ACdA;AACA;AACA;;AAEuD;;AAEvD,iBAAiB,iEAAgB;AACjC,aAAa,oDAAoD;AACjE,aAAa,sDAAsD;AACnE,aAAa,6BAA6B;AAC1C,aAAa,gCAAgC;AAC7C;;AAE+B;AAC/B;;;;;;;;;;;;;;;;ACdA;AACA;AACA;;AAEuD;;AAEvD,eAAe,iEAAgB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,sDAAsD;AACnE,aAAa,wDAAwD;AACrE;;AAE6B;AAC7B;;;;;;;;;;;;;;;;ACpCA;AACA;AACA;;AAEuD;;AAEvD,cAAc,iEAAgB;AAC9B,aAAa,6BAA6B;AAC1C,aAAa,2DAA2D;AACxE,aAAa,wDAAwD;AACrE;;AAE4B;AAC5B;;;;;;;;;;;;;;;;ACbA;AACA;AACA;;AAEuD;;AAEvD,gBAAgB,iEAAgB;AAChC,aAAa,qDAAqD;AAClE,aAAa,6CAA6C;AAC1D,aAAa,mDAAmD;AAChE,aAAa,yDAAyD;AACtE,aAAa,wDAAwD;AACrE,aAAa,iDAAiD;AAC9D,aAAa,0DAA0D;AACvE;;AAE8B;AAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjB+D;AACvB;AACU;AACd;AACE;AACA;AACwB;AACA;AACI;AAC9B;AAC7B,0BAA0B,0CAA0C;AAC3E,YAAY,iBAAiB,EAAE,sEAAkB;AACjD,oCAAoC,+CAAQ;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,4CAA4C,+CAAQ;AACpD,sCAAsC,+CAAQ;AAC9C,wCAAwC,+CAAQ;AAChD,gCAAgC,+CAAQ,GAAG;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,yBAAyB;AACxD;AACA;AACA,iCAAiC,sBAAsB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,uDAAK,UAAU,WAAW,8CAAE,yCAAyC,uDAAK,UAAU,mCAAmC,sDAAI,CAAC,4CAAK,IAAI,sSAAsS,GAAG,uDAAK,UAAU,gDAAgD,uDAAK,UAAU,oCAAoC,sDAAI,YAAY,gIAAgI,GAAG,sDAAI,CAAC,uEAAoB,IAAI;AAC5tB;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,wFAAwF,mBAAmB,sDAAI,QAAQ,iFAAiF,KAAK,GAAG,sDAAI,UAAU,UAAU,sDAAI,CAAC,4CAAK,IAAI,iNAAiN,GAAG,IAAI,GAAG,uDAAK,UAAU,8GAA8G,uDAAK,UAAU,WAAW,sDAAI,YAAY,iHAAiH,GAAG,sDAAI,QAAQ,kJAAkJ,IAAI,GAAG,sDAAI,CAAC,8CAAM,IAAI,kFAAkF,IAAI,GAAG,uDAAK,UAAU,oGAAoG,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,oDAAI,IAAI,uCAAuC,GAAG,sDAAI,WAAW,oIAAoI,IAAI,GAAG,sDAAI,CAAC,4CAAK,IAAI,8LAA8L,GAAG,sDAAI,CAAC,4CAAK,IAAI,+OAA+O,sDAAI,aAAa,gKAAgK,sDAAI,CAAC,oDAAM,IAAI,sBAAsB,MAAM,sDAAI,CAAC,oDAAG,IAAI,sBAAsB,IAAI,GAAG,IAAI,GAAG,sDAAI,CAAC,4EAAsB,IAAI;AACjqE;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,2BAA2B,IAAI,GAAG,uDAAK,UAAU,0HAA0H,sDAAI,CAAC,8CAAM,IAAI,mEAAmE,GAAG,sDAAI,CAAC,8CAAM,IAAI,6FAA6F,IAAI,IAAI;AACpX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvF+D;AACvB;AAC4D;AACvC;AACX;AACsB;AACY;AAC9C;AACJ;AACE;AACA;AACoB;AACpB;AAC7B,6BAA6B,YAAY;AAChD,YAAY,mKAAmK,EAAE,uEAAkB;AACnM,oCAAoC,+CAAQ;AAC5C,sDAAsD,+CAAQ;AAC9D,sDAAsD,+CAAQ;AAC9D,oDAAoD,+CAAQ;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,0FAA4B;AACpC;AACA;AACA,8BAA8B,0FAA4B;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,uDAAK,UAAU,WAAW,8CAAE,qCAAqC,uDAAK,UAAU,2DAA2D,uDAAK,UAAU,WAAW,sDAAI,SAAS,oHAAoH,GAAG,sDAAI,QAAQ,uJAAuJ,IAAI,GAAG,uDAAK,UAAU,qDAAqD,uDAAK,CAAC,+CAAM,IAAI,qHAAqH,sDAAI,CAAC,oDAAO,IAAI,sBAAsB,GAAG,sDAAI,WAAW,sCAAsC,IAAI,GAAG,uDAAK,CAAC,+CAAM,IAAI,mFAAmF,sDAAI,CAAC,oDAAI,IAAI,sBAAsB,GAAG,sDAAI,WAAW,sCAAsC,IAAI,IAAI,IAAI,6DAA6D,sDAAI,CAAC,2CAAI,IAAI,oGAAoG,uDAAK,UAAU,0DAA0D,uDAAK,UAAU,oDAAoD,sDAAI,CAAC,iEAAe,IAAI,8CAA8C,GAAG,uDAAK,UAAU,gCAAgC,sDAAI,SAAS,2GAA2G,GAAG,uDAAK,QAAQ,0JAA0J,GAAG,uDAAK,UAAU,6DAA6D,uDAAK,UAAU,WAAW,sDAAI,WAAW,wFAAwF,GAAG,sDAAI,WAAW,4FAA4F,IAAI,GAAG,uDAAK,UAAU,WAAW,sDAAI,WAAW,oGAAoG,GAAG,sDAAI,WAAW,kGAAkG,IAAI,GAAG,uDAAK,UAAU,WAAW,sDAAI,WAAW,kFAAkF,GAAG,sDAAI,WAAW,oGAAoG,IAAI,GAAG,uDAAK,UAAU,WAAW,sDAAI,WAAW,gFAAgF,GAAG,uDAAK,WAAW,qIAAqI,IAAI,IAAI,IAAI,IAAI,GAAG,uDAAK,CAAC,+CAAM,IAAI,wKAAwK,sDAAI,CAAC,oDAAQ,IAAI,2BAA2B,gCAAgC,IAAI,GAAG,wBAAwB,sDAAI,CAAC,2CAAI,IAAI,4FAA4F,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,oDAAO,IAAI,mCAAmC,GAAG,uDAAK,UAAU,WAAW,sDAAI,SAAS,+FAA+F,GAAG,sDAAI,QAAQ,gFAAgF,IAAI,IAAI,GAAG,IAAI,sDAAI,UAAU,8DAA8D,sDAAI,CAAC,2CAAI,IAAI,wCAAwC,uDAAK,UAAU,8DAA8D,sDAAI,UAAU,8EAA8E,sDAAI,CAAC,qDAAI,IAAI,uCAAuC,GAAG,GAAG,uDAAK,UAAU,WAAW,sDAAI,SAAS,qHAAqH,GAAG,sDAAI,QAAQ,6KAA6K,IAAI,GAAG,uDAAK,CAAC,+CAAM,IAAI,yCAAyC,sDAAI,CAAC,oDAAI,IAAI,2BAA2B,gCAAgC,IAAI,GAAG;AACz1I;AACA,4BAA4B,sDAAI,CAAC,2CAAI,IAAI,4BAA4B,uDAAK,UAAU,2DAA2D,sDAAI,UAAU,2DAA2D,uDAAK,UAAU,gCAAgC,uDAAK,UAAU,0DAA0D,sDAAI,SAAS,kGAAkG,GAAG,sDAAI,CAAC,6CAAK,IAAI,sDAAsD,IAAI,GAAG,sDAAI,CAAC,kFAAyB,IAAI,+EAA+E,GAAG,uDAAK,UAAU,4GAA4G,uDAAK,WAAW,6FAA6F,2BAA2B,uDAAK,WAAW,2CAA2C,sDAAI,WAAW,uDAAuD,2CAA2C,KAAK,IAAI,GAAG,GAAG,uDAAK,UAAU,4EAA4E,sDAAI,CAAC,+CAAM,IAAI,+FAA+F,sDAAI,CAAC,oDAAQ,IAAI,sBAAsB,GAAG,MAAM,sDAAI,CAAC,+CAAM,IAAI,6KAA6K,sDAAI,CAAC,oDAAK,IAAI,sBAAsB,GAAG,IAAI,sDAAI,CAAC,+CAAM,IAAI,yFAAyF,sDAAI,CAAC,oDAAK,IAAI,sBAAsB,GAAG,GAAG,sDAAI,CAAC,+CAAM,IAAI,6NAA6N,sDAAI,CAAC,oDAAM,IAAI,sBAAsB,GAAG,IAAI,IAAI,GAAG;AACh9D,iBAAiB,IAAI,GAAG,sDAAI,CAAC,6CAAK,IAAI;AACtC;AACA;AACA,iBAAiB,oEAAoE,sDAAI,CAAC,4DAAc,IAAI;AAC5G;AACA;AACA,uBAAuB,GAAG,GAAG,sDAAI,CAAC,6CAAK,IAAI,qHAAqH,uDAAK,UAAU,mCAAmC,sDAAI,QAAQ,6LAA6L,GAAG,uDAAK,UAAU,iEAAiE,sDAAI,CAAC,+CAAM,IAAI,2FAA2F,GAAG,sDAAI,CAAC,+CAAM,IAAI,4HAA4H,IAAI,IAAI,GAAG,GAAG,sDAAI,CAAC,6CAAK,IAAI,8HAA8H,uDAAK,UAAU,mCAAmC,uDAAK,UAAU,2DAA2D,sDAAI,QAAQ,uNAAuN,GAAG,uDAAK,CAAC,+CAAM,IAAI,wHAAwH,sDAAI,CAAC,oDAAK,IAAI,2BAA2B,gCAAgC,IAAI,qCAAqC,uDAAK,UAAU,0CAA0C,sDAAI,CAAC,oDAAO,IAAI,sDAAsD,GAAG,sDAAI,QAAQ,2HAA2H,IAAI,MAAM,sDAAI,UAAU,mGAAmG,uDAAK,UAAU,8GAA8G,uDAAK,UAAU,qDAAqD,sDAAI,UAAU,WAAW,8CAAE,+EAA+E,GAAG,uDAAK,UAAU,WAAW,uDAAK,UAAU,uJAAuJ,sDAAI,WAAW,8FAA8F,KAAK,GAAG,uDAAK,UAAU,+GAA+G,sBAAsB,2FAA2F,2BAA2B,MAAM,IAAI,IAAI,GAAG,sDAAI,UAAU,wFAAwF,IAAI,qBAAqB,KAAK,GAAG,IAAI;AACz2F;;;;;;;;;;;;;;;;;;;;;;;;;;ACrG+D;AACZ;AACmC;AACF;AAChD;AAC7B,qCAAqC,4DAA4D;AACxG,gCAAgC,+CAAQ,CAAC,yFAA4B;AACrE,IAAI,gDAAS;AACb,4BAA4B,yFAA4B;AACxD;AACA,KAAK;AACL;AACA,0BAA0B,6CAAE;AAC5B;AACA;AACA,uBAAuB,sDAAI,CAAC,oDAAW,IAAI,WAAW,6CAAE,+BAA+B;AACvF;AACA;AACA,uBAAuB,sDAAI,CAAC,oDAAO,IAAI,WAAW,6CAAE,2CAA2C;AAC/F;AACA,uBAAuB,sDAAI,CAAC,oDAAW,IAAI,WAAW,6CAAE,6BAA6B;AACrF;AACA;AACA,uBAAuB,sDAAI,CAAC,oDAAO,IAAI,WAAW,6CAAE,8BAA8B;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,uDAAK,UAAU,WAAW,6CAAE,uDAAuD,uDAAK,UAAU,WAAW,6CAAE,mIAAmI,sDAAI,UAAU,kFAAkF,KAAK,GAAG,sDAAI,WAAW,WAAW,6CAAE,gZAAgZ,IAAI;AAC1xB;AACA,YAAY,uDAAK,UAAU,WAAW,6CAAE,sJAAsJ,uDAAK,UAAU,qDAAqD,uDAAK,UAAU,uHAAuH,sDAAI,UAAU,4FAA4F,KAAK,GAAG,uDAAK,UAAU,gCAAgC,uDAAK,UAAU,qDAAqD,sDAAI,WAAW,WAAW,6CAAE,+HAA+H,2BAA2B,uDAAK,UAAU,wEAAwE,sDAAI,CAAC,oDAAG,IAAI,sBAAsB,GAAG,uDAAK,WAAW,uCAAuC,IAAI,KAAK,0BAA0B,uDAAK,UAAU,6EAA6E,sDAAI,CAAC,oDAAK,IAAI,sBAAsB,GAAG,uDAAK,WAAW,mFAAmF,IAAI,KAAK,IAAI,GAAG,sDAAI,UAAU,WAAW,6CAAE,kWAAkW,IAAI;AAChnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnD+D;AACvB;AAC+D;AACnB;AAC9C;AACF;AAC7B,kCAAkC,wCAAwC;AACjF,0DAA0D,+CAAQ;AAClE,4CAA4C,+CAAQ;AACpD,wCAAwC,+CAAQ;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,0FAA4B;AAC7D;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,sDAAI,CAAC,oDAAI,IAAI,WAAW,8CAAE,8BAA8B;AAC/E;AACA,uBAAuB,sDAAI,CAAC,oDAAM,IAAI,WAAW,8CAAE,gCAAgC;AACnF;AACA,uBAAuB,sDAAI,CAAC,oDAAQ,IAAI,WAAW,8CAAE,gCAAgC;AACrF;AACA,uBAAuB,sDAAI,CAAC,oDAAW,IAAI,WAAW,8CAAE,+BAA+B;AACvF;AACA,uBAAuB,sDAAI,CAAC,oDAAO,IAAI,WAAW,8CAAE,6BAA6B;AACjF;AACA,uBAAuB,sDAAI,CAAC,oDAAO,IAAI,WAAW,8CAAE,2CAA2C;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,uDAAK,UAAU,WAAW,8CAAE,qCAAqC,sDAAI,CAAC,+CAAM,IAAI,gNAAgN,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,oDAAO,IAAI,mCAAmC,GAAG,sDAAI,WAAW,+CAA+C,IAAI,MAAM,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,oDAAI,IAAI,sBAAsB,GAAG,sDAAI,WAAW,sCAAsC,IAAI,IAAI,oBAAoB,uDAAK,UAAU,6FAA6F,uDAAK,UAAU,2DAA2D,uDAAK,UAAU,uFAAuF,sDAAI,WAAW,+GAA+G,IAAI,6BAA6B,uDAAK,UAAU,8EAA8E,sDAAI,CAAC,oDAAK,IAAI,sBAAsB,GAAG,uDAAK,WAAW,yCAAyC,IAAI,KAAK,GAAG,sDAAI,UAAU,mFAAmF,sDAAI,UAAU,WAAW,8CAAE,oJAAoJ,UAAU,sBAAsB,MAAM,GAAG,GAAG,uDAAK,UAAU,oFAAoF,sDAAI,WAAW,0BAA0B,GAAG,uDAAK,WAAW,wCAAwC,IAAI,0BAA0B,sDAAI,UAAU,2KAA2K,KAAK,2CAA2C,sDAAI,UAAU,WAAW,8CAAE;AACllE;AACA,oGAAoG,uDAAK,UAAU,0EAA0E,sDAAI,CAAC,oDAAW,IAAI,0DAA0D,MAAM,sDAAI,CAAC,oDAAO,IAAI,wDAAwD,IAAI,uDAAK,UAAU,gCAAgC,sDAAI,SAAS,WAAW,8CAAE;AACta;AACA,kIAAkI,wBAAwB,sDAAI,QAAQ,WAAW,8CAAE;AACnL;AACA,yGAAyG,qDAAqD,uDAAK,UAAU,qGAAqG,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,qDAAG,IAAI,sBAAsB,GAAG,uDAAK,WAAW,uEAAuE,IAAI,GAAG,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,oDAAK,IAAI,sBAAsB,GAAG,uDAAK,WAAW,iFAAiF,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK;AAC9rB;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxE+D;AACJ;AACiC;AACR;AAChD;AAC7B,gCAAgC,4EAA4E;AACnH,0CAA0C,+CAAQ;AAClD,kDAAkD,+CAAQ;AAC1D,8CAA8C,+CAAQ;AACtD,sCAAsC,+CAAQ;AAC9C,qBAAqB,6CAAM;AAC3B,2BAA2B,6CAAM;AACjC,IAAI,gDAAS;AACb;AACA;AACA;AACA;AACA,0CAA0C,0FAA4B;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,uBAAuB,sDAAI,CAAC,oDAAO,IAAI,oCAAoC;AAC3E;AACA,uBAAuB,sDAAI,CAAC,oDAAI,IAAI,qCAAqC;AACzE;AACA,uBAAuB,sDAAI,CAAC,oDAAK,IAAI,oCAAoC;AACzE;AACA,uBAAuB,sDAAI,CAAC,oDAAM,IAAI,oCAAoC;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,uDAAK,UAAU,WAAW,8CAAE,oCAAoC,uDAAK,UAAU,kCAAkC,sDAAI,YAAY,2LAA2L,8CAAE,qRAAqR,GAAG,sDAAI,CAAC,oDAAM,IAAI,0FAA0F,iBAAiB,sDAAI,UAAU,4EAA4E,sDAAI,UAAU,8FAA8F,GAAG,kDAAkD,sDAAI,CAAC,oDAAW,IAAI,2FAA2F,KAAK,iDAAiD,sDAAI,UAAU,gCAAgC,8CAAE,oNAAoN,uDAAK,UAAU,6DAA6D,8CAAE,wSAAwS,uDAAK,UAAU,wGAAwG,uDAAK,UAAU,wCAAwC,uDAAK,UAAU,qDAAqD,uDAAK,WAAW,mIAAmI,sBAAsB,sDAAI,WAAW,yFAAyF,KAAK,GAAG,uDAAK,UAAU,0DAA0D,sDAAI,WAAW,gHAAgH,2BAA2B,uDAAK,UAAU,8EAA8E,sDAAI,CAAC,oDAAK,IAAI,sBAAsB,GAAG,sDAAI,WAAW,oDAAoD,IAAI,KAAK,2BAA2B,uDAAK,UAAU,+FAA+F,KAAK,IAAI,GAAG,uDAAK,UAAU,6GAA6G,uDAAK,WAAW,kFAAkF,IAAI,uDAAK,UAAU,WAAW,8CAAE,+FAA+F,sDAAI,CAAC,oDAAG,IAAI,sBAAsB,GAAG,uDAAK,WAAW,0DAA0D,IAAI,+BAA+B,sDAAI,CAAC,oDAAK,IAAI,oCAAoC,KAAK,IAAI,oBAAoB,KAAK;AAC54G", "sources": ["webpack://es-client/./node_modules/lucide-react/dist/esm/icons/pen.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/power-off.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/server.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/trash.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/wifi-off.mjs", "webpack://es-client/./src/renderer/components/Connection/ConnectionForm.tsx", "webpack://es-client/./src/renderer/components/Connection/ConnectionManager.tsx", "webpack://es-client/./src/renderer/components/Connection/ConnectionStatusIndicator.tsx", "webpack://es-client/./src/renderer/components/Connection/ConnectionTestFeedback.tsx", "webpack://es-client/./src/renderer/components/Connection/SmartConnectionInput.tsx"], "sourcesContent": ["/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst Pen = createLucideIcon(\"Pen\", [\n  [\n    \"path\",\n    { d: \"M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z\", key: \"5qss01\" }\n  ]\n]);\n\nexport { Pen as default };\n//# sourceMappingURL=pen.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst PowerOff = createLucideIcon(\"PowerOff\", [\n  [\"path\", { d: \"M18.36 6.64A9 9 0 0 1 20.77 15\", key: \"dxknvb\" }],\n  [\"path\", { d: \"M6.16 6.16a9 9 0 1 0 12.68 12.68\", key: \"1x7qb5\" }],\n  [\"path\", { d: \"M12 2v4\", key: \"3427ic\" }],\n  [\"path\", { d: \"m2 2 20 20\", key: \"1ooewy\" }]\n]);\n\nexport { PowerOff as default };\n//# sourceMappingURL=power-off.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst Server = createLucideIcon(\"Server\", [\n  [\n    \"rect\",\n    {\n      width: \"20\",\n      height: \"8\",\n      x: \"2\",\n      y: \"2\",\n      rx: \"2\",\n      ry: \"2\",\n      key: \"ngkwjq\"\n    }\n  ],\n  [\n    \"rect\",\n    {\n      width: \"20\",\n      height: \"8\",\n      x: \"2\",\n      y: \"14\",\n      rx: \"2\",\n      ry: \"2\",\n      key: \"iecqi9\"\n    }\n  ],\n  [\"line\", { x1: \"6\", x2: \"6.01\", y1: \"6\", y2: \"6\", key: \"16zg32\" }],\n  [\"line\", { x1: \"6\", x2: \"6.01\", y1: \"18\", y2: \"18\", key: \"nzw8ys\" }]\n]);\n\nexport { Server as default };\n//# sourceMappingURL=server.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst Trash = createLucideIcon(\"Trash\", [\n  [\"path\", { d: \"M3 6h18\", key: \"d0wm0j\" }],\n  [\"path\", { d: \"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\", key: \"4alrt4\" }],\n  [\"path\", { d: \"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\", key: \"v07s0e\" }]\n]);\n\nexport { Trash as default };\n//# sourceMappingURL=trash.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst WifiOff = createLucideIcon(\"WifiOff\", [\n  [\"line\", { x1: \"2\", x2: \"22\", y1: \"2\", y2: \"22\", key: \"a6p6uj\" }],\n  [\"path\", { d: \"M8.5 16.5a5 5 0 0 1 7 0\", key: \"sej527\" }],\n  [\"path\", { d: \"M2 8.82a15 15 0 0 1 4.17-2.65\", key: \"11utq1\" }],\n  [\"path\", { d: \"M10.66 5c4.01-.36 8.14.9 11.34 3.76\", key: \"hxefdu\" }],\n  [\"path\", { d: \"M16.85 11.25a10 10 0 0 1 2.22 1.68\", key: \"q734kn\" }],\n  [\"path\", { d: \"M5 13a10 10 0 0 1 5.24-2.76\", key: \"piq4yl\" }],\n  [\"line\", { x1: \"12\", x2: \"12.01\", y1: \"20\", y2: \"20\", key: \"of4bc4\" }]\n]);\n\nexport { WifiOff as default };\n//# sourceMappingURL=wifi-off.mjs.map\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { useState } from 'react';\nimport { Lock, Eye, EyeOff, } from 'lucide-react';\nimport { Input } from '../UI/Input';\nimport { Button } from '../UI/Button';\nimport { Switch } from '../UI/Switch';\nimport { useConnectionStore, } from '../../stores/connection';\nimport { SmartConnectionInput } from './SmartConnectionInput';\nimport { ConnectionTestFeedback } from './ConnectionTestFeedback';\nimport { cn } from '../../utils/cn';\nexport const ConnectionForm = ({ connection, onSave, onCancel, className, }) => {\n    const { testConnection } = useConnectionStore();\n    const [formData, setFormData] = useState({\n        name: connection?.name || '',\n        host: connection?.host || 'localhost',\n        port: connection?.port || 9200,\n        username: connection?.username || '',\n        password: connection?.password || '',\n        ssl: connection?.ssl || false,\n    });\n    const [showPassword, setShowPassword] = useState(false);\n    const [isTesting, setIsTesting] = useState(false);\n    const [testResult, setTestResult] = useState(null);\n    const [errors, setErrors] = useState({});\n    const validateForm = () => {\n        const newErrors = {};\n        if (!formData.name.trim()) {\n            newErrors.name = '连接名称不能为空';\n        }\n        if (!formData.host.trim()) {\n            newErrors.host = '主机地址不能为空';\n        }\n        if (!formData.port || formData.port < 1 || formData.port > 65535) {\n            newErrors.port = '端口号必须在 1-65535 之间';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleInputChange = (field, value) => {\n        setFormData(prev => ({ ...prev, [field]: value }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors(prev => ({ ...prev, [field]: '' }));\n        }\n        // Clear test result when connection details change\n        if (['host', 'port', 'username', 'password', 'ssl'].includes(field)) {\n            setTestResult(null);\n        }\n    };\n    const handleTestConnection = async () => {\n        if (!validateForm())\n            return;\n        setIsTesting(true);\n        setTestResult(null);\n        try {\n            const result = await testConnection(formData);\n            setTestResult(result);\n        }\n        catch (error) {\n            setTestResult({\n                success: false,\n                error: error instanceof Error ? error.message : '连接测试失败',\n            });\n        }\n        finally {\n            setIsTesting(false);\n        }\n    };\n    const handleSave = () => {\n        if (!validateForm())\n            return;\n        onSave(formData);\n    };\n    return (_jsxs(\"div\", { className: cn('space-y-6 p-6', className), children: [_jsxs(\"div\", { className: \"space-y-4\", children: [_jsx(Input, { label: \"\\u8FDE\\u63A5\\u540D\\u79F0\", placeholder: \"\\u8F93\\u5165\\u8FDE\\u63A5\\u540D\\u79F0\", value: formData.name, onChange: e => handleInputChange('name', e.target.value), error: errors.name, helperText: \"\\u4E3A\\u6B64\\u8FDE\\u63A5\\u8BBE\\u7F6E\\u4E00\\u4E2A\\u6613\\u4E8E\\u8BC6\\u522B\\u7684\\u540D\\u79F0\" }), _jsxs(\"div\", { className: \"grid grid-cols-3 gap-4\", children: [_jsxs(\"div\", { className: \"col-span-2\", children: [_jsx(\"label\", { className: \"block text-sm font-medium text-neutral-700 dark:text-dark-text-primary mb-2\", children: \"\\u4E3B\\u673A\\u5730\\u5740\" }), _jsx(SmartConnectionInput, { value: formData.host, onChange: (value) => handleInputChange('host', value), onSuggestionSelect: (suggestion) => {\n                                            handleInputChange('host', suggestion.host);\n                                            handleInputChange('port', suggestion.port);\n                                            handleInputChange('ssl', suggestion.ssl);\n                                            if (suggestion.username) {\n                                                handleInputChange('username', suggestion.username);\n                                            }\n                                        }, placeholder: \"\\u8F93\\u5165\\u4E3B\\u673A\\u5730\\u5740\\u6216\\u9009\\u62E9\\u5EFA\\u8BAE...\" }), errors.host && (_jsx(\"p\", { className: \"mt-1 text-sm text-red-600 dark:text-red-400\", children: errors.host }))] }), _jsx(\"div\", { children: _jsx(Input, { label: \"\\u7AEF\\u53E3\", type: \"number\", placeholder: \"9200\", value: formData.port.toString(), onChange: e => handleInputChange('port', parseInt(e.target.value) || 9200), error: errors.port, min: 1, max: 65535 }) })] }), _jsxs(\"div\", { className: \"flex items-center justify-between p-4 bg-neutral-50 dark:bg-dark-tertiary rounded-lg\", children: [_jsxs(\"div\", { children: [_jsx(\"label\", { className: \"text-sm font-medium text-neutral-700 dark:text-dark-text-primary\", children: \"\\u542F\\u7528 SSL/TLS\" }), _jsx(\"p\", { className: \"text-xs text-neutral-500 dark:text-dark-text-secondary\", children: \"\\u4F7F\\u7528 HTTPS \\u534F\\u8BAE\\u8FDE\\u63A5\\u5230 Elasticsearch\" })] }), _jsx(Switch, { checked: formData.ssl, onChange: e => handleInputChange('ssl', e.target.checked) })] }), _jsxs(\"div\", { className: \"space-y-4 p-4 border border-neutral-200 dark:border-neutral-600 rounded-lg\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-2\", children: [_jsx(Lock, { className: \"h-4 w-4 text-neutral-500\" }), _jsx(\"span\", { className: \"text-sm font-medium text-neutral-700 dark:text-dark-text-primary\", children: \"\\u8EAB\\u4EFD\\u9A8C\\u8BC1 (\\u53EF\\u9009)\" })] }), _jsx(Input, { label: \"\\u7528\\u6237\\u540D\", placeholder: \"\\u8F93\\u5165\\u7528\\u6237\\u540D\", value: formData.username, onChange: e => handleInputChange('username', e.target.value), autoComplete: \"username\" }), _jsx(Input, { label: \"\\u5BC6\\u7801\", type: showPassword ? 'text' : 'password', placeholder: \"\\u8F93\\u5165\\u5BC6\\u7801\", value: formData.password, onChange: e => handleInputChange('password', e.target.value), autoComplete: \"current-password\", rightIcon: _jsx(\"button\", { type: \"button\", onClick: () => setShowPassword(!showPassword), className: \"hover:text-neutral-600 dark:hover:text-dark-text-primary\", children: showPassword ? (_jsx(EyeOff, { className: \"h-4 w-4\" })) : (_jsx(Eye, { className: \"h-4 w-4\" })) }) })] }), _jsx(ConnectionTestFeedback, { connection: formData, onTestComplete: (success, error) => {\n                            setTestResult({\n                                success,\n                                error,\n                                clusterInfo: undefined, // Will be populated by the feedback component\n                            });\n                        } })] }), _jsxs(\"div\", { className: \"flex items-center justify-end space-x-3 pt-4 border-t border-neutral-200 dark:border-neutral-600\", children: [_jsx(Button, { variant: \"secondary\", onClick: onCancel, children: \"\\u53D6\\u6D88\" }), _jsx(Button, { onClick: handleSave, disabled: !testResult?.success, children: connection ? '更新连接' : '保存连接' })] })] }));\n};\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { useState } from 'react';\nimport { Plus, Edit2, Trash2, Power, PowerOff, Wifi, WifiOff, History, Trash, } from 'lucide-react';\nimport { useConnectionStore } from '../../stores/connection';\nimport { ConnectionForm } from './ConnectionForm';\nimport { ConnectionStatusIndicator } from './ConnectionStatusIndicator';\nimport { intelligentConnectionService } from '../../services/intelligentConnection';\nimport { Button } from '../UI/Button';\nimport { Card } from '../UI/Card';\nimport { Badge } from '../UI/Badge';\nimport { Modal } from '../UI/Modal';\nimport { HealthIndicator } from '../UI/HealthIndicator';\nimport { cn } from '../../utils/cn';\nexport const ConnectionManager = ({ className, }) => {\n    const { connections, activeConnectionId, isConnecting, connectionError, clusterInfo, addConnection, updateConnection, deleteConnection, setActiveConnection, disconnect, } = useConnectionStore();\n    const [showForm, setShowForm] = useState(false);\n    const [editingConnection, setEditingConnection] = useState(null);\n    const [showDeleteConfirm, setShowDeleteConfirm] = useState(null);\n    const [showHistoryModal, setShowHistoryModal] = useState(false);\n    const handleAddConnection = () => {\n        setEditingConnection(null);\n        setShowForm(true);\n    };\n    const handleEditConnection = (connection) => {\n        setEditingConnection(connection);\n        setShowForm(true);\n    };\n    const handleSaveConnection = (connectionData) => {\n        if (editingConnection) {\n            updateConnection(editingConnection.id, connectionData);\n        }\n        else {\n            addConnection(connectionData);\n        }\n        setShowForm(false);\n        setEditingConnection(null);\n    };\n    const handleDeleteConnection = (id) => {\n        deleteConnection(id);\n        setShowDeleteConfirm(null);\n    };\n    const handleConnect = async (connection) => {\n        try {\n            await setActiveConnection(connection.id);\n        }\n        catch (error) {\n            console.error('Failed to connect:', error);\n        }\n    };\n    const handleDisconnect = () => {\n        disconnect();\n    };\n    const handleClearHistory = () => {\n        intelligentConnectionService.clearConnectionHistory();\n        setShowHistoryModal(false);\n    };\n    const connectionHistory = intelligentConnectionService.getConnectionHistory();\n    const getConnectionStatus = (connection) => {\n        if (connection.isActive && !connectionError) {\n            return {\n                status: 'connected',\n                label: '已连接',\n                healthColor: 'green',\n                badgeVariant: 'success',\n            };\n        }\n        else if (connection.id === activeConnectionId && isConnecting) {\n            return {\n                status: 'connecting',\n                label: '连接中',\n                healthColor: 'yellow',\n                badgeVariant: 'warning',\n            };\n        }\n        else if (connection.id === activeConnectionId && connectionError) {\n            return {\n                status: 'error',\n                label: '连接失败',\n                healthColor: 'red',\n                badgeVariant: 'error',\n            };\n        }\n        else {\n            return {\n                status: 'disconnected',\n                label: '未连接',\n                healthColor: 'red',\n                badgeVariant: 'default',\n            };\n        }\n    };\n    return (_jsxs(\"div\", { className: cn('space-y-6', className), children: [_jsxs(\"div\", { className: \"flex items-center justify-between\", children: [_jsxs(\"div\", { children: [_jsx(\"h2\", { className: \"text-2xl font-bold text-neutral-900 dark:text-dark-text-primary\", children: \"\\u8FDE\\u63A5\\u7BA1\\u7406\" }), _jsx(\"p\", { className: \"text-sm text-neutral-600 dark:text-dark-text-secondary mt-1\", children: \"\\u7BA1\\u7406\\u60A8\\u7684 Elasticsearch \\u96C6\\u7FA4\\u8FDE\\u63A5\" })] }), _jsxs(\"div\", { className: \"flex items-center space-x-2\", children: [_jsxs(Button, { variant: \"secondary\", onClick: () => setShowHistoryModal(true), className: \"flex items-center space-x-2\", children: [_jsx(History, { className: \"h-4 w-4\" }), _jsx(\"span\", { children: \"\\u8FDE\\u63A5\\u5386\\u53F2\" })] }), _jsxs(Button, { onClick: handleAddConnection, className: \"flex items-center space-x-2\", children: [_jsx(Plus, { className: \"h-4 w-4\" }), _jsx(\"span\", { children: \"\\u65B0\\u5EFA\\u8FDE\\u63A5\" })] })] })] }), activeConnectionId && clusterInfo && !connectionError && (_jsx(Card, { className: \"p-6 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800\", children: _jsxs(\"div\", { className: \"flex items-start justify-between\", children: [_jsxs(\"div\", { className: \"flex items-start space-x-4\", children: [_jsx(HealthIndicator, { status: \"green\", size: \"md\", showText: false }), _jsxs(\"div\", { className: \"flex-1\", children: [_jsx(\"h3\", { className: \"text-lg font-semibold text-green-800 dark:text-green-200\", children: clusterInfo.cluster_name }), _jsxs(\"p\", { className: \"text-sm text-green-600 dark:text-green-300 mt-1\", children: [\"\\u8FDE\\u63A5:\", ' ', connections.find(c => c.id === activeConnectionId)?.name] }), _jsxs(\"div\", { className: \"grid grid-cols-2 gap-4 mt-3 text-sm\", children: [_jsxs(\"div\", { children: [_jsx(\"span\", { className: \"text-green-700 dark:text-green-300 font-medium\", children: \"\\u7248\\u672C:\" }), _jsx(\"span\", { className: \"text-green-600 dark:text-green-400 ml-2\", children: clusterInfo.version.number })] }), _jsxs(\"div\", { children: [_jsx(\"span\", { className: \"text-green-700 dark:text-green-300 font-medium\", children: \"\\u6784\\u5EFA\\u7C7B\\u578B:\" }), _jsx(\"span\", { className: \"text-green-600 dark:text-green-400 ml-2\", children: clusterInfo.version.build_flavor })] }), _jsxs(\"div\", { children: [_jsx(\"span\", { className: \"text-green-700 dark:text-green-300 font-medium\", children: \"Lucene:\" }), _jsx(\"span\", { className: \"text-green-600 dark:text-green-400 ml-2\", children: clusterInfo.version.lucene_version })] }), _jsxs(\"div\", { children: [_jsx(\"span\", { className: \"text-green-700 dark:text-green-300 font-medium\", children: \"UUID:\" }), _jsxs(\"span\", { className: \"text-green-600 dark:text-green-400 ml-2 font-mono text-xs\", children: [clusterInfo.cluster_uuid.substring(0, 8), \"...\"] })] })] })] })] }), _jsxs(Button, { variant: \"secondary\", size: \"sm\", onClick: handleDisconnect, className: \"text-green-700 dark:text-green-300 hover:text-green-800 dark:hover:text-green-200\", children: [_jsx(PowerOff, { className: \"h-4 w-4 mr-2\" }), \"\\u65AD\\u5F00\\u8FDE\\u63A5\"] })] }) })), connectionError && (_jsx(Card, { className: \"p-4 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800\", children: _jsxs(\"div\", { className: \"flex items-center space-x-3\", children: [_jsx(WifiOff, { className: \"h-5 w-5 text-red-500\" }), _jsxs(\"div\", { children: [_jsx(\"h3\", { className: \"font-medium text-red-800 dark:text-red-200\", children: \"\\u8FDE\\u63A5\\u5931\\u8D25\" }), _jsx(\"p\", { className: \"text-sm text-red-600 dark:text-red-300\", children: connectionError })] })] }) })), _jsx(\"div\", { className: \"space-y-4\", children: connections.length === 0 ? (_jsx(Card, { className: \"p-8 text-center\", children: _jsxs(\"div\", { className: \"flex flex-col items-center space-y-4\", children: [_jsx(\"div\", { className: \"p-3 bg-neutral-100 dark:bg-dark-tertiary rounded-full\", children: _jsx(Wifi, { className: \"h-8 w-8 text-neutral-400\" }) }), _jsxs(\"div\", { children: [_jsx(\"h3\", { className: \"text-lg font-medium text-neutral-900 dark:text-dark-text-primary\", children: \"\\u6682\\u65E0\\u8FDE\\u63A5\" }), _jsx(\"p\", { className: \"text-neutral-600 dark:text-dark-text-secondary mt-1\", children: \"\\u521B\\u5EFA\\u60A8\\u7684\\u7B2C\\u4E00\\u4E2A Elasticsearch \\u8FDE\\u63A5\\u5F00\\u59CB\\u4F7F\\u7528\" })] }), _jsxs(Button, { onClick: handleAddConnection, children: [_jsx(Plus, { className: \"h-4 w-4 mr-2\" }), \"\\u65B0\\u5EFA\\u8FDE\\u63A5\"] })] }) })) : (connections.map(connection => {\n                    const status = getConnectionStatus(connection);\n                    return (_jsx(Card, { className: \"p-4\", children: _jsxs(\"div\", { className: \"flex items-center justify-between\", children: [_jsx(\"div\", { className: \"flex items-center space-x-4 flex-1\", children: _jsxs(\"div\", { className: \"flex-1\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-2 mb-2\", children: [_jsx(\"h3\", { className: \"font-medium text-neutral-900 dark:text-dark-text-primary\", children: connection.name }), _jsx(Badge, { variant: status.badgeVariant, children: status.label })] }), _jsx(ConnectionStatusIndicator, { connectionId: connection.id, showDetails: true, size: \"sm\", className: \"mb-2\" }), _jsxs(\"div\", { className: \"flex items-center space-x-4 text-sm text-neutral-600 dark:text-dark-text-secondary\", children: [_jsxs(\"span\", { children: [connection.ssl ? 'https' : 'http', \"://\", connection.host, \":\", connection.port] }), connection.username && (_jsxs(\"span\", { className: \"flex items-center\", children: [_jsx(\"span\", { className: \"w-1 h-1 bg-neutral-400 rounded-full mr-2\" }), \"\\u7528\\u6237: \", connection.username] }))] })] }) }), _jsxs(\"div\", { className: \"flex items-center space-x-2\", children: [connection.isActive ? (_jsx(Button, { variant: \"secondary\", size: \"sm\", onClick: handleDisconnect, disabled: isConnecting, children: _jsx(PowerOff, { className: \"h-4 w-4\" }) })) : (_jsx(Button, { variant: \"secondary\", size: \"sm\", onClick: () => handleConnect(connection), loading: isConnecting && activeConnectionId === connection.id, disabled: isConnecting, children: _jsx(Power, { className: \"h-4 w-4\" }) })), _jsx(Button, { variant: \"ghost\", size: \"sm\", onClick: () => handleEditConnection(connection), children: _jsx(Edit2, { className: \"h-4 w-4\" }) }), _jsx(Button, { variant: \"ghost\", size: \"sm\", onClick: () => setShowDeleteConfirm(connection.id), className: \"text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20\", children: _jsx(Trash2, { className: \"h-4 w-4\" }) })] })] }) }, connection.id));\n                })) }), _jsx(Modal, { isOpen: showForm, onClose: () => {\n                    setShowForm(false);\n                    setEditingConnection(null);\n                }, title: editingConnection ? '编辑连接' : '新建连接', size: \"lg\", children: _jsx(ConnectionForm, { connection: editingConnection || undefined, onSave: handleSaveConnection, onCancel: () => {\n                        setShowForm(false);\n                        setEditingConnection(null);\n                    } }) }), _jsx(Modal, { isOpen: !!showDeleteConfirm, onClose: () => setShowDeleteConfirm(null), title: \"\\u786E\\u8BA4\\u5220\\u9664\", children: _jsxs(\"div\", { className: \"space-y-4\", children: [_jsx(\"p\", { className: \"text-neutral-600 dark:text-dark-text-secondary\", children: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u8FDE\\u63A5\\u5417\\uFF1F\\u6B64\\u64CD\\u4F5C\\u65E0\\u6CD5\\u64A4\\u9500\\u3002\" }), _jsxs(\"div\", { className: \"flex items-center justify-end space-x-3\", children: [_jsx(Button, { variant: \"secondary\", onClick: () => setShowDeleteConfirm(null), children: \"\\u53D6\\u6D88\" }), _jsx(Button, { variant: \"danger\", onClick: () => showDeleteConfirm && handleDeleteConnection(showDeleteConfirm), children: \"\\u5220\\u9664\" })] })] }) }), _jsx(Modal, { isOpen: showHistoryModal, onClose: () => setShowHistoryModal(false), title: \"\\u8FDE\\u63A5\\u5386\\u53F2\", size: \"lg\", children: _jsxs(\"div\", { className: \"space-y-4\", children: [_jsxs(\"div\", { className: \"flex items-center justify-between\", children: [_jsx(\"p\", { className: \"text-sm text-neutral-600 dark:text-dark-text-secondary\", children: \"\\u663E\\u793A\\u6700\\u8FD1\\u7684\\u8FDE\\u63A5\\u8BB0\\u5F55\\uFF0C\\u5305\\u62EC\\u6210\\u529F\\u548C\\u5931\\u8D25\\u7684\\u8FDE\\u63A5\\u5C1D\\u8BD5\" }), _jsxs(Button, { variant: \"secondary\", size: \"sm\", onClick: handleClearHistory, className: \"text-red-600 hover:text-red-700\", children: [_jsx(Trash, { className: \"h-4 w-4 mr-1\" }), \"\\u6E05\\u7A7A\\u5386\\u53F2\"] })] }), connectionHistory.length === 0 ? (_jsxs(\"div\", { className: \"text-center py-8\", children: [_jsx(History, { className: \"h-12 w-12 text-neutral-400 mx-auto mb-4\" }), _jsx(\"p\", { className: \"text-neutral-500 dark:text-dark-text-secondary\", children: \"\\u6682\\u65E0\\u8FDE\\u63A5\\u5386\\u53F2\\u8BB0\\u5F55\" })] })) : (_jsx(\"div\", { className: \"space-y-2 max-h-96 overflow-y-auto\", children: connectionHistory.map((historyItem) => (_jsxs(\"div\", { className: \"flex items-center justify-between p-3 bg-neutral-50 dark:bg-dark-tertiary rounded-lg\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-3\", children: [_jsx(\"div\", { className: cn('w-2 h-2 rounded-full', historyItem.success ? 'bg-green-500' : 'bg-red-500') }), _jsxs(\"div\", { children: [_jsxs(\"div\", { className: \"font-medium text-sm text-neutral-900 dark:text-dark-text-primary\", children: [historyItem.host, \":\", historyItem.port, historyItem.ssl && (_jsx(\"span\", { className: \"ml-2 text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded\", children: \"SSL\" }))] }), _jsxs(\"div\", { className: \"text-xs text-neutral-500 dark:text-dark-text-secondary\", children: [historyItem.username && `用户: ${historyItem.username} • `, \"\\u4F7F\\u7528 \", historyItem.useCount, \" \\u6B21\", historyItem.connectionTime && ` • ${historyItem.connectionTime}ms`] })] })] }), _jsx(\"div\", { className: \"text-xs text-neutral-400\", children: historyItem.lastUsed.toLocaleString() })] }, historyItem.id))) }))] }) })] }));\n};\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { useState, useEffect } from 'react';\nimport { WifiOff, Loader2, AlertCircle, CheckCircle, Clock, Zap } from 'lucide-react';\nimport { intelligentConnectionService } from '../../services/intelligentConnection';\nimport { cn } from '../../utils/cn';\nexport const ConnectionStatusIndicator = ({ connectionId, showDetails = false, size = 'md', className, }) => {\n    const [status, setStatus] = useState(intelligentConnectionService.getConnectionStatus(connectionId));\n    useEffect(() => {\n        const unsubscribe = intelligentConnectionService.subscribeToStatusUpdates((newStatus) => setStatus(newStatus));\n        return unsubscribe;\n    }, []);\n    const getStatusIcon = () => {\n        const iconClass = cn(size === 'sm' && 'h-3 w-3', size === 'md' && 'h-4 w-4', size === 'lg' && 'h-5 w-5');\n        switch (status.status) {\n            case 'connected':\n                return _jsx(CheckCircle, { className: cn(iconClass, 'text-green-500') });\n            case 'connecting':\n            case 'testing':\n                return _jsx(Loader2, { className: cn(iconClass, 'text-blue-500 animate-spin') });\n            case 'error':\n                return _jsx(AlertCircle, { className: cn(iconClass, 'text-red-500') });\n            case 'disconnected':\n            default:\n                return _jsx(WifiOff, { className: cn(iconClass, 'text-gray-400') });\n        }\n    };\n    const getStatusColor = () => {\n        switch (status.healthColor) {\n            case 'green':\n                return 'text-green-600 bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800 dark:text-green-400';\n            case 'yellow':\n                return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-400';\n            case 'red':\n                return 'text-red-600 bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800 dark:text-red-400';\n            case 'blue':\n                return 'text-blue-600 bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-400';\n            case 'gray':\n            default:\n                return 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-900/20 dark:border-gray-800 dark:text-gray-400';\n        }\n    };\n    const getPulseAnimation = () => {\n        if (status.status === 'connecting' || status.status === 'testing') {\n            return 'animate-pulse';\n        }\n        return '';\n    };\n    if (!showDetails) {\n        return (_jsxs(\"div\", { className: cn('flex items-center space-x-2', className), children: [_jsxs(\"div\", { className: cn('relative', getPulseAnimation()), children: [getStatusIcon(), (status.status === 'connecting' || status.status === 'testing') && (_jsx(\"div\", { className: \"absolute inset-0 rounded-full border-2 border-blue-300 animate-ping\" }))] }), _jsx(\"span\", { className: cn('text-sm font-medium', status.healthColor === 'green' && 'text-green-600 dark:text-green-400', status.healthColor === 'yellow' && 'text-yellow-600 dark:text-yellow-400', status.healthColor === 'red' && 'text-red-600 dark:text-red-400', status.healthColor === 'blue' && 'text-blue-600 dark:text-blue-400', status.healthColor === 'gray' && 'text-gray-600 dark:text-gray-400'), children: status.label })] }));\n    }\n    return (_jsxs(\"div\", { className: cn('flex items-center justify-between p-3 rounded-lg border transition-all duration-200', getStatusColor(), getPulseAnimation(), className), children: [_jsxs(\"div\", { className: \"flex items-center space-x-3\", children: [_jsxs(\"div\", { className: \"relative\", children: [getStatusIcon(), (status.status === 'connecting' || status.status === 'testing') && (_jsx(\"div\", { className: \"absolute inset-0 rounded-full border-2 border-current opacity-30 animate-ping\" }))] }), _jsxs(\"div\", { className: \"flex-1\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-2\", children: [_jsx(\"span\", { className: cn('font-medium', size === 'sm' && 'text-sm', size === 'md' && 'text-base', size === 'lg' && 'text-lg'), children: status.label }), status.responseTime && (_jsxs(\"div\", { className: \"flex items-center space-x-1 text-xs opacity-75\", children: [_jsx(Zap, { className: \"h-3 w-3\" }), _jsxs(\"span\", { children: [status.responseTime, \"ms\"] })] }))] }), status.lastChecked && (_jsxs(\"div\", { className: \"flex items-center space-x-1 text-xs opacity-75 mt-1\", children: [_jsx(Clock, { className: \"h-3 w-3\" }), _jsxs(\"span\", { children: [\"\\u6700\\u540E\\u68C0\\u67E5: \", status.lastChecked.toLocaleTimeString()] })] }))] })] }), _jsx(\"div\", { className: cn('w-2 h-2 rounded-full', status.healthColor === 'green' && 'bg-green-500', status.healthColor === 'yellow' && 'bg-yellow-500', status.healthColor === 'red' && 'bg-red-500', status.healthColor === 'blue' && 'bg-blue-500', status.healthColor === 'gray' && 'bg-gray-400', (status.status === 'connecting' || status.status === 'testing') && 'animate-pulse') })] }));\n};\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { useState } from 'react';\nimport { Play, CheckCircle, XCircle, Loader2, Wifi, Shield, Database, Clock, Zap } from 'lucide-react';\nimport { intelligentConnectionService } from '../../services/intelligentConnection';\nimport { Button } from '../UI/Button';\nimport { cn } from '../../utils/cn';\nexport const ConnectionTestFeedback = ({ connection, onTestComplete, className, }) => {\n    const [isTestingConnection, setIsTestingConnection] = useState(false);\n    const [testFeedback, setTestFeedback] = useState(null);\n    const [testResult, setTestResult] = useState(null);\n    const handleTestConnection = async () => {\n        if (!connection.host || !connection.port) {\n            return;\n        }\n        setIsTestingConnection(true);\n        setTestFeedback(null);\n        setTestResult(null);\n        try {\n            const result = await intelligentConnectionService.testConnectionWithFeedback(connection, (feedback) => {\n                setTestFeedback(feedback);\n            });\n            setTestResult({\n                success: result.success,\n                error: result.error,\n            });\n            onTestComplete?.(result.success, result.error);\n        }\n        catch (error) {\n            const errorMessage = error instanceof Error ? error.message : '测试连接时发生未知错误';\n            setTestResult({\n                success: false,\n                error: errorMessage,\n            });\n            onTestComplete?.(false, errorMessage);\n        }\n        finally {\n            setIsTestingConnection(false);\n        }\n    };\n    const getStageIcon = (stage) => {\n        const iconClass = 'h-4 w-4';\n        switch (stage) {\n            case 'connecting':\n                return _jsx(Wifi, { className: cn(iconClass, 'text-blue-500') });\n            case 'authenticating':\n                return _jsx(Shield, { className: cn(iconClass, 'text-yellow-500') });\n            case 'fetching_info':\n                return _jsx(Database, { className: cn(iconClass, 'text-purple-500') });\n            case 'complete':\n                return _jsx(CheckCircle, { className: cn(iconClass, 'text-green-500') });\n            case 'error':\n                return _jsx(XCircle, { className: cn(iconClass, 'text-red-500') });\n            default:\n                return _jsx(Loader2, { className: cn(iconClass, 'text-gray-500 animate-spin') });\n        }\n    };\n    const getProgressColor = (progress) => {\n        if (progress >= 100)\n            return 'bg-green-500';\n        if (progress >= 75)\n            return 'bg-blue-500';\n        if (progress >= 50)\n            return 'bg-yellow-500';\n        return 'bg-gray-400';\n    };\n    return (_jsxs(\"div\", { className: cn('space-y-4', className), children: [_jsx(Button, { onClick: handleTestConnection, loading: isTestingConnection, disabled: !connection.host || !connection.port || isTestingConnection, className: \"w-full\", variant: \"secondary\", children: isTestingConnection ? (_jsxs(\"div\", { className: \"flex items-center space-x-2\", children: [_jsx(Loader2, { className: \"h-4 w-4 animate-spin\" }), _jsx(\"span\", { children: \"\\u6D4B\\u8BD5\\u8FDE\\u63A5\\u4E2D...\" })] })) : (_jsxs(\"div\", { className: \"flex items-center space-x-2\", children: [_jsx(Play, { className: \"h-4 w-4\" }), _jsx(\"span\", { children: \"\\u6D4B\\u8BD5\\u8FDE\\u63A5\" })] })) }), testFeedback && (_jsxs(\"div\", { className: \"space-y-3 p-4 bg-neutral-50 dark:bg-dark-tertiary rounded-lg border\", children: [_jsxs(\"div\", { className: \"flex items-center justify-between\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-2\", children: [getStageIcon(testFeedback.stage), _jsx(\"span\", { className: \"text-sm font-medium text-neutral-700 dark:text-dark-text-primary\", children: testFeedback.message })] }), testFeedback.duration && (_jsxs(\"div\", { className: \"flex items-center space-x-1 text-xs text-neutral-500\", children: [_jsx(Clock, { className: \"h-3 w-3\" }), _jsxs(\"span\", { children: [testFeedback.duration, \"ms\"] })] }))] }), _jsx(\"div\", { className: \"w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2\", children: _jsx(\"div\", { className: cn('h-2 rounded-full transition-all duration-300', getProgressColor(testFeedback.progress), testFeedback.stage === 'error' && 'bg-red-500'), style: { width: `${testFeedback.progress}%` } }) }), _jsxs(\"div\", { className: \"flex justify-between items-center text-xs text-neutral-500\", children: [_jsx(\"span\", { children: \"\\u8FDB\\u5EA6\" }), _jsxs(\"span\", { children: [testFeedback.progress, \"%\"] })] }), testFeedback.error && (_jsx(\"div\", { className: \"mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-sm text-red-700 dark:text-red-400\", children: testFeedback.error }))] })), testResult && !isTestingConnection && (_jsx(\"div\", { className: cn('p-4 rounded-lg border transition-all duration-200', testResult.success\n                    ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'\n                    : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'), children: _jsxs(\"div\", { className: \"flex items-start space-x-3\", children: [testResult.success ? (_jsx(CheckCircle, { className: \"h-5 w-5 text-green-500 flex-shrink-0 mt-0.5\" })) : (_jsx(XCircle, { className: \"h-5 w-5 text-red-500 flex-shrink-0 mt-0.5\" })), _jsxs(\"div\", { className: \"flex-1\", children: [_jsx(\"h4\", { className: cn('font-medium text-sm', testResult.success\n                                        ? 'text-green-800 dark:text-green-200'\n                                        : 'text-red-800 dark:text-red-200'), children: testResult.success ? '连接测试成功！' : '连接测试失败' }), testResult.error && (_jsx(\"p\", { className: cn('text-xs mt-1', testResult.success\n                                        ? 'text-green-600 dark:text-green-300'\n                                        : 'text-red-600 dark:text-red-300'), children: testResult.error })), testResult.success && testFeedback?.duration && (_jsxs(\"div\", { className: \"flex items-center space-x-4 mt-2 text-xs text-green-600 dark:text-green-300\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-1\", children: [_jsx(Zap, { className: \"h-3 w-3\" }), _jsxs(\"span\", { children: [\"\\u54CD\\u5E94\\u65F6\\u95F4: \", testFeedback.duration, \"ms\"] })] }), _jsxs(\"div\", { className: \"flex items-center space-x-1\", children: [_jsx(Clock, { className: \"h-3 w-3\" }), _jsxs(\"span\", { children: [\"\\u6D4B\\u8BD5\\u5B8C\\u6210\\u4E8E: \", new Date().toLocaleTimeString()] })] })] }))] })] }) }))] }));\n};\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Server, Clock, Zap, Globe, ChevronDown, Check, History, Wifi } from 'lucide-react';\nimport { intelligentConnectionService } from '../../services/intelligentConnection';\nimport { cn } from '../../utils/cn';\nexport const SmartConnectionInput = ({ value, onChange, onSuggestionSelect, placeholder = \"输入主机地址...\", className, }) => {\n    const [suggestions, setSuggestions] = useState([]);\n    const [showSuggestions, setShowSuggestions] = useState(false);\n    const [selectedIndex, setSelectedIndex] = useState(-1);\n    const [isLoading, setIsLoading] = useState(false);\n    const inputRef = useRef(null);\n    const suggestionsRef = useRef(null);\n    useEffect(() => {\n        const loadSuggestions = async () => {\n            if (value.length >= 1) {\n                setIsLoading(true);\n                try {\n                    const results = await intelligentConnectionService.getSuggestions(value);\n                    setSuggestions(results);\n                    setShowSuggestions(true);\n                    setSelectedIndex(-1);\n                }\n                catch (error) {\n                    console.warn('Failed to load suggestions:', error);\n                    setSuggestions([]);\n                }\n                finally {\n                    setIsLoading(false);\n                }\n            }\n            else {\n                setSuggestions([]);\n                setShowSuggestions(false);\n            }\n        };\n        const debounceTimer = setTimeout(loadSuggestions, 200);\n        return () => clearTimeout(debounceTimer);\n    }, [value]);\n    const handleInputChange = (e) => {\n        onChange(e.target.value);\n    };\n    const handleKeyDown = (e) => {\n        if (!showSuggestions || suggestions.length === 0)\n            return;\n        switch (e.key) {\n            case 'ArrowDown':\n                e.preventDefault();\n                setSelectedIndex(prev => prev < suggestions.length - 1 ? prev + 1 : 0);\n                break;\n            case 'ArrowUp':\n                e.preventDefault();\n                setSelectedIndex(prev => prev > 0 ? prev - 1 : suggestions.length - 1);\n                break;\n            case 'Enter':\n                e.preventDefault();\n                if (selectedIndex >= 0) {\n                    handleSuggestionClick(suggestions[selectedIndex]);\n                }\n                break;\n            case 'Escape':\n                setShowSuggestions(false);\n                setSelectedIndex(-1);\n                break;\n        }\n    };\n    const handleSuggestionClick = (suggestion) => {\n        onChange(suggestion.host);\n        onSuggestionSelect(suggestion);\n        setShowSuggestions(false);\n        setSelectedIndex(-1);\n    };\n    const handleInputFocus = () => {\n        if (suggestions.length > 0) {\n            setShowSuggestions(true);\n        }\n    };\n    const handleInputBlur = () => {\n        // Delay hiding suggestions to allow clicking\n        setTimeout(() => {\n            setShowSuggestions(false);\n            setSelectedIndex(-1);\n        }, 200);\n    };\n    const getSuggestionIcon = (type) => {\n        switch (type) {\n            case 'history':\n                return _jsx(History, { className: \"h-4 w-4 text-blue-500\" });\n            case 'local':\n                return _jsx(Wifi, { className: \"h-4 w-4 text-green-500\" });\n            case 'common':\n                return _jsx(Globe, { className: \"h-4 w-4 text-gray-500\" });\n            default:\n                return _jsx(Server, { className: \"h-4 w-4 text-gray-500\" });\n        }\n    };\n    const getConfidenceColor = (confidence) => {\n        if (confidence >= 0.8)\n            return 'text-green-600';\n        if (confidence >= 0.6)\n            return 'text-yellow-600';\n        return 'text-gray-600';\n    };\n    return (_jsxs(\"div\", { className: cn('relative', className), children: [_jsxs(\"div\", { className: \"relative\", children: [_jsx(\"input\", { ref: inputRef, type: \"text\", value: value, onChange: handleInputChange, onKeyDown: handleKeyDown, onFocus: handleInputFocus, onBlur: handleInputBlur, placeholder: placeholder, className: cn('w-full px-4 py-2 pl-10 pr-10 text-sm border border-neutral-300 rounded-lg', 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent', 'dark:bg-dark-secondary dark:border-neutral-600 dark:text-dark-text-primary', 'dark:placeholder-dark-text-secondary') }), _jsx(Server, { className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400\" }), isLoading && (_jsx(\"div\", { className: \"absolute right-3 top-1/2 transform -translate-y-1/2\", children: _jsx(\"div\", { className: \"animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full\" }) })), showSuggestions && suggestions.length > 0 && (_jsx(ChevronDown, { className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400\" }))] }), showSuggestions && suggestions.length > 0 && (_jsx(\"div\", { ref: suggestionsRef, className: cn('absolute z-50 w-full mt-1 bg-white dark:bg-dark-secondary border border-neutral-200 dark:border-neutral-600', 'rounded-lg shadow-lg max-h-64 overflow-y-auto'), children: suggestions.map((suggestion, index) => (_jsxs(\"div\", { onClick: () => handleSuggestionClick(suggestion), className: cn('flex items-center justify-between px-4 py-3 cursor-pointer transition-colors', 'hover:bg-neutral-50 dark:hover:bg-dark-tertiary', selectedIndex === index && 'bg-blue-50 dark:bg-blue-900/20', index !== suggestions.length - 1 && 'border-b border-neutral-100 dark:border-neutral-700'), children: [_jsxs(\"div\", { className: \"flex items-center space-x-3 flex-1 min-w-0\", children: [getSuggestionIcon(suggestion.type), _jsxs(\"div\", { className: \"flex-1 min-w-0\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-2\", children: [_jsxs(\"span\", { className: \"font-medium text-neutral-900 dark:text-dark-text-primary truncate\", children: [suggestion.host, \":\", suggestion.port] }), suggestion.ssl && (_jsx(\"span\", { className: \"text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded\", children: \"SSL\" }))] }), _jsxs(\"div\", { className: \"flex items-center space-x-2 mt-1\", children: [_jsx(\"span\", { className: \"text-xs text-neutral-500 dark:text-dark-text-secondary truncate\", children: suggestion.description }), suggestion.lastUsed && (_jsxs(\"div\", { className: \"flex items-center space-x-1 text-xs text-neutral-400\", children: [_jsx(Clock, { className: \"h-3 w-3\" }), _jsx(\"span\", { children: suggestion.lastUsed.toLocaleDateString() })] }))] }), suggestion.username && (_jsxs(\"div\", { className: \"text-xs text-neutral-400 mt-1\", children: [\"\\u7528\\u6237: \", suggestion.username] }))] })] }), _jsxs(\"div\", { className: \"flex items-center space-x-2 ml-2\", children: [suggestion.useCount && suggestion.useCount > 1 && (_jsxs(\"span\", { className: \"text-xs text-neutral-400\", children: [suggestion.useCount, \"\\u6B21\"] })), _jsxs(\"div\", { className: cn('flex items-center space-x-1 text-xs', getConfidenceColor(suggestion.confidence)), children: [_jsx(Zap, { className: \"h-3 w-3\" }), _jsxs(\"span\", { children: [Math.round(suggestion.confidence * 100), \"%\"] })] }), selectedIndex === index && (_jsx(Check, { className: \"h-4 w-4 text-blue-500\" }))] })] }, suggestion.id))) }))] }));\n};\n"], "names": [], "sourceRoot": ""}