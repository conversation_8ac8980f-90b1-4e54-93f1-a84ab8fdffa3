<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' http://localhost:3000 http://localhost:9200 https://localhost:9200; script-src 'self' 'unsafe-inline' 'unsafe-eval' http://localhost:3000; style-src 'self' 'unsafe-inline' http://localhost:3000; img-src 'self' data: http://localhost:3000; connect-src 'self' http://localhost:3000 http://localhost:9200 https://localhost:9200 ws://localhost:3000;">
    <title>ES Client</title>
    <script>
      // Define global variable for Node.js compatibility
      window.global = window.globalThis = window;

      // Comprehensive polyfills for Node.js modules in browser environment
      
      // EventEmitter polyfill
      function EventEmitter() {
        EventEmitter.init.call(this);
      }
      
      // Initialize EventEmitter properties
      EventEmitter.prototype._events = undefined;
      EventEmitter.prototype._eventsCount = 0;
      EventEmitter.prototype._maxListeners = undefined;
      
      // EventEmitter init method
      EventEmitter.init = function() {
        if (this._events === undefined ||
            this._events === Object.getPrototypeOf(this)._events) {
          this._events = Object.create(null);
          this._eventsCount = 0;
        }
        this._maxListeners = this._maxListeners || undefined;
      };
      
      EventEmitter.prototype.on = function(event, listener) {
        if (!this._events) this._events = {};
        if (!this._events[event]) {
          this._events[event] = [];
        }
        this._events[event].push(listener);
        return this;
      };
      
      EventEmitter.prototype.emit = function(event) {
        var args = Array.prototype.slice.call(arguments, 1);
        if (!this._events) this._events = {};
        var listeners = this._events[event];
        if (listeners) {
          for (var i = 0; i < listeners.length; i++) {
            listeners[i].apply(this, args);
          }
        }
        return this;
      };
      
      EventEmitter.prototype.removeListener = function(event, listener) {
        if (!this._events) this._events = {};
        var listeners = this._events[event];
        if (listeners) {
          var index = listeners.indexOf(listener);
          if (index !== -1) {
            listeners.splice(index, 1);
          }
        }
        return this;
      };
      
      // Make EventEmitter available globally
      window.EventEmitter = EventEmitter;
      if (typeof globalThis !== 'undefined') {
        globalThis.EventEmitter = EventEmitter;
      }

      // Also make it available as a module export for webpack
      if (typeof module !== 'undefined' && module.exports) {
        module.exports = EventEmitter;
        module.exports.EventEmitter = EventEmitter;
      }
      
      // Polyfill require function
      if (typeof window !== 'undefined' && typeof window.require === 'undefined') {
        window.require = function(id) {
          // For events module, return our EventEmitter
          if (id === 'events') {
            // Create module object that mimics Node.js events module
            var eventsModule = {
              EventEmitter: EventEmitter
            };
            // Set the default export to be the EventEmitter constructor
            eventsModule.default = EventEmitter;
            // Add backwards compatibility
            eventsModule.EventEmitter = EventEmitter;
            return eventsModule;
          }
          
          // For Monaco Editor workers and common modules, return empty object
          if (id.includes('monaco') || id.includes('worker') || id.includes('vs/')) {
            return {};
          }
          
          // For path module, return a basic implementation
          if (id === 'path') {
            return {
              join: function() { return Array.prototype.slice.call(arguments).join('/'); },
              resolve: function() { return Array.prototype.slice.call(arguments).join('/'); },
              dirname: function(path) { return path.split('/').slice(0, -1).join('/'); },
              basename: function(path) { return path.split('/').pop() || ''; }
            };
          }
          
          // For other modules, return empty object
          console.warn('Module "' + id + '" is not available in the renderer process');
          return {};
        };
        
        // Add require.resolve for compatibility
        window.require.resolve = function(id) {
          return id;
        };
        
        // Also set global require
        if (typeof globalThis !== 'undefined') {
          globalThis.require = window.require;
        }
      }
    </script>
  <script defer src="renderer.js"></script></head>
<body class="bg-neutral-50 dark:bg-dark-primary text-neutral-900 dark:text-dark-text-primary">
  <div id="root"></div>
</body>
</html>