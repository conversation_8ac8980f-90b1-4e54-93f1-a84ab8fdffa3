"use strict";
(global["webpackChunkes_client"] = global["webpackChunkes_client"] || []).push([["src_renderer_components_DesignSystemShowcase_tsx"],{

/***/ "./node_modules/lucide-react/dist/esm/icons/heart.mjs":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/heart.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Heart)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const Heart = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("Heart", [
  [
    "path",
    {
      d: "M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",
      key: "c3ymky"
    }
  ]
]);


//# sourceMappingURL=heart.mjs.map


/***/ }),

/***/ "./src/renderer/components/DesignSystemShowcase.tsx":
/*!**********************************************************!*\
  !*** ./src/renderer/components/DesignSystemShowcase.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DesignSystemShowcase: () => (/* binding */ DesignSystemShowcase)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _UI__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UI */ "./src/renderer/components/UI/index.ts");
/* harmony import */ var _hooks_useToast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../hooks/useToast */ "./src/renderer/hooks/useToast.ts");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/heart.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/search.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/settings.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/star.mjs");





const DesignSystemShowcase = () => {
    const [switchValue, setSwitchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [dropdownValue, setDropdownValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const { toasts, success, error, warning, info, removeToast } = (0,_hooks_useToast__WEBPACK_IMPORTED_MODULE_3__.useToast)();
    const dropdownOptions = [
        { value: 'option1', label: '选项 1', icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"], { className: "h-4 w-4" }) },
        { value: 'option2', label: '选项 2', icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], { className: "h-4 w-4" }) },
        {
            value: 'option3',
            label: '选项 3',
            icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"], { className: "h-4 w-4" }),
        },
    ];
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "p-6 space-y-8 max-w-4xl mx-auto", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1", { className: "text-3xl font-bold text-neutral-900 dark:text-dark-text-primary mb-2", children: "\u8BBE\u8BA1\u7CFB\u7EDF\u5C55\u793A" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-neutral-600 dark:text-dark-text-secondary", children: "\u82F9\u679C\u98CE\u683C\u7684 Elasticsearch \u5BA2\u6237\u7AEF\u8BBE\u8BA1\u7CFB\u7EDF" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI__WEBPACK_IMPORTED_MODULE_2__.Card, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardHeader, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardTitle, { children: "\u6309\u94AE\u7EC4\u4EF6" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardContent, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-wrap gap-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { variant: "primary", children: "\u4E3B\u8981\u6309\u94AE" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { variant: "secondary", children: "\u6B21\u8981\u6309\u94AE" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { variant: "danger", children: "\u5371\u9669\u6309\u94AE" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { variant: "ghost", children: "\u5E7D\u7075\u6309\u94AE" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-wrap gap-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { size: "sm", children: "\u5C0F\u6309\u94AE" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { size: "md", children: "\u4E2D\u7B49\u6309\u94AE" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { size: "lg", children: "\u5927\u6309\u94AE" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-wrap gap-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { loading: true, children: "\u52A0\u8F7D\u4E2D" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { disabled: true, children: "\u7981\u7528\u6309\u94AE" })] })] }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI__WEBPACK_IMPORTED_MODULE_2__.Card, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardHeader, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardTitle, { children: "\u8868\u5355\u7EC4\u4EF6" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardContent, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Input, { label: "\u7528\u6237\u540D", placeholder: "\u8BF7\u8F93\u5165\u7528\u6237\u540D", value: inputValue, onChange: e => setInputValue(e.target.value), leftIcon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], {}) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Input, { label: "\u5BC6\u7801", type: "password", placeholder: "\u8BF7\u8F93\u5165\u5BC6\u7801", error: "\u5BC6\u7801\u957F\u5EA6\u81F3\u5C118\u4F4D" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Textarea, { label: "\u63CF\u8FF0", placeholder: "\u8BF7\u8F93\u5165\u63CF\u8FF0\u4FE1\u606F", helperText: "\u6700\u591A500\u4E2A\u5B57\u7B26" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Dropdown, { label: "\u9009\u62E9\u9009\u9879", options: dropdownOptions, value: dropdownValue, onChange: setDropdownValue, placeholder: "\u8BF7\u9009\u62E9\u4E00\u4E2A\u9009\u9879" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Switch, { label: "\u542F\u7528\u901A\u77E5", description: "\u63A5\u6536\u7CFB\u7EDF\u901A\u77E5\u548C\u66F4\u65B0", checked: switchValue, onChange: e => setSwitchValue(e.target.checked) })] }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI__WEBPACK_IMPORTED_MODULE_2__.Card, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardHeader, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardTitle, { children: "\u5FBD\u7AE0\u548C\u72B6\u6001" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardContent, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-wrap gap-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Badge, { variant: "default", children: "\u9ED8\u8BA4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Badge, { variant: "primary", children: "\u4E3B\u8981" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Badge, { variant: "success", children: "\u6210\u529F" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Badge, { variant: "warning", children: "\u8B66\u544A" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Badge, { variant: "error", children: "\u9519\u8BEF" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Badge, { variant: "info", children: "\u4FE1\u606F" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-wrap gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.HealthIndicator, { status: "green" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.HealthIndicator, { status: "yellow" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.HealthIndicator, { status: "red" })] })] }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI__WEBPACK_IMPORTED_MODULE_2__.Card, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardHeader, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardTitle, { children: "\u52A0\u8F7D\u72B6\u6001" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardContent, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Spinner, { size: "sm" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Spinner, { size: "md" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Spinner, { size: "lg" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Spinner, { size: "xl" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Loading, { text: "\u6B63\u5728\u52A0\u8F7D\u6570\u636E..." })] }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI__WEBPACK_IMPORTED_MODULE_2__.Card, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardHeader, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardTitle, { children: "\u4E3B\u9898\u5207\u6362" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardContent, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-neutral-600 dark:text-dark-text-secondary", children: "\u6309\u94AE\u6A21\u5F0F:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.ThemeToggle, { variant: "button" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-neutral-600 dark:text-dark-text-secondary", children: "\u4E0B\u62C9\u6A21\u5F0F:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.ThemeToggle, { variant: "dropdown" })] })] }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI__WEBPACK_IMPORTED_MODULE_2__.Card, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardHeader, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardTitle, { children: "\u5DE5\u5177\u63D0\u793A" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardContent, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Tooltip, { content: "\u8FD9\u662F\u4E00\u4E2A\u9876\u90E8\u63D0\u793A", placement: "top", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { variant: "secondary", children: "\u9876\u90E8\u63D0\u793A" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Tooltip, { content: "\u8FD9\u662F\u4E00\u4E2A\u5E95\u90E8\u63D0\u793A", placement: "bottom", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { variant: "secondary", children: "\u5E95\u90E8\u63D0\u793A" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Tooltip, { content: "\u8FD9\u662F\u4E00\u4E2A\u5DE6\u4FA7\u63D0\u793A", placement: "left", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { variant: "secondary", children: "\u5DE6\u4FA7\u63D0\u793A" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Tooltip, { content: "\u8FD9\u662F\u4E00\u4E2A\u53F3\u4FA7\u63D0\u793A", placement: "right", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { variant: "secondary", children: "\u53F3\u4FA7\u63D0\u793A" }) })] }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI__WEBPACK_IMPORTED_MODULE_2__.Card, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardHeader, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardTitle, { children: "\u6A21\u6001\u6846\u548C\u901A\u77E5" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardContent, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-4", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-wrap gap-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { onClick: () => setIsModalOpen(true), children: "\u6253\u5F00\u6A21\u6001\u6846" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { onClick: () => success('成功', '操作已成功完成'), children: "\u6210\u529F\u901A\u77E5" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { onClick: () => error('错误', '操作失败，请重试'), children: "\u9519\u8BEF\u901A\u77E5" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { onClick: () => warning('警告', '请注意此操作的风险'), children: "\u8B66\u544A\u901A\u77E5" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { onClick: () => info('信息', '这是一条信息提示'), children: "\u4FE1\u606F\u901A\u77E5" })] }) }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI__WEBPACK_IMPORTED_MODULE_2__.Card, { variant: "default", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardHeader, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardTitle, { children: "\u9ED8\u8BA4\u5361\u7247" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardContent, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-neutral-600 dark:text-dark-text-secondary", children: "\u8FD9\u662F\u4E00\u4E2A\u9ED8\u8BA4\u6837\u5F0F\u7684\u5361\u7247\u7EC4\u4EF6\u3002" }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI__WEBPACK_IMPORTED_MODULE_2__.Card, { variant: "elevated", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardHeader, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardTitle, { children: "\u60AC\u6D6E\u5361\u7247" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardContent, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-neutral-600 dark:text-dark-text-secondary", children: "\u8FD9\u662F\u4E00\u4E2A\u5E26\u9634\u5F71\u7684\u60AC\u6D6E\u5361\u7247\uFF0C\u60AC\u505C\u65F6\u4F1A\u6709\u63D0\u5347\u6548\u679C\u3002" }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI__WEBPACK_IMPORTED_MODULE_2__.Card, { variant: "outlined", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardHeader, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardTitle, { children: "\u8FB9\u6846\u5361\u7247" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.CardContent, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-neutral-600 dark:text-dark-text-secondary", children: "\u8FD9\u662F\u4E00\u4E2A\u5E26\u8FB9\u6846\u7684\u5361\u7247\u7EC4\u4EF6\u3002" }) })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI__WEBPACK_IMPORTED_MODULE_2__.Modal, { isOpen: isModalOpen, onClose: () => setIsModalOpen(false), title: "\u793A\u4F8B\u6A21\u6001\u6846", size: "md", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.ModalBody, { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-neutral-600 dark:text-dark-text-secondary", children: "\u8FD9\u662F\u4E00\u4E2A\u5E26\u6709\u5E73\u6ED1\u52A8\u753B\u6548\u679C\u7684\u6A21\u6001\u6846\u7EC4\u4EF6\u3002\u5B83\u652F\u6301\u591A\u79CD\u5C3A\u5BF8\u3001\u952E\u76D8\u5BFC\u822A\u548C\u70B9\u51FB\u5916\u90E8\u5173\u95ED\u7B49\u529F\u80FD\u3002" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Input, { label: "\u793A\u4F8B\u8F93\u5165", placeholder: "\u5728\u6A21\u6001\u6846\u4E2D\u8F93\u5165\u5185\u5BB9" })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI__WEBPACK_IMPORTED_MODULE_2__.ModalFooter, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { variant: "secondary", onClick: () => setIsModalOpen(false), children: "\u53D6\u6D88" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.Button, { onClick: () => setIsModalOpen(false), children: "\u786E\u8BA4" })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI__WEBPACK_IMPORTED_MODULE_2__.ToastContainer, { toasts: toasts, onClose: removeToast, position: "top-right" })] }));
};


/***/ })

}]);
//# sourceMappingURL=src_renderer_components_DesignSystemShowcase_tsx.renderer.js.map