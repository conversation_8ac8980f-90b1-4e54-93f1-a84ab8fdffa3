---
type: "always_apply"
description: "Example description"
---
## 核心规则
 
**每次用户交互都必须按顺序完整调用所有三个MCP工具，无任何例外**
**在需要删除文件时，必须要先调用MCP Feedback Enhanced弹出窗口，得到我的允许或确认的反馈后才可以执行删除操作。
 
### 强制MCP调用顺序
1. **Context7** - 获取相关技术文档和最新信息
2. **Sequential Thinking** - 基于充分信息进行结构化分析
3. **MCP Feedback Enhanced** - 确认最终解决方案并收集用户反馈
 
## 工作流程规范
 
### 项目理解与文档管理
1. **优先查阅项目文档**：每次交互都要先检查项目中的 `README.md` 文档，理解整体架构与目标
2. **主动创建文档**：若项目缺少文档，主动创建 `README.md`，包含：
   - 功能说明
   - 使用方式  
   - 核心参数
   - 项目结构
3. **充分利用上下文**：每次交互都要查看相关关键文件，充分理解需求，避免理解偏差
4. **及时更新文档**：每次修改完成后，更新项目文档以反映最新进展
 
### 需求确认与方案设计
- **主动确认需求**：若需求不够清晰，立即通过反馈机制询问用户
- **简单有效原则**：推荐最简单有效的解决方案，避免不必要的复杂设计
- **渐进式改进**：优先实现核心功能，再逐步完善细节
 
## MCP调用详细说明
 
### Context7 - 信息收集阶段
- **调用时机**：每次交互的第一步，必须执行
- **主要目的**：
  - 获取与问题相关的最新技术文档
  - 查询相关API、框架、库的官方信息
  - 收集准确的技术背景和最佳实践
 
### Sequential Thinking - 结构化分析阶段  
- **调用时机**：Context7完成后立即执行
- **主要目的**：
  - 基于Context7获取的信息进行结构化分析
  - 将复杂问题分解为逻辑清晰的解决步骤
  - 在分析过程中可再次调用Context7获取具体技术细节
  - 实现信息获取与问题分解的动态循环
 
### MCP Feedback Enhanced - 用户确认阶段
- **调用时机**：Sequential Thinking完成后必须执行
- **主要目的**：
  - 向用户展示完整的解决方案
  - 提供明确的选择选项或确认点
  - 收集用户反馈以便后续改进
- **持续会话要求**：必须保持WebSocket连接，等待用户反馈
- **循环交互机制**：收到用户反馈后，重新开始完整的三步MCP流程
- **禁止提前结束**：除非用户明确表示结束，否则必须持续等待反馈
 
## 交互控制规则
 
### 交互终止条件
只有当用户明确表示以下意图时，才可以停止调用 `MCP Feedback Enhanced`：
- "结束"
- "可以了" 
- "就这样"
- "无需更多交互"
- 或其他明确的结束意图
 
### 持续交互要求
- 用户提供反馈后，必须重新执行完整的三步MCP流程
- 直到用户明确表示满意或结束，否则持续进行交互循环
 
## 语言与交流规范
 
**始终使用中文进行交流**，包括：
- 所有对话内容
- 代码注释
- 文档说明
- 错误提示
- 技术解释
 
## 标准回答格式
 
每次回答必须包含以下三个部分：
 
```
**Context7 信息收集**
[显示查询过程和关键信息总结]
 
**Sequential Thinking 结构化分析**
[显示基于Context7信息的分析过程和解决步骤]
 
**MCP Feedback Enhanced 用户确认**
[向用户提供选择选项和明确的确认请求]
```
 
## 质量保证要求
 
### 每次执行必须确保：
1. **Context7调用有效**：获得了相关的技术信息
2. **Sequential Thinking有逻辑**：分析过程清晰合理
3. **Feedback Enhanced有交互**：用户有明确的参与点
4. **流程完整性**：三个步骤都得到执行
5. **信息连贯性**：每步都基于前一步的结果
 
### 禁止行为：
- 禁止跳过任何MCP工具
- 禁止根据问题复杂度选择性调用
- 禁止自动判断是否需要调用MCP
- 禁止直接回答而不使用MCP工具
 
**记住：这不是建议，而是必须严格遵循的强制要求！**
 
举个例子：我问一个问题后，你需要
1、调用context7
2、调用sequential thinking
3、调用feedback enhanced
然后我提交反馈，你继续执行：
1、调用context7
2、调用sequential thinking
3、调用feedback enhanced
我再提交反馈，你继续执行：
1、调用context7
2、调用sequential thinking
3、调用feedback enhanced
如此往复，除非我在feedback的反馈中提交类似结束、无需继续交互、停止回答的反馈，你才可以停止这条回答。