"use strict";
(global["webpackChunkes_client"] = global["webpackChunkes_client"] || []).push([["src_renderer_components_Query_index_ts"],{

/***/ "./node_modules/lucide-react/dist/esm/icons/arrow-down-wide-narrow.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/arrow-down-wide-narrow.mjs ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ ArrowDownWideNarrow)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const ArrowDownWideNarrow = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("ArrowDownWideNarrow", [
  ["path", { d: "m3 16 4 4 4-4", key: "1co6wj" }],
  ["path", { d: "M7 20V4", key: "1yoxec" }],
  ["path", { d: "M11 4h10", key: "1w87gc" }],
  ["path", { d: "M11 8h7", key: "djye34" }],
  ["path", { d: "M11 12h4", key: "q8tih4" }]
]);


//# sourceMappingURL=arrow-down-wide-narrow.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ ArrowUpNarrowWide)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const ArrowUpNarrowWide = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("ArrowUpNarrowWide", [
  ["path", { d: "m3 8 4-4 4 4", key: "11wl7u" }],
  ["path", { d: "M7 4v16", key: "1glfcx" }],
  ["path", { d: "M11 12h4", key: "q8tih4" }],
  ["path", { d: "M11 16h7", key: "uosisv" }],
  ["path", { d: "M11 20h10", key: "jvxblo" }]
]);


//# sourceMappingURL=arrow-up-narrow-wide.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/link.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/link.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Link)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const Link = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("Link", [
  [
    "path",
    {
      d: "M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",
      key: "1cjeqo"
    }
  ],
  [
    "path",
    {
      d: "M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",
      key: "19qd67"
    }
  ]
]);


//# sourceMappingURL=link.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/share-2.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/share-2.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Share2)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const Share2 = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("Share2", [
  ["circle", { cx: "18", cy: "5", r: "3", key: "gq8acd" }],
  ["circle", { cx: "6", cy: "12", r: "3", key: "w7nqdw" }],
  ["circle", { cx: "18", cy: "19", r: "3", key: "1xt0gg" }],
  [
    "line",
    { x1: "8.59", x2: "15.42", y1: "13.51", y2: "17.49", key: "47mynk" }
  ],
  ["line", { x1: "15.41", x2: "8.59", y1: "6.51", y2: "10.49", key: "1n3mei" }]
]);


//# sourceMappingURL=share-2.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/star-off.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/star-off.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ StarOff)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const StarOff = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("StarOff", [
  [
    "path",
    {
      d: "M8.34 8.34 2 9.27l5 4.87L5.82 21 12 17.77 18.18 21l-.59-3.43",
      key: "16m0ql"
    }
  ],
  [
    "path",
    { d: "M18.42 12.76 22 9.27l-6.91-1L12 2l-1.44 2.91", key: "1vt8nq" }
  ],
  ["line", { x1: "2", x2: "22", y1: "2", y2: "22", key: "a6p6uj" }]
]);


//# sourceMappingURL=star-off.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/tag.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/tag.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Tag)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const Tag = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("Tag", [
  [
    "path",
    {
      d: "M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z",
      key: "14b2ls"
    }
  ],
  ["path", { d: "M7 7h.01", key: "7u93v4" }]
]);


//# sourceMappingURL=tag.mjs.map


/***/ }),

/***/ "./src/renderer/components/Query/QueryHistory.tsx":
/*!********************************************************!*\
  !*** ./src/renderer/components/Query/QueryHistory.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ QueryHistory)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/arrow-down-wide-narrow.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/calendar.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/check-circle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/clock.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/copy.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/download.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/hash.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/history.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/link.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/plus.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/rotate-ccw.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/search.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/share-2.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/star-off.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/star.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/tag.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/trash-2.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/trending-up.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/upload.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/x-circle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/x.mjs");
/* harmony import */ var _stores_query__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ../../stores/query */ "./src/renderer/stores/query.ts");
/* harmony import */ var _UI_Button__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ../UI/Button */ "./src/renderer/components/UI/Button.tsx");
/* harmony import */ var _UI_Card__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ../UI/Card */ "./src/renderer/components/UI/Card.tsx");
/* harmony import */ var _UI_Input__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ../UI/Input */ "./src/renderer/components/UI/Input.tsx");
/* harmony import */ var _UI_Badge__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ../UI/Badge */ "./src/renderer/components/UI/Badge.tsx");








function QueryHistory() {
    const { queryHistory, savedQueries, loadHistoryQuery, clearQueryHistory, deleteHistoryItem, saveQuery, toggleQueryFavorite, updateSavedQuery, exportQueries, importQueries, } = (0,_stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore)();
    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [filterStatus, setFilterStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');
    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('date');
    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('desc');
    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
    const [showTagInput, setShowTagInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [newTag, setNewTag] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [showShareDialog, setShowShareDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [showSaveDialog, setShowSaveDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [saveQueryName, setSaveQueryName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [saveQueryDescription, setSaveQueryDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [saveQueryTags, setSaveQueryTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
    // Calculate usage statistics for intelligent sorting
    const usageStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {
        const stats = new Map();
        queryHistory.forEach(item => {
            const queryKey = JSON.stringify(item.query);
            const existing = stats.get(queryKey);
            if (existing) {
                existing.usageCount++;
                existing.lastUsed = new Date(Math.max(existing.lastUsed.getTime(), item.executedAt.getTime()));
                if (item.executionTime) {
                    existing.avgExecutionTime = (existing.avgExecutionTime + item.executionTime) / 2;
                }
            }
            else {
                stats.set(queryKey, {
                    queryId: queryKey,
                    usageCount: 1,
                    lastUsed: item.executedAt,
                    avgExecutionTime: item.executionTime || 0,
                });
            }
        });
        return stats;
    }, [queryHistory]);
    // Get all available tags from saved queries
    const availableTags = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {
        const tags = new Set();
        savedQueries.forEach(query => {
            query.tags.forEach(tag => tags.add(tag));
        });
        return Array.from(tags).sort();
    }, [savedQueries]);
    // Enhanced filtering and sorting
    const filteredHistory = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {
        let filtered = queryHistory.filter(item => {
            // Search filter
            const matchesSearch = item.index.toLowerCase().includes(searchTerm.toLowerCase()) ||
                (item.error && item.error.toLowerCase().includes(searchTerm.toLowerCase())) ||
                JSON.stringify(item.query).toLowerCase().includes(searchTerm.toLowerCase());
            // Status filter
            const matchesStatus = filterStatus === 'all' ||
                (filterStatus === 'success' && item.success) ||
                (filterStatus === 'error' && !item.success) ||
                (filterStatus === 'favorites' && savedQueries.some(sq => sq.isFavorite && JSON.stringify(sq.query) === JSON.stringify(item.query)));
            // Tag filter
            const matchesTags = selectedTags.length === 0 ||
                savedQueries.some(sq => JSON.stringify(sq.query) === JSON.stringify(item.query) &&
                    selectedTags.every(tag => sq.tags.includes(tag)));
            return matchesSearch && matchesStatus && matchesTags;
        });
        // Sort the filtered results
        return filtered.sort((a, b) => {
            let comparison = 0;
            switch (sortBy) {
                case 'date':
                    comparison = new Date(b.executedAt).getTime() - new Date(a.executedAt).getTime();
                    break;
                case 'execution_time':
                    comparison = (b.executionTime || 0) - (a.executionTime || 0);
                    break;
                case 'usage_frequency':
                    const aStats = usageStats.get(JSON.stringify(a.query));
                    const bStats = usageStats.get(JSON.stringify(b.query));
                    comparison = (bStats?.usageCount || 0) - (aStats?.usageCount || 0);
                    break;
                case 'name':
                    const aName = savedQueries.find(sq => JSON.stringify(sq.query) === JSON.stringify(a.query))?.name || 'Unnamed';
                    const bName = savedQueries.find(sq => JSON.stringify(sq.query) === JSON.stringify(b.query))?.name || 'Unnamed';
                    comparison = aName.localeCompare(bName);
                    break;
            }
            return sortOrder === 'desc' ? comparison : -comparison;
        });
    }, [queryHistory, searchTerm, filterStatus, selectedTags, sortBy, sortOrder, usageStats, savedQueries]);
    const handleLoadQuery = (historyId) => {
        loadHistoryQuery(historyId);
    };
    const handleDeleteItem = (historyId) => {
        if (confirm('Are you sure you want to delete this history item?')) {
            deleteHistoryItem(historyId);
        }
    };
    const handleClearHistory = () => {
        if (confirm('Are you sure you want to clear all query history?')) {
            clearQueryHistory();
        }
    };
    const handleSaveQuery = (historyItem) => {
        if (!saveQueryName.trim())
            return;
        // Temporarily set the selected index and query to match the history item
        const currentState = {
            selectedIndex: _stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore.getState().selectedIndex,
            queryMode: _stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore.getState().queryMode,
            visualQuery: _stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore.getState().visualQuery,
            dslQuery: _stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore.getState().dslQuery,
        };
        // Set the state to match the history item
        _stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore.setState({
            selectedIndex: historyItem.index,
            queryMode: historyItem.queryMode,
            visualQuery: historyItem.queryMode === 'visual' ? historyItem.query : _stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore.getState().visualQuery,
            dslQuery: historyItem.queryMode === 'dsl' ? JSON.stringify(historyItem.query, null, 2) : _stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore.getState().dslQuery,
        });
        // Save the query
        const queryId = saveQuery(saveQueryName, saveQueryDescription, saveQueryTags);
        // Restore the previous state
        _stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore.setState(currentState);
        setSaveQueryName('');
        setSaveQueryDescription('');
        setSaveQueryTags([]);
        setShowSaveDialog(null);
    };
    const handleToggleFavorite = (historyItem) => {
        const existingSaved = savedQueries.find(sq => JSON.stringify(sq.query) === JSON.stringify(historyItem.query) &&
            sq.index === historyItem.index);
        if (existingSaved) {
            toggleQueryFavorite(existingSaved.id);
        }
        else {
            // Save as favorite if not already saved
            const queryName = `Query on ${historyItem.index} - ${new Date(historyItem.executedAt).toLocaleDateString()}`;
            // Temporarily set the state to match the history item
            const currentState = {
                selectedIndex: _stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore.getState().selectedIndex,
                queryMode: _stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore.getState().queryMode,
                visualQuery: _stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore.getState().visualQuery,
                dslQuery: _stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore.getState().dslQuery,
            };
            _stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore.setState({
                selectedIndex: historyItem.index,
                queryMode: historyItem.queryMode,
                visualQuery: historyItem.queryMode === 'visual' ? historyItem.query : _stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore.getState().visualQuery,
                dslQuery: historyItem.queryMode === 'dsl' ? JSON.stringify(historyItem.query, null, 2) : _stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore.getState().dslQuery,
            });
            const queryId = saveQuery(queryName, '', [], undefined);
            // Restore the previous state
            _stores_query__WEBPACK_IMPORTED_MODULE_24__.useQueryStore.setState(currentState);
            // Mark as favorite
            if (queryId) {
                toggleQueryFavorite(queryId);
            }
        }
    };
    const handleAddTag = (queryId) => {
        if (!newTag.trim())
            return;
        const savedQuery = savedQueries.find(sq => sq.id === queryId);
        if (savedQuery && !savedQuery.tags.includes(newTag)) {
            updateSavedQuery(queryId, {
                tags: [...savedQuery.tags, newTag]
            });
        }
        setNewTag('');
        setShowTagInput(null);
    };
    const handleRemoveTag = (queryId, tagToRemove) => {
        const savedQuery = savedQueries.find(sq => sq.id === queryId);
        if (savedQuery) {
            updateSavedQuery(queryId, {
                tags: savedQuery.tags.filter(tag => tag !== tagToRemove)
            });
        }
    };
    const handleGenerateShareLink = (historyItem) => {
        const shareData = {
            index: historyItem.index,
            query: historyItem.query,
            queryMode: historyItem.queryMode,
            timestamp: historyItem.executedAt.toISOString(),
        };
        const shareUrl = `${window.location.origin}/shared-query?data=${encodeURIComponent(btoa(JSON.stringify(shareData)))}`;
        navigator.clipboard.writeText(shareUrl);
        // Show success feedback
        alert('Share link copied to clipboard!');
        setShowShareDialog(null);
    };
    const handleExportQueries = () => {
        const exportData = exportQueries();
        const blob = new Blob([exportData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `elasticsearch-queries-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };
    const handleImportQueries = async (event) => {
        const file = event.target.files?.[0];
        if (!file)
            return;
        try {
            const content = await file.text();
            const result = await importQueries(content);
            if (result.errors.length > 0) {
                alert(`Import completed with ${result.success} successful imports and ${result.errors.length} errors:\n${result.errors.join('\n')}`);
            }
            else {
                alert(`Successfully imported ${result.success} queries!`);
            }
        }
        catch (error) {
            alert(`Failed to import queries: ${error}`);
        }
        // Reset file input
        event.target.value = '';
    };
    const toggleSortOrder = () => {
        setSortOrder(current => current === 'desc' ? 'asc' : 'desc');
    };
    const getSavedQueryForHistory = (historyItem) => {
        return savedQueries.find(sq => JSON.stringify(sq.query) === JSON.stringify(historyItem.query) &&
            sq.index === historyItem.index);
    };
    const getUsageStatsForHistory = (historyItem) => {
        return usageStats.get(JSON.stringify(historyItem.query));
    };
    const formatExecutionTime = (time) => {
        if (!time)
            return 'N/A';
        return time < 1000 ? `${time}ms` : `${(time / 1000).toFixed(2)}s`;
    };
    const formatQueryPreview = (item) => {
        if (item.queryMode === 'visual') {
            const visualQuery = item.query;
            if (visualQuery.conditions && visualQuery.conditions.length > 0) {
                const condition = visualQuery.conditions[0];
                return `${condition.field} ${condition.operator} ${condition.value}`;
            }
            return 'match_all';
        }
        else {
            // DSL query preview
            const queryStr = JSON.stringify(item.query);
            return queryStr.length > 50 ? queryStr.substring(0, 50) + '...' : queryStr;
        }
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_26__.Card, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between mb-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_10__["default"], { className: "w-6 h-6 text-blue-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-xl font-semibold text-gray-900 dark:text-white", children: "Query History & Favorites" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Badge__WEBPACK_IMPORTED_MODULE_28__.Badge, { variant: "secondary", className: "text-xs", children: [queryHistory.length, " items"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Badge__WEBPACK_IMPORTED_MODULE_28__.Badge, { variant: "outline", className: "text-xs", children: [savedQueries.filter(sq => sq.isFavorite).length, " favorites"] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "file", accept: ".json", onChange: handleImportQueries, className: "hidden", id: "import-queries" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { variant: "outline", onClick: () => document.getElementById('import-queries')?.click(), className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_21__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "Import" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { variant: "outline", onClick: handleExportQueries, disabled: savedQueries.length === 0, className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_8__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "Export" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { variant: "outline", onClick: handleClearHistory, disabled: queryHistory.length === 0, className: "flex items-center space-x-2 text-red-600 hover:text-red-700", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_19__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "Clear All" })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-col sm:flex-row gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "relative flex-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_14__["default"], { className: "absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Input__WEBPACK_IMPORTED_MODULE_27__.Input, { placeholder: "Search by index, query content, or error...", value: searchTerm, onChange: e => setSearchTerm(e.target.value), className: "pl-10" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("select", { value: filterStatus, onChange: e => setFilterStatus(e.target.value), className: "px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "all", children: "All Status" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "success", children: "Success Only" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "error", children: "Errors Only" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "favorites", children: "Favorites Only" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("select", { value: sortBy, onChange: e => setSortBy(e.target.value), className: "px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md bg-white dark:bg-gray-800 text-sm border-r-0", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "date", children: "Sort by Date" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "execution_time", children: "Sort by Execution Time" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "usage_frequency", children: "Sort by Usage Frequency" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option", { value: "name", children: "Sort by Name" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { variant: "outline", size: "sm", onClick: toggleSortOrder, className: "rounded-l-none border-l-0 px-2", children: sortOrder === 'desc' ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_2__["default"], { className: "w-4 h-4" }) : (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_3__["default"], { className: "w-4 h-4" }) })] })] })] }), availableTags.length > 0 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-wrap gap-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "text-sm text-gray-500 dark:text-gray-400 flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_18__["default"], { className: "w-4 h-4 mr-1" }), "Filter by tags:"] }), availableTags.map(tag => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Badge__WEBPACK_IMPORTED_MODULE_28__.Badge, { variant: selectedTags.includes(tag) ? "default" : "outline", className: "cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900", onClick: () => {
                                            setSelectedTags(current => current.includes(tag)
                                                ? current.filter(t => t !== tag)
                                                : [...current, tag]);
                                        }, children: [tag, selectedTags.includes(tag) && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_23__["default"], { className: "w-3 h-3 ml-1" }))] }, tag))), selectedTags.length > 0 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { variant: "ghost", size: "sm", onClick: () => setSelectedTags([]), className: "text-xs h-6", children: "Clear filters" }))] }))] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-4", children: filteredHistory.length === 0 ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_26__.Card, { className: "p-12", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center text-gray-500 dark:text-gray-400", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_10__["default"], { className: "w-12 h-12 mx-auto mb-4 opacity-50" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium mb-2", children: queryHistory.length === 0
                                    ? 'No Query History'
                                    : 'No Matching History' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { children: queryHistory.length === 0
                                    ? 'Execute some queries to see them appear here.'
                                    : 'Try adjusting your search terms or filters.' })] }) })) : (filteredHistory.map(item => {
                    const savedQuery = getSavedQueryForHistory(item);
                    const usageStats = getUsageStatsForHistory(item);
                    const isFavorite = savedQuery?.isFavorite || false;
                    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_26__.Card, { className: "p-6 hover:shadow-md transition-shadow", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center space-x-3 mb-2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [item.success ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], { className: "w-5 h-5 text-green-500" })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_22__["default"], { className: "w-5 h-5 text-red-500" })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Badge__WEBPACK_IMPORTED_MODULE_28__.Badge, { variant: "outline", className: "text-xs", children: item.index }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Badge__WEBPACK_IMPORTED_MODULE_28__.Badge, { variant: item.queryMode === 'visual' ? 'default' : 'secondary', className: "text-xs", children: item.queryMode.toUpperCase() }), usageStats && usageStats.usageCount > 1 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Badge__WEBPACK_IMPORTED_MODULE_28__.Badge, { variant: "outline", className: "text-xs flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_20__["default"], { className: "w-3 h-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { children: [usageStats.usageCount, "x used"] })] })), isFavorite && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Badge__WEBPACK_IMPORTED_MODULE_28__.Badge, { variant: "default", className: "text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_17__["default"], { className: "w-3 h-3 mr-1" }), "Favorite"] }))] }) }), savedQuery && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "mb-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-sm font-medium text-gray-900 dark:text-white", children: savedQuery.name }), savedQuery.description && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-gray-500 dark:text-gray-400", children: savedQuery.description }))] })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mb-3", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-gray-600 dark:text-gray-400 font-mono", children: formatQueryPreview(item) }) }), savedQuery && savedQuery.tags.length > 0 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-wrap gap-1 mb-3", children: [savedQuery.tags.map(tag => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Badge__WEBPACK_IMPORTED_MODULE_28__.Badge, { variant: "secondary", className: "text-xs", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"], { className: "w-3 h-3 mr-1" }), tag] }, tag))), showTagInput === item.id ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Input__WEBPACK_IMPORTED_MODULE_27__.Input, { value: newTag, onChange: e => setNewTag(e.target.value), placeholder: "Add tag...", className: "h-6 text-xs w-20", onKeyPress: e => {
                                                                    if (e.key === 'Enter') {
                                                                        handleAddTag(savedQuery.id);
                                                                    }
                                                                } }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { size: "sm", onClick: () => handleAddTag(savedQuery.id), className: "h-6 px-2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_12__["default"], { className: "w-3 h-3" }) })] })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { variant: "ghost", size: "sm", onClick: () => setShowTagInput(item.id), className: "h-6 px-2 text-xs", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_12__["default"], { className: "w-3 h-3 mr-1" }), "Add tag"] }))] })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: new Date(item.executedAt).toLocaleString() })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: formatExecutionTime(item.executionTime) })] }), item.success && item.resultCount !== undefined && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center space-x-1", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { children: [item.resultCount.toLocaleString(), " results"] }) })), usageStats && usageStats.avgExecutionTime > 0 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center space-x-1", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { children: ["Avg: ", formatExecutionTime(usageStats.avgExecutionTime)] }) }))] }), !item.success && item.error && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mt-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-md", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-red-600 dark:text-red-400", children: item.error }) }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-col items-end space-y-2 ml-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { variant: "outline", size: "sm", onClick: () => handleLoadQuery(item.id), className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_13__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "Rerun" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { variant: "ghost", size: "sm", onClick: () => handleToggleFavorite(item), className: `${isFavorite ? 'text-yellow-500 hover:text-yellow-600' : 'text-gray-400 hover:text-yellow-500'}`, children: isFavorite ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_17__["default"], { className: "w-4 h-4 fill-current" }) : (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_16__["default"], { className: "w-4 h-4" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { variant: "ghost", size: "sm", onClick: () => setShowShareDialog(item.id), className: "text-blue-500 hover:text-blue-600", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_15__["default"], { className: "w-4 h-4" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { variant: "ghost", size: "sm", onClick: () => handleDeleteItem(item.id), className: "text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_19__["default"], { className: "w-4 h-4" }) })] }), !savedQuery && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { variant: "outline", size: "sm", onClick: () => setShowSaveDialog(item.id), className: "text-xs", children: "Save Query" }))] })] }), showShareDialog === item.id && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-sm font-medium mb-2", children: "Share Query" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { size: "sm", onClick: () => handleGenerateShareLink(item), className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_11__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "Generate Link" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { size: "sm", variant: "outline", onClick: () => {
                                                    navigator.clipboard.writeText(JSON.stringify(item.query, null, 2));
                                                    alert('Query copied to clipboard!');
                                                }, className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "Copy Query" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { size: "sm", variant: "ghost", onClick: () => setShowShareDialog(null), children: "Cancel" })] })] })), showSaveDialog === item.id && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-md", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-sm font-medium mb-2", children: "Save Query" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Input__WEBPACK_IMPORTED_MODULE_27__.Input, { placeholder: "Query name...", value: saveQueryName, onChange: e => setSaveQueryName(e.target.value), className: "text-sm" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Input__WEBPACK_IMPORTED_MODULE_27__.Input, { placeholder: "Description (optional)...", value: saveQueryDescription, onChange: e => setSaveQueryDescription(e.target.value), className: "text-sm" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { size: "sm", onClick: () => handleSaveQuery(item), disabled: !saveQueryName.trim(), children: "Save" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_25__.Button, { size: "sm", variant: "ghost", onClick: () => {
                                                            setShowSaveDialog(null);
                                                            setSaveQueryName('');
                                                            setSaveQueryDescription('');
                                                        }, children: "Cancel" })] })] })] }))] }, item.id));
                })) })] }));
}


/***/ }),

/***/ "./src/renderer/components/Query/SavedQueries.tsx":
/*!********************************************************!*\
  !*** ./src/renderer/components/Query/SavedQueries.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ SavedQueries)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/calendar.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/play.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/save.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/search.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/tag.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/trash-2.mjs");
/* harmony import */ var _stores_query__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../stores/query */ "./src/renderer/stores/query.ts");
/* harmony import */ var _UI_Button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../UI/Button */ "./src/renderer/components/UI/Button.tsx");
/* harmony import */ var _UI_Card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../UI/Card */ "./src/renderer/components/UI/Card.tsx");
/* harmony import */ var _UI_Input__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../UI/Input */ "./src/renderer/components/UI/Input.tsx");
/* harmony import */ var _UI_Modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../UI/Modal */ "./src/renderer/components/UI/Modal.tsx");
/* harmony import */ var _UI_Badge__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../UI/Badge */ "./src/renderer/components/UI/Badge.tsx");









function SavedQueries() {
    const { savedQueries, selectedIndex, queryMode, visualQuery, dslQuery, saveQuery, loadSavedQuery, deleteSavedQuery, } = (0,_stores_query__WEBPACK_IMPORTED_MODULE_8__.useQueryStore)();
    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
    const [saveForm, setSaveForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({
        name: '',
        description: '',
        tags: '',
    });
    const filteredQueries = savedQueries.filter(query => query.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        query.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        query.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())));
    const handleSaveQuery = () => {
        if (!selectedIndex || !saveForm.name.trim())
            return;
        const tags = saveForm.tags
            .split(',')
            .map(tag => tag.trim())
            .filter(tag => tag.length > 0);
        saveQuery(saveForm.name.trim(), saveForm.description.trim() || undefined, tags);
        setSaveForm({ name: '', description: '', tags: '' });
        setIsModalOpen(false);
    };
    const handleLoadQuery = (queryId) => {
        loadSavedQuery(queryId);
    };
    const handleDeleteQuery = (queryId) => {
        if (confirm('Are you sure you want to delete this saved query?')) {
            deleteSavedQuery(queryId);
        }
    };
    const canSaveQuery = selectedIndex &&
        ((queryMode === 'visual' && visualQuery.conditions.length > 0) ||
            (queryMode === 'dsl' &&
                dslQuery.trim() !== '{\n  "query": {\n    "match_all": {}\n  }\n}'));
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_10__.Card, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between mb-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], { className: "w-6 h-6 text-blue-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-xl font-semibold text-gray-900 dark:text-white", children: "Saved Queries" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_9__.Button, { onClick: () => setIsModalOpen(true), disabled: !canSaveQuery, className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "Save Current Query" })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "relative", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], { className: "absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Input__WEBPACK_IMPORTED_MODULE_11__.Input, { placeholder: "Search saved queries...", value: searchTerm, onChange: e => setSearchTerm(e.target.value), className: "pl-10" })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-4", children: filteredQueries.length === 0 ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_10__.Card, { className: "p-12", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center text-gray-500 dark:text-gray-400", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], { className: "w-12 h-12 mx-auto mb-4 opacity-50" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium mb-2", children: savedQueries.length === 0
                                    ? 'No Saved Queries'
                                    : 'No Matching Queries' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { children: savedQueries.length === 0
                                    ? 'Save your frequently used queries for quick access.'
                                    : 'Try adjusting your search terms.' })] }) })) : (filteredQueries.map(query => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_10__.Card, { className: "p-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3 mb-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium text-gray-900 dark:text-white", children: query.name }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Badge__WEBPACK_IMPORTED_MODULE_13__.Badge, { variant: "outline", className: "text-xs", children: query.index })] }), query.description && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-gray-600 dark:text-gray-400 mb-3", children: query.description })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_2__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: new Date(query.createdAt).toLocaleDateString() })] }), query.tags.length > 0 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex space-x-1", children: query.tags.map(tag => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Badge__WEBPACK_IMPORTED_MODULE_13__.Badge, { variant: "secondary", className: "text-xs", children: tag }, tag))) })] }))] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2 ml-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_9__.Button, { variant: "outline", size: "sm", onClick: () => handleLoadQuery(query.id), className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_3__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "Load" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_9__.Button, { variant: "ghost", size: "sm", onClick: () => handleDeleteQuery(query.id), className: "text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"], { className: "w-4 h-4" }) })] })] }) }, query.id)))) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Modal__WEBPACK_IMPORTED_MODULE_12__.Modal, { isOpen: isModalOpen, onClose: () => setIsModalOpen(false), title: "Save Query", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2", children: "Query Name *" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Input__WEBPACK_IMPORTED_MODULE_11__.Input, { placeholder: "Enter a name for this query", value: saveForm.name, onChange: e => setSaveForm({ ...saveForm, name: e.target.value }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2", children: "Description" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Input__WEBPACK_IMPORTED_MODULE_11__.Input, { placeholder: "Optional description", value: saveForm.description, onChange: e => setSaveForm({ ...saveForm, description: e.target.value }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2", children: "Tags" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Input__WEBPACK_IMPORTED_MODULE_11__.Input, { placeholder: "Enter tags separated by commas", value: saveForm.tags, onChange: e => setSaveForm({ ...saveForm, tags: e.target.value }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-gray-500 mt-1", children: "Example: analytics, daily-report, user-data" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-end space-x-3 pt-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_9__.Button, { variant: "outline", onClick: () => setIsModalOpen(false), children: "Cancel" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_9__.Button, { onClick: handleSaveQuery, disabled: !saveForm.name.trim(), children: "Save Query" })] })] }) })] }));
}


/***/ }),

/***/ "./src/renderer/components/Query/index.ts":
/*!************************************************!*\
  !*** ./src/renderer/components/Query/index.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DSLQueryEditor: () => (/* reexport safe */ _DSLQueryEditor__WEBPACK_IMPORTED_MODULE_2__["default"]),
/* harmony export */   QueryBuilder: () => (/* reexport safe */ _QueryBuilder__WEBPACK_IMPORTED_MODULE_0__["default"]),
/* harmony export */   QueryCondition: () => (/* reexport safe */ _QueryCondition__WEBPACK_IMPORTED_MODULE_3__["default"]),
/* harmony export */   QueryHistory: () => (/* reexport safe */ _QueryHistory__WEBPACK_IMPORTED_MODULE_5__["default"]),
/* harmony export */   SavedQueries: () => (/* reexport safe */ _SavedQueries__WEBPACK_IMPORTED_MODULE_4__["default"]),
/* harmony export */   VisualQueryBuilder: () => (/* reexport safe */ _VisualQueryBuilder__WEBPACK_IMPORTED_MODULE_1__["default"])
/* harmony export */ });
/* harmony import */ var _QueryBuilder__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./QueryBuilder */ "./src/renderer/components/Query/QueryBuilder.tsx");
/* harmony import */ var _VisualQueryBuilder__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./VisualQueryBuilder */ "./src/renderer/components/Query/VisualQueryBuilder.tsx");
/* harmony import */ var _DSLQueryEditor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DSLQueryEditor */ "./src/renderer/components/Query/DSLQueryEditor.tsx");
/* harmony import */ var _QueryCondition__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryCondition */ "./src/renderer/components/Query/QueryCondition.tsx");
/* harmony import */ var _SavedQueries__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SavedQueries */ "./src/renderer/components/Query/SavedQueries.tsx");
/* harmony import */ var _QueryHistory__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./QueryHistory */ "./src/renderer/components/Query/QueryHistory.tsx");








/***/ })

}]);
//# sourceMappingURL=src_renderer_components_Query_index_ts.renderer.js.map