import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { 
  Database, 
  Plus, 
  X, 
  RefreshCw, 
  Search, 
  Filter,
  Settings,
  Maximize2,
  Minimize2,
  MoreVertical,
  ArrowLeft,
  ArrowRight,
  Copy,
  Download,
  Upload,
  Edit3,
  Trash2,
  Eye
} from 'lucide-react';
import { Card } from '../UI/Card';
import { Button } from '../UI/Button';
import { Badge } from '../UI/Badge';
import { Input } from '../UI/Input';
import { useToast } from '../../hooks/useToast';
import { EnhancedDataDisplay } from './EnhancedDataDisplay';
import { SimpleDataBrowser } from './SimpleDataBrowser';
import { SimpleDocumentEditor } from './SimpleDocumentEditor';
import { SimpleBatchOperations } from './SimpleBatchOperations';

interface TabInfo {
  id: string;
  indexName: string;
  title: string;
  isActive: boolean;
  hasUnsavedChanges?: boolean;
  searchQuery?: string;
  searchResults?: any;
  lastUpdated?: Date;
  viewMode?: 'browser' | 'editor' | 'batch' | 'query';
}

interface EnhancedDataOperationPanelProps {
  selectedIndex?: string;
  indices: string[];
  onIndexSelect?: (indexName: string) => void;
  onRefresh?: () => void;
  isLoading?: boolean;
  className?: string;
  enableMultiTab?: boolean;
  maxTabs?: number;
  showQuickActions?: boolean;
  enableAutoRefresh?: boolean;
  autoRefreshInterval?: number;
}

export const EnhancedDataOperationPanel: React.FC<EnhancedDataOperationPanelProps> = ({
  selectedIndex,
  indices,
  onIndexSelect,
  onRefresh,
  isLoading = false,
  className = '',
  enableMultiTab = true,
  maxTabs = 5,
  showQuickActions = true,
  enableAutoRefresh = false,
  autoRefreshInterval = 30000
}) => {
  const [tabs, setTabs] = useState<TabInfo[]>([]);
  const [activeTabId, setActiveTabId] = useState<string | null>(null);
  const [isMaximized, setIsMaximized] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'browser' | 'editor' | 'batch' | 'query'>('browser');
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(enableAutoRefresh);
  
  const { success, error } = useToast();

  // 当选中的索引改变时，创建或切换到对应的标签页
  useEffect(() => {
    if (selectedIndex && enableMultiTab) {
      const existingTab = tabs.find(tab => tab.indexName === selectedIndex);
      
      if (existingTab) {
        // 切换到已存在的标签页
        setActiveTabId(existingTab.id);
      } else {
        // 创建新的标签页
        createNewTab(selectedIndex);
      }
    }
  }, [selectedIndex, enableMultiTab]);

  // 自动刷新功能
  useEffect(() => {
    if (!autoRefreshEnabled || !activeTabId) return;

    const interval = setInterval(() => {
      handleRefreshActiveTab();
    }, autoRefreshInterval);

    return () => clearInterval(interval);
  }, [autoRefreshEnabled, activeTabId, autoRefreshInterval]);

  // 创建新标签页
  const createNewTab = useCallback((indexName: string) => {
    if (tabs.length >= maxTabs) {
      error(`最多只能打开 ${maxTabs} 个标签页`);
      return;
    }

    const newTab: TabInfo = {
      id: `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      indexName,
      title: indexName,
      isActive: true,
      viewMode: 'browser',
      lastUpdated: new Date()
    };

    setTabs(prev => {
      const updated = prev.map(tab => ({ ...tab, isActive: false }));
      return [...updated, newTab];
    });
    setActiveTabId(newTab.id);
    success(`已打开索引: ${indexName}`);
  }, [tabs.length, maxTabs, error, success]);

  // 关闭标签页
  const closeTab = useCallback((tabId: string, event?: React.MouseEvent) => {
    event?.stopPropagation();
    
    const tabToClose = tabs.find(tab => tab.id === tabId);
    if (tabToClose?.hasUnsavedChanges) {
      // TODO: 显示确认对话框
      if (!confirm('该标签页有未保存的更改，确定要关闭吗？')) {
        return;
      }
    }

    setTabs(prev => {
      const filtered = prev.filter(tab => tab.id !== tabId);
      
      // 如果关闭的是当前活动标签页，切换到其他标签页
      if (tabId === activeTabId) {
        if (filtered.length > 0) {
          const newActiveTab = filtered[filtered.length - 1];
          setActiveTabId(newActiveTab.id);
          onIndexSelect?.(newActiveTab.indexName);
        } else {
          setActiveTabId(null);
        }
      }
      
      return filtered;
    });
  }, [tabs, activeTabId, onIndexSelect]);

  // 切换标签页
  const switchTab = useCallback((tabId: string) => {
    const tab = tabs.find(t => t.id === tabId);
    if (!tab) return;

    setTabs(prev => prev.map(t => ({ ...t, isActive: t.id === tabId })));
    setActiveTabId(tabId);
    onIndexSelect?.(tab.indexName);
  }, [tabs, onIndexSelect]);

  // 刷新当前活动标签页
  const handleRefreshActiveTab = useCallback(() => {
    if (!activeTabId) return;
    
    setTabs(prev => prev.map(tab => 
      tab.id === activeTabId 
        ? { ...tab, lastUpdated: new Date() }
        : tab
    ));
    
    onRefresh?.();
    success('数据已刷新');
  }, [activeTabId, onRefresh, success]);

  // 获取当前活动标签页
  const activeTab = useMemo(() => {
    return tabs.find(tab => tab.id === activeTabId);
  }, [tabs, activeTabId]);

  // 渲染标签页头部
  const renderTabHeader = () => {
    if (!enableMultiTab || tabs.length === 0) return null;

    return (
      <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center overflow-x-auto">
          {tabs.map(tab => (
            <div
              key={tab.id}
              className={`
                flex items-center space-x-2 px-4 py-2 cursor-pointer border-r border-gray-200 dark:border-gray-700
                hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors
                ${tab.id === activeTabId ? 'bg-white dark:bg-gray-900 border-b-2 border-blue-500' : ''}
              `}
              onClick={() => switchTab(tab.id)}
            >
              <Database className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-900 dark:text-white truncate max-w-32">
                {tab.title}
              </span>
              {tab.hasUnsavedChanges && (
                <div className="w-2 h-2 bg-orange-500 rounded-full" />
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => closeTab(tab.id, e)}
                className="p-0.5 h-5 w-5 hover:bg-gray-200 dark:hover:bg-gray-600"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
          ))}
        </div>
        
        <div className="flex items-center space-x-2 px-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setAutoRefreshEnabled(!autoRefreshEnabled)}
            className="p-2"
            title={autoRefreshEnabled ? '关闭自动刷新' : '开启自动刷新'}
          >
            <RefreshCw className={`w-4 h-4 ${autoRefreshEnabled ? 'text-green-500' : 'text-gray-400'}`} />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMaximized(!isMaximized)}
            className="p-2"
            title={isMaximized ? '还原窗口' : '最大化窗口'}
          >
            {isMaximized ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
          </Button>
        </div>
      </div>
    );
  };

  // 渲染工具栏
  const renderToolbar = () => {
    if (!activeTab) return null;

    return (
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Database className="w-5 h-5 text-blue-500" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              {activeTab.indexName}
            </h3>
            <Badge variant="secondary" className="text-xs">
              {viewMode}
            </Badge>
          </div>
          
          {activeTab.lastUpdated && (
            <span className="text-xs text-gray-500 dark:text-gray-400">
              更新于 {activeTab.lastUpdated.toLocaleTimeString()}
            </span>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {showQuickActions && (
            <>
              <Button variant="outline" size="sm">
                <Plus className="w-4 h-4 mr-1" />
                新建
              </Button>
              <Button variant="outline" size="sm">
                <Upload className="w-4 h-4 mr-1" />
                导入
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-1" />
                导出
              </Button>
            </>
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefreshActiveTab}
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSettings(!showSettings)}
          >
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </div>
    );
  };

  // 渲染内容区域
  const renderContent = () => {
    if (!activeTab) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Database className="w-12 h-12 mx-auto mb-4 text-gray-400" />
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {enableMultiTab ? '选择一个索引开始操作' : '请先选择索引'}
            </p>
            {enableMultiTab && indices.length > 0 && (
              <div className="flex flex-wrap gap-2 justify-center">
                {indices.slice(0, 5).map(index => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => createNewTab(index)}
                  >
                    {index}
                  </Button>
                ))}
              </div>
            )}
          </div>
        </div>
      );
    }

    // 根据视图模式渲染不同的组件
    switch (viewMode) {
      case 'browser':
        return (
          <SimpleDataBrowser
            searchResults={activeTab.searchResults}
            isLoading={isLoading}
            onRefresh={handleRefreshActiveTab}
            className="h-full"
          />
        );
      case 'editor':
        return (
          <SimpleDocumentEditor
            index={activeTab.indexName}
            mode="create"
            onSave={() => {}}
            onCancel={() => setViewMode('browser')}
          />
        );
      case 'batch':
        return (
          <SimpleBatchOperations
            index={activeTab.indexName}
            onImportComplete={() => setViewMode('browser')}
            onExportComplete={() => setViewMode('browser')}
          />
        );
      default:
        return (
          <div className="p-8 text-center">
            <p className="text-gray-600 dark:text-gray-400">
              视图模式 "{viewMode}" 暂未实现
            </p>
          </div>
        );
    }
  };

  return (
    <Card className={`flex flex-col h-full ${isMaximized ? 'fixed inset-4 z-50' : ''} ${className}`}>
      {renderTabHeader()}
      {renderToolbar()}
      
      <div className="flex-1 overflow-hidden">
        {renderContent()}
      </div>
    </Card>
  );
};

export default EnhancedDataOperationPanel;
