/**
 * 索引切换性能优化服务
 * 实现500ms内完成索引切换的性能要求
 */

interface IndexSwitchMetrics {
  indexName: string;
  switchTime: number;
  dataLoadTime: number;
  renderTime: number;
  totalTime: number;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
}

interface CachedIndexData {
  indexName: string;
  searchResults: any;
  metadata: any;
  cachedAt: Date;
  ttl: number;
  hitCount: number;
  lastAccessed: Date;
}

interface PreloadConfig {
  enabled: boolean;
  maxPreloadCount: number;
  preloadThreshold: number; // 访问频率阈值
  backgroundPreload: boolean;
}

export class IndexSwitchOptimizer {
  private static instance: IndexSwitchOptimizer;
  private cache = new Map<string, CachedIndexData>();
  private metrics: IndexSwitchMetrics[] = [];
  private preloadQueue = new Set<string>();
  private isPreloading = false;
  
  private config: PreloadConfig = {
    enabled: true,
    maxPreloadCount: 10,
    preloadThreshold: 3,
    backgroundPreload: true
  };

  private readonly PERFORMANCE_TARGET = 500; // 500ms 性能目标
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存
  private readonly MAX_CACHE_SIZE = 50;
  private readonly MAX_METRICS_SIZE = 1000;

  private constructor() {
    this.startBackgroundPreloader();
  }

  public static getInstance(): IndexSwitchOptimizer {
    if (!IndexSwitchOptimizer.instance) {
      IndexSwitchOptimizer.instance = new IndexSwitchOptimizer();
    }
    return IndexSwitchOptimizer.instance;
  }

  /**
   * 优化的索引切换方法
   */
  public async switchIndex(
    indexName: string,
    dataLoader: (index: string) => Promise<any>,
    onProgress?: (stage: string, progress: number) => void
  ): Promise<any> {
    const startTime = performance.now();
    let dataLoadTime = 0;
    let renderTime = 0;
    let success = false;
    let errorMessage: string | undefined;

    try {
      onProgress?.('准备切换索引', 10);

      // 1. 检查缓存
      const cachedData = this.getCachedData(indexName);
      if (cachedData) {
        onProgress?.('使用缓存数据', 90);
        this.updateCacheAccess(indexName);
        
        renderTime = performance.now() - startTime;
        success = true;
        
        // 异步更新缓存
        this.refreshCacheInBackground(indexName, dataLoader);
        
        onProgress?.('完成', 100);
        return cachedData.searchResults;
      }

      onProgress?.('加载索引数据', 30);

      // 2. 加载数据
      const dataLoadStart = performance.now();
      const data = await dataLoader(indexName);
      dataLoadTime = performance.now() - dataLoadStart;

      onProgress?.('处理数据', 70);

      // 3. 缓存数据
      this.cacheData(indexName, data);

      onProgress?.('渲染界面', 90);
      
      renderTime = performance.now() - startTime - dataLoadTime;
      success = true;

      onProgress?.('完成', 100);
      return data;

    } catch (error) {
      success = false;
      errorMessage = error instanceof Error ? error.message : '未知错误';
      throw error;
    } finally {
      // 记录性能指标
      const totalTime = performance.now() - startTime;
      this.recordMetrics({
        indexName,
        switchTime: totalTime - dataLoadTime - renderTime,
        dataLoadTime,
        renderTime,
        totalTime,
        timestamp: new Date(),
        success,
        errorMessage
      });

      // 触发预加载
      this.triggerPreload(indexName);
    }
  }

  /**
   * 预加载索引数据
   */
  public async preloadIndex(
    indexName: string,
    dataLoader: (index: string) => Promise<any>
  ): Promise<void> {
    if (this.cache.has(indexName)) {
      return; // 已经缓存，无需预加载
    }

    try {
      const data = await dataLoader(indexName);
      this.cacheData(indexName, data);
    } catch (error) {
      console.warn(`预加载索引 ${indexName} 失败:`, error);
    }
  }

  /**
   * 批量预加载索引
   */
  public async preloadIndices(
    indices: string[],
    dataLoader: (index: string) => Promise<any>,
    onProgress?: (completed: number, total: number) => void
  ): Promise<void> {
    const uncachedIndices = indices.filter(index => !this.cache.has(index));
    
    for (let i = 0; i < uncachedIndices.length; i++) {
      const index = uncachedIndices[i];
      try {
        await this.preloadIndex(index, dataLoader);
        onProgress?.(i + 1, uncachedIndices.length);
      } catch (error) {
        console.warn(`预加载索引 ${index} 失败:`, error);
      }
    }
  }

  /**
   * 获取缓存数据
   */
  private getCachedData(indexName: string): CachedIndexData | null {
    const cached = this.cache.get(indexName);
    if (!cached) return null;

    // 检查TTL
    if (Date.now() - cached.cachedAt.getTime() > cached.ttl) {
      this.cache.delete(indexName);
      return null;
    }

    return cached;
  }

  /**
   * 缓存数据
   */
  private cacheData(indexName: string, data: any, metadata?: any): void {
    // 检查缓存大小限制
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.evictLeastRecentlyUsed();
    }

    const cachedData: CachedIndexData = {
      indexName,
      searchResults: data,
      metadata: metadata || {},
      cachedAt: new Date(),
      ttl: this.CACHE_TTL,
      hitCount: 0,
      lastAccessed: new Date()
    };

    this.cache.set(indexName, cachedData);
  }

  /**
   * 更新缓存访问记录
   */
  private updateCacheAccess(indexName: string): void {
    const cached = this.cache.get(indexName);
    if (cached) {
      cached.hitCount++;
      cached.lastAccessed = new Date();
    }
  }

  /**
   * LRU缓存淘汰
   */
  private evictLeastRecentlyUsed(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, data] of this.cache.entries()) {
      if (data.lastAccessed.getTime() < oldestTime) {
        oldestTime = data.lastAccessed.getTime();
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * 后台刷新缓存
   */
  private async refreshCacheInBackground(
    indexName: string,
    dataLoader: (index: string) => Promise<any>
  ): Promise<void> {
    try {
      const data = await dataLoader(indexName);
      this.cacheData(indexName, data);
    } catch (error) {
      console.warn(`后台刷新索引 ${indexName} 缓存失败:`, error);
    }
  }

  /**
   * 触发预加载
   */
  private triggerPreload(currentIndex: string): void {
    if (!this.config.enabled || !this.config.backgroundPreload) return;

    // 基于访问模式预测下一个可能访问的索引
    const candidates = this.predictNextIndices(currentIndex);
    candidates.forEach(index => {
      if (!this.cache.has(index) && this.preloadQueue.size < this.config.maxPreloadCount) {
        this.preloadQueue.add(index);
      }
    });
  }

  /**
   * 预测下一个可能访问的索引
   */
  private predictNextIndices(currentIndex: string): string[] {
    // 基于历史访问模式的简单预测算法
    const recentMetrics = this.metrics
      .filter(m => m.success)
      .slice(-20); // 最近20次切换

    const patterns = new Map<string, string[]>();
    
    for (let i = 0; i < recentMetrics.length - 1; i++) {
      const current = recentMetrics[i].indexName;
      const next = recentMetrics[i + 1].indexName;
      
      if (!patterns.has(current)) {
        patterns.set(current, []);
      }
      patterns.get(current)!.push(next);
    }

    const predictions = patterns.get(currentIndex) || [];
    
    // 返回最常见的后续索引
    const frequency = new Map<string, number>();
    predictions.forEach(index => {
      frequency.set(index, (frequency.get(index) || 0) + 1);
    });

    return Array.from(frequency.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([index]) => index);
  }

  /**
   * 后台预加载器
   */
  private startBackgroundPreloader(): void {
    setInterval(() => {
      if (this.isPreloading || this.preloadQueue.size === 0) return;
      
      this.isPreloading = true;
      const indexToPreload = this.preloadQueue.values().next().value;
      this.preloadQueue.delete(indexToPreload);
      
      // 这里需要外部提供数据加载器
      // 实际实现中可以通过事件系统或依赖注入来解决
      
      this.isPreloading = false;
    }, 1000);
  }

  /**
   * 记录性能指标
   */
  private recordMetrics(metrics: IndexSwitchMetrics): void {
    this.metrics.push(metrics);
    
    // 限制指标数组大小
    if (this.metrics.length > this.MAX_METRICS_SIZE) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS_SIZE / 2);
    }

    // 性能警告
    if (metrics.totalTime > this.PERFORMANCE_TARGET) {
      console.warn(
        `索引切换性能警告: ${metrics.indexName} 耗时 ${metrics.totalTime.toFixed(2)}ms，超过目标 ${this.PERFORMANCE_TARGET}ms`
      );
    }
  }

  /**
   * 获取性能统计
   */
  public getPerformanceStats(): {
    averageSwitchTime: number;
    cacheHitRate: number;
    slowSwitches: number;
    totalSwitches: number;
  } {
    const successfulSwitches = this.metrics.filter(m => m.success);
    const totalSwitches = this.metrics.length;
    
    if (totalSwitches === 0) {
      return {
        averageSwitchTime: 0,
        cacheHitRate: 0,
        slowSwitches: 0,
        totalSwitches: 0
      };
    }

    const averageSwitchTime = successfulSwitches.reduce((sum, m) => sum + m.totalTime, 0) / successfulSwitches.length;
    const slowSwitches = successfulSwitches.filter(m => m.totalTime > this.PERFORMANCE_TARGET).length;
    
    // 计算缓存命中率（基于数据加载时间）
    const cacheHits = successfulSwitches.filter(m => m.dataLoadTime < 10).length; // 小于10ms认为是缓存命中
    const cacheHitRate = cacheHits / successfulSwitches.length;

    return {
      averageSwitchTime,
      cacheHitRate,
      slowSwitches,
      totalSwitches
    };
  }

  /**
   * 清理缓存
   */
  public clearCache(): void {
    this.cache.clear();
    this.preloadQueue.clear();
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<PreloadConfig>): void {
    this.config = { ...this.config, ...config };
  }
}

export default IndexSwitchOptimizer;
