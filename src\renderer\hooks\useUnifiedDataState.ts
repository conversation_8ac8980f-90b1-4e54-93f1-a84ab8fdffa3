import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useUnifiedDataStore, IndexInfo, TabInfo } from '../stores/unifiedDataState';
import DataStateSyncService, { SyncEventType, SyncListener } from '../services/DataStateSyncService';
import { useToast } from './useToast';

// Hook配置选项
export interface UseUnifiedDataStateOptions {
  enableAutoSync?: boolean;
  enableToastNotifications?: boolean;
  syncEventTypes?: SyncEventType[];
  onSyncEvent?: (type: SyncEventType, payload: any) => void;
}

// Hook返回值接口
export interface UseUnifiedDataStateReturn {
  // 状态数据
  indices: IndexInfo[];
  selectedIndex: string | null;
  favoriteIndices: string[];
  pinnedIndices: string[];
  recentIndices: string[];
  tabs: TabInfo[];
  activeTabId: string | null;
  activeTab: TabInfo | null;
  isLoading: boolean;
  error: string | null;
  lastRefresh: Date | null;
  
  // 索引操作
  selectIndex: (indexName: string) => Promise<void>;
  updateIndex: (indexName: string, updates: Partial<IndexInfo>) => void;
  toggleFavorite: (indexName: string) => void;
  togglePinned: (indexName: string) => void;
  refreshIndex: (indexName?: string) => Promise<void>;
  
  // 标签页操作
  createTab: (indexName: string, options?: Partial<TabInfo>) => Promise<string>;
  updateTab: (tabId: string, updates: Partial<TabInfo>) => void;
  closeTab: (tabId: string) => void;
  switchTab: (tabId: string) => void;
  closeAllTabs: () => void;
  duplicateTab: (tabId: string) => void;
  
  // 批量操作
  batchSelectIndices: (indexNames: string[]) => Promise<void>;
  batchToggleFavorites: (indexNames: string[], isFavorite: boolean) => void;
  batchTogglePinned: (indexNames: string[], isPinned: boolean) => void;
  
  // 状态查询
  isIndexFavorite: (indexName: string) => boolean;
  isIndexPinned: (indexName: string) => boolean;
  isIndexRecent: (indexName: string) => boolean;
  getIndexInfo: (indexName: string) => IndexInfo | null;
  getTabByIndex: (indexName: string) => TabInfo | null;
  
  // 同步控制
  forceSyncAll: () => Promise<void>;
  getSyncStatus: () => any;
  
  // 统计信息
  getStatistics: () => {
    totalIndices: number;
    favoriteCount: number;
    pinnedCount: number;
    recentCount: number;
    activeTabsCount: number;
    healthyIndicesCount: number;
    warningIndicesCount: number;
    errorIndicesCount: number;
  };
}

/**
 * 统一数据状态管理Hook
 * 提供索引管理、标签页管理和状态同步的统一接口
 */
export const useUnifiedDataState = (
  options: UseUnifiedDataStateOptions = {}
): UseUnifiedDataStateReturn => {
  const {
    enableAutoSync = true,
    enableToastNotifications = true,
    syncEventTypes = ['index-selected', 'tab-created', 'tab-closed', 'data-refreshed'],
    onSyncEvent
  } = options;

  // Zustand store状态
  const store = useUnifiedDataStore();
  const { success, error: showError, warning } = useToast();
  
  // 同步服务实例
  const syncService = useMemo(() => DataStateSyncService.getInstance(), []);
  const syncListenersRef = useRef<Map<SyncEventType, SyncListener>>(new Map());

  // 计算活动标签页
  const activeTab = useMemo(() => {
    return store.tabs.find(tab => tab.id === store.activeTabId) || null;
  }, [store.tabs, store.activeTabId]);

  // 设置同步事件监听器
  useEffect(() => {
    if (!enableAutoSync) return;

    // 清理旧的监听器
    syncListenersRef.current.forEach((listener, type) => {
      syncService.removeEventListener(type, listener);
    });
    syncListenersRef.current.clear();

    // 添加新的监听器
    syncEventTypes.forEach(eventType => {
      const listener: SyncListener = (event) => {
        // 处理同步事件
        if (enableToastNotifications) {
          handleSyncEventNotification(eventType, event.payload);
        }
        
        // 调用用户自定义回调
        onSyncEvent?.(eventType, event.payload);
      };

      syncService.addEventListener(eventType, listener);
      syncListenersRef.current.set(eventType, listener);
    });

    // 清理函数
    return () => {
      syncListenersRef.current.forEach((listener, type) => {
        syncService.removeEventListener(type, listener);
      });
      syncListenersRef.current.clear();
    };
  }, [enableAutoSync, syncEventTypes, enableToastNotifications, onSyncEvent, syncService]);

  // 处理同步事件通知
  const handleSyncEventNotification = useCallback((type: SyncEventType, payload: any) => {
    switch (type) {
      case 'index-selected':
        success(`已选择索引: ${payload.indexName}`);
        break;
      case 'tab-created':
        success(`已创建标签页: ${payload.tab.title}`);
        break;
      case 'tab-closed':
        warning('标签页已关闭');
        break;
      case 'data-refreshed':
        success(payload.indexName ? `索引 ${payload.indexName} 已刷新` : '数据已刷新');
        break;
      default:
        break;
    }
  }, [success, warning]);

  // 索引操作方法
  const selectIndex = useCallback(async (indexName: string) => {
    try {
      await syncService.syncIndexSelection(indexName, 'user');
    } catch (error) {
      const message = error instanceof Error ? error.message : '选择索引失败';
      showError(message);
      throw error;
    }
  }, [syncService, showError]);

  const updateIndex = useCallback((indexName: string, updates: Partial<IndexInfo>) => {
    syncService.syncIndexUpdate(indexName, updates, 'user');
  }, [syncService]);

  const toggleFavorite = useCallback((indexName: string) => {
    const isFavorite = store.favoriteIndices.includes(indexName);
    syncService.syncFavoriteChange(indexName, !isFavorite, 'user');
  }, [store.favoriteIndices, syncService]);

  const togglePinned = useCallback((indexName: string) => {
    const isPinned = store.pinnedIndices.includes(indexName);
    syncService.syncPinnedChange(indexName, !isPinned, 'user');
  }, [store.pinnedIndices, syncService]);

  const refreshIndex = useCallback(async (indexName?: string) => {
    try {
      await syncService.syncDataRefresh(indexName, 'user');
    } catch (error) {
      const message = error instanceof Error ? error.message : '刷新数据失败';
      showError(message);
      throw error;
    }
  }, [syncService, showError]);

  // 标签页操作方法
  const createTab = useCallback(async (indexName: string, options: Partial<TabInfo> = {}): Promise<string> => {
    const tab: Omit<TabInfo, 'id'> = {
      indexName,
      title: options.title || indexName,
      isActive: true,
      viewMode: options.viewMode || store.preferences.defaultViewMode,
      hasUnsavedChanges: false,
      lastUpdated: new Date(),
      ...options
    };

    return syncService.syncTabCreation(tab, 'user');
  }, [store.preferences.defaultViewMode, syncService]);

  const updateTab = useCallback((tabId: string, updates: Partial<TabInfo>) => {
    syncService.syncTabUpdate(tabId, updates, 'user');
  }, [syncService]);

  const closeTab = useCallback((tabId: string) => {
    syncService.syncTabClose(tabId, 'user');
  }, [syncService]);

  const switchTab = useCallback((tabId: string) => {
    store.setActiveTab(tabId);
  }, [store]);

  const closeAllTabs = useCallback(() => {
    store.closeAllTabs();
  }, [store]);

  const duplicateTab = useCallback((tabId: string) => {
    store.duplicateTab(tabId);
  }, [store]);

  // 批量操作方法
  const batchSelectIndices = useCallback(async (indexNames: string[]) => {
    const promises = indexNames.map(indexName => selectIndex(indexName));
    await Promise.allSettled(promises);
  }, [selectIndex]);

  const batchToggleFavorites = useCallback((indexNames: string[], isFavorite: boolean) => {
    indexNames.forEach(indexName => {
      syncService.syncFavoriteChange(indexName, isFavorite, 'user');
    });
  }, [syncService]);

  const batchTogglePinned = useCallback((indexNames: string[], isPinned: boolean) => {
    indexNames.forEach(indexName => {
      syncService.syncPinnedChange(indexName, isPinned, 'user');
    });
  }, [syncService]);

  // 状态查询方法
  const isIndexFavorite = useCallback((indexName: string) => {
    return store.favoriteIndices.includes(indexName);
  }, [store.favoriteIndices]);

  const isIndexPinned = useCallback((indexName: string) => {
    return store.pinnedIndices.includes(indexName);
  }, [store.pinnedIndices]);

  const isIndexRecent = useCallback((indexName: string) => {
    return store.recentIndices.includes(indexName);
  }, [store.recentIndices]);

  const getIndexInfo = useCallback((indexName: string): IndexInfo | null => {
    return store.indices.find(index => index.name === indexName) || null;
  }, [store.indices]);

  const getTabByIndex = useCallback((indexName: string): TabInfo | null => {
    return store.tabs.find(tab => tab.indexName === indexName) || null;
  }, [store.tabs]);

  // 同步控制方法
  const forceSyncAll = useCallback(async () => {
    try {
      await syncService.forceSyncAll();
      success('数据同步完成');
    } catch (error) {
      const message = error instanceof Error ? error.message : '数据同步失败';
      showError(message);
      throw error;
    }
  }, [syncService, success, showError]);

  const getSyncStatus = useCallback(() => {
    return syncService.getSyncStatus();
  }, [syncService]);

  // 统计信息
  const getStatistics = useCallback(() => {
    const healthyCount = store.indices.filter(i => i.health === 'green').length;
    const warningCount = store.indices.filter(i => i.health === 'yellow').length;
    const errorCount = store.indices.filter(i => i.health === 'red').length;

    return {
      totalIndices: store.indices.length,
      favoriteCount: store.favoriteIndices.length,
      pinnedCount: store.pinnedIndices.length,
      recentCount: store.recentIndices.length,
      activeTabsCount: store.tabs.length,
      healthyIndicesCount: healthyCount,
      warningIndicesCount: warningCount,
      errorIndicesCount: errorCount,
    };
  }, [store]);

  return {
    // 状态数据
    indices: store.indices,
    selectedIndex: store.selectedIndex,
    favoriteIndices: store.favoriteIndices,
    pinnedIndices: store.pinnedIndices,
    recentIndices: store.recentIndices,
    tabs: store.tabs,
    activeTabId: store.activeTabId,
    activeTab,
    isLoading: store.isLoading,
    error: store.error,
    lastRefresh: store.lastRefresh,
    
    // 索引操作
    selectIndex,
    updateIndex,
    toggleFavorite,
    togglePinned,
    refreshIndex,
    
    // 标签页操作
    createTab,
    updateTab,
    closeTab,
    switchTab,
    closeAllTabs,
    duplicateTab,
    
    // 批量操作
    batchSelectIndices,
    batchToggleFavorites,
    batchTogglePinned,
    
    // 状态查询
    isIndexFavorite,
    isIndexPinned,
    isIndexRecent,
    getIndexInfo,
    getTabByIndex,
    
    // 同步控制
    forceSyncAll,
    getSyncStatus,
    
    // 统计信息
    getStatistics,
  };
};

export default useUnifiedDataState;
