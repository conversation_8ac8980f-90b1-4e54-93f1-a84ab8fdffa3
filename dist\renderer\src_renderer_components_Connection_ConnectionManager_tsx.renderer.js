"use strict";
(global["webpackChunkes_client"] = global["webpackChunkes_client"] || []).push([["src_renderer_components_Connection_ConnectionManager_tsx"],{

/***/ "./node_modules/lucide-react/dist/esm/icons/pen.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/pen.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Pen)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const Pen = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("Pen", [
  [
    "path",
    { d: "M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z", key: "5qss01" }
  ]
]);


//# sourceMappingURL=pen.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/power-off.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/power-off.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ PowerOff)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const PowerOff = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("PowerOff", [
  ["path", { d: "M18.36 6.64A9 9 0 0 1 20.77 15", key: "dxknvb" }],
  ["path", { d: "M6.16 6.16a9 9 0 1 0 12.68 12.68", key: "1x7qb5" }],
  ["path", { d: "M12 2v4", key: "3427ic" }],
  ["path", { d: "m2 2 20 20", key: "1ooewy" }]
]);


//# sourceMappingURL=power-off.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/server.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/server.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Server)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const Server = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("Server", [
  [
    "rect",
    {
      width: "20",
      height: "8",
      x: "2",
      y: "2",
      rx: "2",
      ry: "2",
      key: "ngkwjq"
    }
  ],
  [
    "rect",
    {
      width: "20",
      height: "8",
      x: "2",
      y: "14",
      rx: "2",
      ry: "2",
      key: "iecqi9"
    }
  ],
  ["line", { x1: "6", x2: "6.01", y1: "6", y2: "6", key: "16zg32" }],
  ["line", { x1: "6", x2: "6.01", y1: "18", y2: "18", key: "nzw8ys" }]
]);


//# sourceMappingURL=server.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/trash.mjs":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trash.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Trash)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const Trash = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("Trash", [
  ["path", { d: "M3 6h18", key: "d0wm0j" }],
  ["path", { d: "M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6", key: "4alrt4" }],
  ["path", { d: "M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2", key: "v07s0e" }]
]);


//# sourceMappingURL=trash.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/wifi-off.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/wifi-off.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ WifiOff)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const WifiOff = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("WifiOff", [
  ["line", { x1: "2", x2: "22", y1: "2", y2: "22", key: "a6p6uj" }],
  ["path", { d: "M8.5 16.5a5 5 0 0 1 7 0", key: "sej527" }],
  ["path", { d: "M2 8.82a15 15 0 0 1 4.17-2.65", key: "11utq1" }],
  ["path", { d: "M10.66 5c4.01-.36 8.14.9 11.34 3.76", key: "hxefdu" }],
  ["path", { d: "M16.85 11.25a10 10 0 0 1 2.22 1.68", key: "q734kn" }],
  ["path", { d: "M5 13a10 10 0 0 1 5.24-2.76", key: "piq4yl" }],
  ["line", { x1: "12", x2: "12.01", y1: "20", y2: "20", key: "of4bc4" }]
]);


//# sourceMappingURL=wifi-off.mjs.map


/***/ }),

/***/ "./src/renderer/components/Connection/ConnectionForm.tsx":
/*!***************************************************************!*\
  !*** ./src/renderer/components/Connection/ConnectionForm.tsx ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConnectionForm: () => (/* binding */ ConnectionForm)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/eye-off.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/eye.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/lock.mjs");
/* harmony import */ var _UI_Input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../UI/Input */ "./src/renderer/components/UI/Input.tsx");
/* harmony import */ var _UI_Button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../UI/Button */ "./src/renderer/components/UI/Button.tsx");
/* harmony import */ var _UI_Switch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../UI/Switch */ "./src/renderer/components/UI/Switch.tsx");
/* harmony import */ var _stores_connection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../stores/connection */ "./src/renderer/stores/connection.ts");
/* harmony import */ var _SmartConnectionInput__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./SmartConnectionInput */ "./src/renderer/components/Connection/SmartConnectionInput.tsx");
/* harmony import */ var _ConnectionTestFeedback__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ConnectionTestFeedback */ "./src/renderer/components/Connection/ConnectionTestFeedback.tsx");
/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../utils/cn */ "./src/renderer/utils/cn.ts");










const ConnectionForm = ({ connection, onSave, onCancel, className, }) => {
    const { testConnection } = (0,_stores_connection__WEBPACK_IMPORTED_MODULE_8__.useConnectionStore)();
    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({
        name: connection?.name || '',
        host: connection?.host || 'localhost',
        port: connection?.port || 9200,
        username: connection?.username || '',
        password: connection?.password || '',
        ssl: connection?.ssl || false,
    });
    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [isTesting, setIsTesting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});
    const validateForm = () => {
        const newErrors = {};
        if (!formData.name.trim()) {
            newErrors.name = '连接名称不能为空';
        }
        if (!formData.host.trim()) {
            newErrors.host = '主机地址不能为空';
        }
        if (!formData.port || formData.port < 1 || formData.port > 65535) {
            newErrors.port = '端口号必须在 1-65535 之间';
        }
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };
    const handleInputChange = (field, value) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: '' }));
        }
        // Clear test result when connection details change
        if (['host', 'port', 'username', 'password', 'ssl'].includes(field)) {
            setTestResult(null);
        }
    };
    const handleTestConnection = async () => {
        if (!validateForm())
            return;
        setIsTesting(true);
        setTestResult(null);
        try {
            const result = await testConnection(formData);
            setTestResult(result);
        }
        catch (error) {
            setTestResult({
                success: false,
                error: error instanceof Error ? error.message : '连接测试失败',
            });
        }
        finally {
            setIsTesting(false);
        }
    };
    const handleSave = () => {
        if (!validateForm())
            return;
        onSave(formData);
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_11__.cn)('space-y-6 p-6', className), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Input__WEBPACK_IMPORTED_MODULE_5__.Input, { label: "\u8FDE\u63A5\u540D\u79F0", placeholder: "\u8F93\u5165\u8FDE\u63A5\u540D\u79F0", value: formData.name, onChange: e => handleInputChange('name', e.target.value), error: errors.name, helperText: "\u4E3A\u6B64\u8FDE\u63A5\u8BBE\u7F6E\u4E00\u4E2A\u6613\u4E8E\u8BC6\u522B\u7684\u540D\u79F0" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-3 gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "col-span-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-neutral-700 dark:text-dark-text-primary mb-2", children: "\u4E3B\u673A\u5730\u5740" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_SmartConnectionInput__WEBPACK_IMPORTED_MODULE_9__.SmartConnectionInput, { value: formData.host, onChange: (value) => handleInputChange('host', value), onSuggestionSelect: (suggestion) => {
                                            handleInputChange('host', suggestion.host);
                                            handleInputChange('port', suggestion.port);
                                            handleInputChange('ssl', suggestion.ssl);
                                            if (suggestion.username) {
                                                handleInputChange('username', suggestion.username);
                                            }
                                        }, placeholder: "\u8F93\u5165\u4E3B\u673A\u5730\u5740\u6216\u9009\u62E9\u5EFA\u8BAE..." }), errors.host && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "mt-1 text-sm text-red-600 dark:text-red-400", children: errors.host }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Input__WEBPACK_IMPORTED_MODULE_5__.Input, { label: "\u7AEF\u53E3", type: "number", placeholder: "9200", value: formData.port.toString(), onChange: e => handleInputChange('port', parseInt(e.target.value) || 9200), error: errors.port, min: 1, max: 65535 }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between p-4 bg-neutral-50 dark:bg-dark-tertiary rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "text-sm font-medium text-neutral-700 dark:text-dark-text-primary", children: "\u542F\u7528 SSL/TLS" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-neutral-500 dark:text-dark-text-secondary", children: "\u4F7F\u7528 HTTPS \u534F\u8BAE\u8FDE\u63A5\u5230 Elasticsearch" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Switch__WEBPACK_IMPORTED_MODULE_7__.Switch, { checked: formData.ssl, onChange: e => handleInputChange('ssl', e.target.checked) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4 p-4 border border-neutral-200 dark:border-neutral-600 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], { className: "h-4 w-4 text-neutral-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm font-medium text-neutral-700 dark:text-dark-text-primary", children: "\u8EAB\u4EFD\u9A8C\u8BC1 (\u53EF\u9009)" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Input__WEBPACK_IMPORTED_MODULE_5__.Input, { label: "\u7528\u6237\u540D", placeholder: "\u8F93\u5165\u7528\u6237\u540D", value: formData.username, onChange: e => handleInputChange('username', e.target.value), autoComplete: "username" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Input__WEBPACK_IMPORTED_MODULE_5__.Input, { label: "\u5BC6\u7801", type: showPassword ? 'text' : 'password', placeholder: "\u8F93\u5165\u5BC6\u7801", value: formData.password, onChange: e => handleInputChange('password', e.target.value), autoComplete: "current-password", rightIcon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { type: "button", onClick: () => setShowPassword(!showPassword), className: "hover:text-neutral-600 dark:hover:text-dark-text-primary", children: showPassword ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_2__["default"], { className: "h-4 w-4" })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_3__["default"], { className: "h-4 w-4" })) }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ConnectionTestFeedback__WEBPACK_IMPORTED_MODULE_10__.ConnectionTestFeedback, { connection: formData, onTestComplete: (success, error) => {
                            setTestResult({
                                success,
                                error,
                                clusterInfo: undefined, // Will be populated by the feedback component
                            });
                        } })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-end space-x-3 pt-4 border-t border-neutral-200 dark:border-neutral-600", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_6__.Button, { variant: "secondary", onClick: onCancel, children: "\u53D6\u6D88" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_6__.Button, { onClick: handleSave, disabled: !testResult?.success, children: connection ? '更新连接' : '保存连接' })] })] }));
};


/***/ }),

/***/ "./src/renderer/components/Connection/ConnectionManager.tsx":
/*!******************************************************************!*\
  !*** ./src/renderer/components/Connection/ConnectionManager.tsx ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConnectionManager: () => (/* binding */ ConnectionManager)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/history.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/pen.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/plus.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/power-off.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/power.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/trash-2.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/trash.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/wifi-off.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/wifi.mjs");
/* harmony import */ var _stores_connection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../stores/connection */ "./src/renderer/stores/connection.ts");
/* harmony import */ var _ConnectionForm__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ConnectionForm */ "./src/renderer/components/Connection/ConnectionForm.tsx");
/* harmony import */ var _ConnectionStatusIndicator__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./ConnectionStatusIndicator */ "./src/renderer/components/Connection/ConnectionStatusIndicator.tsx");
/* harmony import */ var _services_intelligentConnection__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../services/intelligentConnection */ "./src/renderer/services/intelligentConnection.ts");
/* harmony import */ var _UI_Button__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../UI/Button */ "./src/renderer/components/UI/Button.tsx");
/* harmony import */ var _UI_Card__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../UI/Card */ "./src/renderer/components/UI/Card.tsx");
/* harmony import */ var _UI_Badge__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../UI/Badge */ "./src/renderer/components/UI/Badge.tsx");
/* harmony import */ var _UI_Modal__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../UI/Modal */ "./src/renderer/components/UI/Modal.tsx");
/* harmony import */ var _UI_HealthIndicator__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../UI/HealthIndicator */ "./src/renderer/components/UI/HealthIndicator.tsx");
/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ../../utils/cn */ "./src/renderer/utils/cn.ts");













const ConnectionManager = ({ className, }) => {
    const { connections, activeConnectionId, isConnecting, connectionError, clusterInfo, addConnection, updateConnection, deleteConnection, setActiveConnection, disconnect, } = (0,_stores_connection__WEBPACK_IMPORTED_MODULE_11__.useConnectionStore)();
    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [editingConnection, setEditingConnection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [showHistoryModal, setShowHistoryModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const handleAddConnection = () => {
        setEditingConnection(null);
        setShowForm(true);
    };
    const handleEditConnection = (connection) => {
        setEditingConnection(connection);
        setShowForm(true);
    };
    const handleSaveConnection = (connectionData) => {
        if (editingConnection) {
            updateConnection(editingConnection.id, connectionData);
        }
        else {
            addConnection(connectionData);
        }
        setShowForm(false);
        setEditingConnection(null);
    };
    const handleDeleteConnection = (id) => {
        deleteConnection(id);
        setShowDeleteConfirm(null);
    };
    const handleConnect = async (connection) => {
        try {
            await setActiveConnection(connection.id);
        }
        catch (error) {
            console.error('Failed to connect:', error);
        }
    };
    const handleDisconnect = () => {
        disconnect();
    };
    const handleClearHistory = () => {
        _services_intelligentConnection__WEBPACK_IMPORTED_MODULE_14__.intelligentConnectionService.clearConnectionHistory();
        setShowHistoryModal(false);
    };
    const connectionHistory = _services_intelligentConnection__WEBPACK_IMPORTED_MODULE_14__.intelligentConnectionService.getConnectionHistory();
    const getConnectionStatus = (connection) => {
        if (connection.isActive && !connectionError) {
            return {
                status: 'connected',
                label: '已连接',
                healthColor: 'green',
                badgeVariant: 'success',
            };
        }
        else if (connection.id === activeConnectionId && isConnecting) {
            return {
                status: 'connecting',
                label: '连接中',
                healthColor: 'yellow',
                badgeVariant: 'warning',
            };
        }
        else if (connection.id === activeConnectionId && connectionError) {
            return {
                status: 'error',
                label: '连接失败',
                healthColor: 'red',
                badgeVariant: 'error',
            };
        }
        else {
            return {
                status: 'disconnected',
                label: '未连接',
                healthColor: 'red',
                badgeVariant: 'default',
            };
        }
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_20__.cn)('space-y-6', className), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-2xl font-bold text-neutral-900 dark:text-dark-text-primary", children: "\u8FDE\u63A5\u7BA1\u7406" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-600 dark:text-dark-text-secondary mt-1", children: "\u7BA1\u7406\u60A8\u7684 Elasticsearch \u96C6\u7FA4\u8FDE\u63A5" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_15__.Button, { variant: "secondary", onClick: () => setShowHistoryModal(true), className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_2__["default"], { className: "h-4 w-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u8FDE\u63A5\u5386\u53F2" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_15__.Button, { onClick: handleAddConnection, className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], { className: "h-4 w-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u65B0\u5EFA\u8FDE\u63A5" })] })] })] }), activeConnectionId && clusterInfo && !connectionError && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_16__.Card, { className: "p-6 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start space-x-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_HealthIndicator__WEBPACK_IMPORTED_MODULE_19__.HealthIndicator, { status: "green", size: "md", showText: false }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-green-800 dark:text-green-200", children: clusterInfo.cluster_name }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-sm text-green-600 dark:text-green-300 mt-1", children: ["\u8FDE\u63A5:", ' ', connections.find(c => c.id === activeConnectionId)?.name] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-2 gap-4 mt-3 text-sm", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-green-700 dark:text-green-300 font-medium", children: "\u7248\u672C:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-green-600 dark:text-green-400 ml-2", children: clusterInfo.version.number })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-green-700 dark:text-green-300 font-medium", children: "\u6784\u5EFA\u7C7B\u578B:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-green-600 dark:text-green-400 ml-2", children: clusterInfo.version.build_flavor })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-green-700 dark:text-green-300 font-medium", children: "Lucene:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-green-600 dark:text-green-400 ml-2", children: clusterInfo.version.lucene_version })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-green-700 dark:text-green-300 font-medium", children: "UUID:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "text-green-600 dark:text-green-400 ml-2 font-mono text-xs", children: [clusterInfo.cluster_uuid.substring(0, 8), "..."] })] })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_15__.Button, { variant: "secondary", size: "sm", onClick: handleDisconnect, className: "text-green-700 dark:text-green-300 hover:text-green-800 dark:hover:text-green-200", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], { className: "h-4 w-4 mr-2" }), "\u65AD\u5F00\u8FDE\u63A5"] })] }) })), connectionError && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_16__.Card, { className: "p-4 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"], { className: "h-5 w-5 text-red-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "font-medium text-red-800 dark:text-red-200", children: "\u8FDE\u63A5\u5931\u8D25" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-red-600 dark:text-red-300", children: connectionError })] })] }) })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-4", children: connections.length === 0 ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_16__.Card, { className: "p-8 text-center", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex flex-col items-center space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "p-3 bg-neutral-100 dark:bg-dark-tertiary rounded-full", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_10__["default"], { className: "h-8 w-8 text-neutral-400" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium text-neutral-900 dark:text-dark-text-primary", children: "\u6682\u65E0\u8FDE\u63A5" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-neutral-600 dark:text-dark-text-secondary mt-1", children: "\u521B\u5EFA\u60A8\u7684\u7B2C\u4E00\u4E2A Elasticsearch \u8FDE\u63A5\u5F00\u59CB\u4F7F\u7528" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_15__.Button, { onClick: handleAddConnection, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], { className: "h-4 w-4 mr-2" }), "\u65B0\u5EFA\u8FDE\u63A5"] })] }) })) : (connections.map(connection => {
                    const status = getConnectionStatus(connection);
                    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_16__.Card, { className: "p-4", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center space-x-4 flex-1", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2 mb-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "font-medium text-neutral-900 dark:text-dark-text-primary", children: connection.name }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Badge__WEBPACK_IMPORTED_MODULE_17__.Badge, { variant: status.badgeVariant, children: status.label })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ConnectionStatusIndicator__WEBPACK_IMPORTED_MODULE_13__.ConnectionStatusIndicator, { connectionId: connection.id, showDetails: true, size: "sm", className: "mb-2" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-4 text-sm text-neutral-600 dark:text-dark-text-secondary", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { children: [connection.ssl ? 'https' : 'http', "://", connection.host, ":", connection.port] }), connection.username && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "flex items-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "w-1 h-1 bg-neutral-400 rounded-full mr-2" }), "\u7528\u6237: ", connection.username] }))] })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [connection.isActive ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_15__.Button, { variant: "secondary", size: "sm", onClick: handleDisconnect, disabled: isConnecting, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], { className: "h-4 w-4" }) })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_15__.Button, { variant: "secondary", size: "sm", onClick: () => handleConnect(connection), loading: isConnecting && activeConnectionId === connection.id, disabled: isConnecting, children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"], { className: "h-4 w-4" }) })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_15__.Button, { variant: "ghost", size: "sm", onClick: () => handleEditConnection(connection), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_3__["default"], { className: "h-4 w-4" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_15__.Button, { variant: "ghost", size: "sm", onClick: () => setShowDeleteConfirm(connection.id), className: "text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"], { className: "h-4 w-4" }) })] })] }) }, connection.id));
                })) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Modal__WEBPACK_IMPORTED_MODULE_18__.Modal, { isOpen: showForm, onClose: () => {
                    setShowForm(false);
                    setEditingConnection(null);
                }, title: editingConnection ? '编辑连接' : '新建连接', size: "lg", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_ConnectionForm__WEBPACK_IMPORTED_MODULE_12__.ConnectionForm, { connection: editingConnection || undefined, onSave: handleSaveConnection, onCancel: () => {
                        setShowForm(false);
                        setEditingConnection(null);
                    } }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Modal__WEBPACK_IMPORTED_MODULE_18__.Modal, { isOpen: !!showDeleteConfirm, onClose: () => setShowDeleteConfirm(null), title: "\u786E\u8BA4\u5220\u9664", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-neutral-600 dark:text-dark-text-secondary", children: "\u786E\u5B9A\u8981\u5220\u9664\u8FD9\u4E2A\u8FDE\u63A5\u5417\uFF1F\u6B64\u64CD\u4F5C\u65E0\u6CD5\u64A4\u9500\u3002" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-end space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_15__.Button, { variant: "secondary", onClick: () => setShowDeleteConfirm(null), children: "\u53D6\u6D88" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_15__.Button, { variant: "danger", onClick: () => showDeleteConfirm && handleDeleteConnection(showDeleteConfirm), children: "\u5220\u9664" })] })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Modal__WEBPACK_IMPORTED_MODULE_18__.Modal, { isOpen: showHistoryModal, onClose: () => setShowHistoryModal(false), title: "\u8FDE\u63A5\u5386\u53F2", size: "lg", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-600 dark:text-dark-text-secondary", children: "\u663E\u793A\u6700\u8FD1\u7684\u8FDE\u63A5\u8BB0\u5F55\uFF0C\u5305\u62EC\u6210\u529F\u548C\u5931\u8D25\u7684\u8FDE\u63A5\u5C1D\u8BD5" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_15__.Button, { variant: "secondary", size: "sm", onClick: handleClearHistory, className: "text-red-600 hover:text-red-700", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_8__["default"], { className: "h-4 w-4 mr-1" }), "\u6E05\u7A7A\u5386\u53F2"] })] }), connectionHistory.length === 0 ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center py-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_2__["default"], { className: "h-12 w-12 text-neutral-400 mx-auto mb-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-neutral-500 dark:text-dark-text-secondary", children: "\u6682\u65E0\u8FDE\u63A5\u5386\u53F2\u8BB0\u5F55" })] })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-2 max-h-96 overflow-y-auto", children: connectionHistory.map((historyItem) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between p-3 bg-neutral-50 dark:bg-dark-tertiary rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_20__.cn)('w-2 h-2 rounded-full', historyItem.success ? 'bg-green-500' : 'bg-red-500') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "font-medium text-sm text-neutral-900 dark:text-dark-text-primary", children: [historyItem.host, ":", historyItem.port, historyItem.ssl && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "ml-2 text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded", children: "SSL" }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-xs text-neutral-500 dark:text-dark-text-secondary", children: [historyItem.username && `用户: ${historyItem.username} • `, "\u4F7F\u7528 ", historyItem.useCount, " \u6B21", historyItem.connectionTime && ` • ${historyItem.connectionTime}ms`] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-xs text-neutral-400", children: historyItem.lastUsed.toLocaleString() })] }, historyItem.id))) }))] }) })] }));
};


/***/ }),

/***/ "./src/renderer/components/Connection/ConnectionStatusIndicator.tsx":
/*!**************************************************************************!*\
  !*** ./src/renderer/components/Connection/ConnectionStatusIndicator.tsx ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConnectionStatusIndicator: () => (/* binding */ ConnectionStatusIndicator)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/alert-circle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/check-circle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/clock.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/loader-2.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/wifi-off.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/zap.mjs");
/* harmony import */ var _services_intelligentConnection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../services/intelligentConnection */ "./src/renderer/services/intelligentConnection.ts");
/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../utils/cn */ "./src/renderer/utils/cn.ts");





const ConnectionStatusIndicator = ({ connectionId, showDetails = false, size = 'md', className, }) => {
    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_services_intelligentConnection__WEBPACK_IMPORTED_MODULE_8__.intelligentConnectionService.getConnectionStatus(connectionId));
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
        const unsubscribe = _services_intelligentConnection__WEBPACK_IMPORTED_MODULE_8__.intelligentConnectionService.subscribeToStatusUpdates((newStatus) => setStatus(newStatus));
        return unsubscribe;
    }, []);
    const getStatusIcon = () => {
        const iconClass = (0,_utils_cn__WEBPACK_IMPORTED_MODULE_9__.cn)(size === 'sm' && 'h-3 w-3', size === 'md' && 'h-4 w-4', size === 'lg' && 'h-5 w-5');
        switch (status.status) {
            case 'connected':
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_3__["default"], { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_9__.cn)(iconClass, 'text-green-500') });
            case 'connecting':
            case 'testing':
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_9__.cn)(iconClass, 'text-blue-500 animate-spin') });
            case 'error':
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_2__["default"], { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_9__.cn)(iconClass, 'text-red-500') });
            case 'disconnected':
            default:
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"], { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_9__.cn)(iconClass, 'text-gray-400') });
        }
    };
    const getStatusColor = () => {
        switch (status.healthColor) {
            case 'green':
                return 'text-green-600 bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800 dark:text-green-400';
            case 'yellow':
                return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-400';
            case 'red':
                return 'text-red-600 bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800 dark:text-red-400';
            case 'blue':
                return 'text-blue-600 bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-400';
            case 'gray':
            default:
                return 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-900/20 dark:border-gray-800 dark:text-gray-400';
        }
    };
    const getPulseAnimation = () => {
        if (status.status === 'connecting' || status.status === 'testing') {
            return 'animate-pulse';
        }
        return '';
    };
    if (!showDetails) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_9__.cn)('flex items-center space-x-2', className), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_9__.cn)('relative', getPulseAnimation()), children: [getStatusIcon(), (status.status === 'connecting' || status.status === 'testing') && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute inset-0 rounded-full border-2 border-blue-300 animate-ping" }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_9__.cn)('text-sm font-medium', status.healthColor === 'green' && 'text-green-600 dark:text-green-400', status.healthColor === 'yellow' && 'text-yellow-600 dark:text-yellow-400', status.healthColor === 'red' && 'text-red-600 dark:text-red-400', status.healthColor === 'blue' && 'text-blue-600 dark:text-blue-400', status.healthColor === 'gray' && 'text-gray-600 dark:text-gray-400'), children: status.label })] }));
    }
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_9__.cn)('flex items-center justify-between p-3 rounded-lg border transition-all duration-200', getStatusColor(), getPulseAnimation(), className), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "relative", children: [getStatusIcon(), (status.status === 'connecting' || status.status === 'testing') && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute inset-0 rounded-full border-2 border-current opacity-30 animate-ping" }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_9__.cn)('font-medium', size === 'sm' && 'text-sm', size === 'md' && 'text-base', size === 'lg' && 'text-lg'), children: status.label }), status.responseTime && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1 text-xs opacity-75", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"], { className: "h-3 w-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { children: [status.responseTime, "ms"] })] }))] }), status.lastChecked && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1 text-xs opacity-75 mt-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], { className: "h-3 w-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { children: ["\u6700\u540E\u68C0\u67E5: ", status.lastChecked.toLocaleTimeString()] })] }))] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_9__.cn)('w-2 h-2 rounded-full', status.healthColor === 'green' && 'bg-green-500', status.healthColor === 'yellow' && 'bg-yellow-500', status.healthColor === 'red' && 'bg-red-500', status.healthColor === 'blue' && 'bg-blue-500', status.healthColor === 'gray' && 'bg-gray-400', (status.status === 'connecting' || status.status === 'testing') && 'animate-pulse') })] }));
};


/***/ }),

/***/ "./src/renderer/components/Connection/ConnectionTestFeedback.tsx":
/*!***********************************************************************!*\
  !*** ./src/renderer/components/Connection/ConnectionTestFeedback.tsx ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConnectionTestFeedback: () => (/* binding */ ConnectionTestFeedback)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/check-circle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/clock.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/database.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/loader-2.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/play.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/shield.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/wifi.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/x-circle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/zap.mjs");
/* harmony import */ var _services_intelligentConnection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../services/intelligentConnection */ "./src/renderer/services/intelligentConnection.ts");
/* harmony import */ var _UI_Button__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../UI/Button */ "./src/renderer/components/UI/Button.tsx");
/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../utils/cn */ "./src/renderer/utils/cn.ts");






const ConnectionTestFeedback = ({ connection, onTestComplete, className, }) => {
    const [isTestingConnection, setIsTestingConnection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [testFeedback, setTestFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const handleTestConnection = async () => {
        if (!connection.host || !connection.port) {
            return;
        }
        setIsTestingConnection(true);
        setTestFeedback(null);
        setTestResult(null);
        try {
            const result = await _services_intelligentConnection__WEBPACK_IMPORTED_MODULE_11__.intelligentConnectionService.testConnectionWithFeedback(connection, (feedback) => {
                setTestFeedback(feedback);
            });
            setTestResult({
                success: result.success,
                error: result.error,
            });
            onTestComplete?.(result.success, result.error);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : '测试连接时发生未知错误';
            setTestResult({
                success: false,
                error: errorMessage,
            });
            onTestComplete?.(false, errorMessage);
        }
        finally {
            setIsTestingConnection(false);
        }
    };
    const getStageIcon = (stage) => {
        const iconClass = 'h-4 w-4';
        switch (stage) {
            case 'connecting':
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_8__["default"], { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)(iconClass, 'text-blue-500') });
            case 'authenticating':
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"], { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)(iconClass, 'text-yellow-500') });
            case 'fetching_info':
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)(iconClass, 'text-purple-500') });
            case 'complete':
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_2__["default"], { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)(iconClass, 'text-green-500') });
            case 'error':
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"], { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)(iconClass, 'text-red-500') });
            default:
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)(iconClass, 'text-gray-500 animate-spin') });
        }
    };
    const getProgressColor = (progress) => {
        if (progress >= 100)
            return 'bg-green-500';
        if (progress >= 75)
            return 'bg-blue-500';
        if (progress >= 50)
            return 'bg-yellow-500';
        return 'bg-gray-400';
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)('space-y-4', className), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_12__.Button, { onClick: handleTestConnection, loading: isTestingConnection, disabled: !connection.host || !connection.port || isTestingConnection, className: "w-full", variant: "secondary", children: isTestingConnection ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], { className: "h-4 w-4 animate-spin" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u6D4B\u8BD5\u8FDE\u63A5\u4E2D..." })] })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"], { className: "h-4 w-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u6D4B\u8BD5\u8FDE\u63A5" })] })) }), testFeedback && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-3 p-4 bg-neutral-50 dark:bg-dark-tertiary rounded-lg border", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [getStageIcon(testFeedback.stage), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm font-medium text-neutral-700 dark:text-dark-text-primary", children: testFeedback.message })] }), testFeedback.duration && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1 text-xs text-neutral-500", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_3__["default"], { className: "h-3 w-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { children: [testFeedback.duration, "ms"] })] }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)('h-2 rounded-full transition-all duration-300', getProgressColor(testFeedback.progress), testFeedback.stage === 'error' && 'bg-red-500'), style: { width: `${testFeedback.progress}%` } }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex justify-between items-center text-xs text-neutral-500", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "\u8FDB\u5EA6" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { children: [testFeedback.progress, "%"] })] }), testFeedback.error && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-sm text-red-700 dark:text-red-400", children: testFeedback.error }))] })), testResult && !isTestingConnection && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)('p-4 rounded-lg border transition-all duration-200', testResult.success
                    ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                    : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start space-x-3", children: [testResult.success ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_2__["default"], { className: "h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"], { className: "h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)('font-medium text-sm', testResult.success
                                        ? 'text-green-800 dark:text-green-200'
                                        : 'text-red-800 dark:text-red-200'), children: testResult.success ? '连接测试成功！' : '连接测试失败' }), testResult.error && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)('text-xs mt-1', testResult.success
                                        ? 'text-green-600 dark:text-green-300'
                                        : 'text-red-600 dark:text-red-300'), children: testResult.error })), testResult.success && testFeedback?.duration && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-4 mt-2 text-xs text-green-600 dark:text-green-300", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_10__["default"], { className: "h-3 w-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { children: ["\u54CD\u5E94\u65F6\u95F4: ", testFeedback.duration, "ms"] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_3__["default"], { className: "h-3 w-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { children: ["\u6D4B\u8BD5\u5B8C\u6210\u4E8E: ", new Date().toLocaleTimeString()] })] })] }))] })] }) }))] }));
};


/***/ }),

/***/ "./src/renderer/components/Connection/SmartConnectionInput.tsx":
/*!*********************************************************************!*\
  !*** ./src/renderer/components/Connection/SmartConnectionInput.tsx ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SmartConnectionInput: () => (/* binding */ SmartConnectionInput)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/check.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/chevron-down.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/clock.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/globe.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/history.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/server.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/wifi.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/zap.mjs");
/* harmony import */ var _services_intelligentConnection__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../services/intelligentConnection */ "./src/renderer/services/intelligentConnection.ts");
/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../utils/cn */ "./src/renderer/utils/cn.ts");





const SmartConnectionInput = ({ value, onChange, onSuggestionSelect, placeholder = "输入主机地址...", className, }) => {
    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);
    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
    const suggestionsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
        const loadSuggestions = async () => {
            if (value.length >= 1) {
                setIsLoading(true);
                try {
                    const results = await _services_intelligentConnection__WEBPACK_IMPORTED_MODULE_10__.intelligentConnectionService.getSuggestions(value);
                    setSuggestions(results);
                    setShowSuggestions(true);
                    setSelectedIndex(-1);
                }
                catch (error) {
                    console.warn('Failed to load suggestions:', error);
                    setSuggestions([]);
                }
                finally {
                    setIsLoading(false);
                }
            }
            else {
                setSuggestions([]);
                setShowSuggestions(false);
            }
        };
        const debounceTimer = setTimeout(loadSuggestions, 200);
        return () => clearTimeout(debounceTimer);
    }, [value]);
    const handleInputChange = (e) => {
        onChange(e.target.value);
    };
    const handleKeyDown = (e) => {
        if (!showSuggestions || suggestions.length === 0)
            return;
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                setSelectedIndex(prev => prev < suggestions.length - 1 ? prev + 1 : 0);
                break;
            case 'ArrowUp':
                e.preventDefault();
                setSelectedIndex(prev => prev > 0 ? prev - 1 : suggestions.length - 1);
                break;
            case 'Enter':
                e.preventDefault();
                if (selectedIndex >= 0) {
                    handleSuggestionClick(suggestions[selectedIndex]);
                }
                break;
            case 'Escape':
                setShowSuggestions(false);
                setSelectedIndex(-1);
                break;
        }
    };
    const handleSuggestionClick = (suggestion) => {
        onChange(suggestion.host);
        onSuggestionSelect(suggestion);
        setShowSuggestions(false);
        setSelectedIndex(-1);
    };
    const handleInputFocus = () => {
        if (suggestions.length > 0) {
            setShowSuggestions(true);
        }
    };
    const handleInputBlur = () => {
        // Delay hiding suggestions to allow clicking
        setTimeout(() => {
            setShowSuggestions(false);
            setSelectedIndex(-1);
        }, 200);
    };
    const getSuggestionIcon = (type) => {
        switch (type) {
            case 'history':
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"], { className: "h-4 w-4 text-blue-500" });
            case 'local':
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_8__["default"], { className: "h-4 w-4 text-green-500" });
            case 'common':
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], { className: "h-4 w-4 text-gray-500" });
            default:
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"], { className: "h-4 w-4 text-gray-500" });
        }
    };
    const getConfidenceColor = (confidence) => {
        if (confidence >= 0.8)
            return 'text-green-600';
        if (confidence >= 0.6)
            return 'text-yellow-600';
        return 'text-gray-600';
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_11__.cn)('relative', className), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "relative", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { ref: inputRef, type: "text", value: value, onChange: handleInputChange, onKeyDown: handleKeyDown, onFocus: handleInputFocus, onBlur: handleInputBlur, placeholder: placeholder, className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_11__.cn)('w-full px-4 py-2 pl-10 pr-10 text-sm border border-neutral-300 rounded-lg', 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent', 'dark:bg-dark-secondary dark:border-neutral-600 dark:text-dark-text-primary', 'dark:placeholder-dark-text-secondary') }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"], { className: "absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400" }), isLoading && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute right-3 top-1/2 transform -translate-y-1/2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full" }) })), showSuggestions && suggestions.length > 0 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_3__["default"], { className: "absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400" }))] }), showSuggestions && suggestions.length > 0 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { ref: suggestionsRef, className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_11__.cn)('absolute z-50 w-full mt-1 bg-white dark:bg-dark-secondary border border-neutral-200 dark:border-neutral-600', 'rounded-lg shadow-lg max-h-64 overflow-y-auto'), children: suggestions.map((suggestion, index) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { onClick: () => handleSuggestionClick(suggestion), className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_11__.cn)('flex items-center justify-between px-4 py-3 cursor-pointer transition-colors', 'hover:bg-neutral-50 dark:hover:bg-dark-tertiary', selectedIndex === index && 'bg-blue-50 dark:bg-blue-900/20', index !== suggestions.length - 1 && 'border-b border-neutral-100 dark:border-neutral-700'), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3 flex-1 min-w-0", children: [getSuggestionIcon(suggestion.type), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1 min-w-0", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "font-medium text-neutral-900 dark:text-dark-text-primary truncate", children: [suggestion.host, ":", suggestion.port] }), suggestion.ssl && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded", children: "SSL" }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2 mt-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-xs text-neutral-500 dark:text-dark-text-secondary truncate", children: suggestion.description }), suggestion.lastUsed && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1 text-xs text-neutral-400", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], { className: "h-3 w-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: suggestion.lastUsed.toLocaleDateString() })] }))] }), suggestion.username && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-xs text-neutral-400 mt-1", children: ["\u7528\u6237: ", suggestion.username] }))] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2 ml-2", children: [suggestion.useCount && suggestion.useCount > 1 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "text-xs text-neutral-400", children: [suggestion.useCount, "\u6B21"] })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_11__.cn)('flex items-center space-x-1 text-xs', getConfidenceColor(suggestion.confidence)), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"], { className: "h-3 w-3" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { children: [Math.round(suggestion.confidence * 100), "%"] })] }), selectedIndex === index && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_2__["default"], { className: "h-4 w-4 text-blue-500" }))] })] }, suggestion.id))) }))] }));
};


/***/ })

}]);
//# sourceMappingURL=src_renderer_components_Connection_ConnectionManager_tsx.renderer.js.map