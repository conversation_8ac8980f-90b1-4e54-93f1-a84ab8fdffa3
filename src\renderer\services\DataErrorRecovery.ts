// 数据加载错误恢复系统
interface DataLoadError {
  indexName: string;
  operation: 'fetch' | 'search' | 'update' | 'delete' | 'create' | 'bulk';
  error: Error;
  retryCount: number;
  timestamp: number;
  context?: any;
}

interface RecoveryStrategy {
  name: string;
  canHandle: (error: DataLoadError) => boolean;
  recover: (error: DataLoadError, originalFunction: Function) => Promise<any>;
  priority: number;
}

interface RecoveryOptions {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  enableExponentialBackoff: boolean;
  enableCircuitBreaker: boolean;
  circuitBreakerThreshold: number;
  circuitBreakerTimeout: number;
}

interface CircuitBreakerState {
  state: 'closed' | 'open' | 'half-open';
  failureCount: number;
  lastFailureTime: number;
  nextAttemptTime: number;
}

export class DataErrorRecovery {
  private strategies: RecoveryStrategy[] = [];
  private options: RecoveryOptions;
  private circuitBreakers = new Map<string, CircuitBreakerState>();
  private errorHistory: DataLoadError[] = [];
  private onErrorCallback?: (error: DataLoadError) => void;
  private onRecoveryCallback?: (error: DataLoadError, success: boolean, result?: any) => void;

  constructor(options: Partial<RecoveryOptions> = {}) {
    this.options = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      enableExponentialBackoff: true,
      enableCircuitBreaker: true,
      circuitBreakerThreshold: 5,
      circuitBreakerTimeout: 30000,
      ...options
    };

    this.initializeDefaultStrategies();
  }

  // 初始化默认恢复策略
  private initializeDefaultStrategies(): void {
    // 网络错误恢复策略
    this.addStrategy({
      name: 'NetworkErrorRecovery',
      priority: 1,
      canHandle: (error) => {
        const message = error.error.message.toLowerCase();
        return message.includes('network') || 
               message.includes('fetch') || 
               message.includes('connection');
      },
      recover: async (error, originalFunction) => {
        await this.delay(this.calculateDelay(error.retryCount));
        return await originalFunction();
      }
    });

    // 超时错误恢复策略
    this.addStrategy({
      name: 'TimeoutRecovery',
      priority: 2,
      canHandle: (error) => {
        return error.error.message.toLowerCase().includes('timeout');
      },
      recover: async (error, originalFunction) => {
        // 超时错误使用更长的延迟
        await this.delay(this.calculateDelay(error.retryCount) * 2);
        return await originalFunction();
      }
    });

    // 服务器错误恢复策略
    this.addStrategy({
      name: 'ServerErrorRecovery',
      priority: 3,
      canHandle: (error) => {
        const message = error.error.message.toLowerCase();
        return message.includes('500') || 
               message.includes('502') || 
               message.includes('503') || 
               message.includes('server');
      },
      recover: async (error, originalFunction) => {
        // 服务器错误使用指数退避
        const delay = Math.min(
          this.options.baseDelay * Math.pow(2, error.retryCount),
          this.options.maxDelay
        );
        await this.delay(delay);
        return await originalFunction();
      }
    });

    // 权限错误恢复策略
    this.addStrategy({
      name: 'PermissionErrorRecovery',
      priority: 4,
      canHandle: (error) => {
        const message = error.error.message.toLowerCase();
        return message.includes('unauthorized') || 
               message.includes('forbidden') || 
               message.includes('403') ||
               message.includes('401');
      },
      recover: async (error, originalFunction) => {
        // 权限错误通常需要重新认证，这里只是简单重试
        await this.delay(this.options.baseDelay);
        return await originalFunction();
      }
    });

    // 数据格式错误恢复策略
    this.addStrategy({
      name: 'DataFormatRecovery',
      priority: 5,
      canHandle: (error) => {
        const message = error.error.message.toLowerCase();
        return message.includes('json') || 
               message.includes('parse') || 
               message.includes('format');
      },
      recover: async (error, originalFunction) => {
        // 数据格式错误通常不需要重试，但可以尝试一次
        if (error.retryCount === 0) {
          await this.delay(this.options.baseDelay);
          return await originalFunction();
        }
        throw error.error; // 不再重试
      }
    });
  }

  // 添加恢复策略
  addStrategy(strategy: RecoveryStrategy): void {
    this.strategies.push(strategy);
    this.strategies.sort((a, b) => a.priority - b.priority);
  }

  // 移除恢复策略
  removeStrategy(name: string): boolean {
    const index = this.strategies.findIndex(s => s.name === name);
    if (index !== -1) {
      this.strategies.splice(index, 1);
      return true;
    }
    return false;
  }

  // 设置错误回调
  setErrorCallback(callback: (error: DataLoadError) => void): void {
    this.onErrorCallback = callback;
  }

  // 设置恢复回调
  setRecoveryCallback(callback: (error: DataLoadError, success: boolean, result?: any) => void): void {
    this.onRecoveryCallback = callback;
  }

  // 处理数据加载错误
  async handleDataError(
    error: Error,
    indexName: string,
    operation: DataLoadError['operation'],
    originalFunction: Function,
    context?: any
  ): Promise<any> {
    const dataError: DataLoadError = {
      indexName,
      operation,
      error,
      retryCount: 0,
      timestamp: Date.now(),
      context
    };

    return this.attemptRecovery(dataError, originalFunction);
  }

  // 尝试恢复
  private async attemptRecovery(error: DataLoadError, originalFunction: Function): Promise<any> {
    // 检查熔断器
    if (this.options.enableCircuitBreaker && this.isCircuitBreakerOpen(error.indexName)) {
      throw new Error(`Circuit breaker is open for index ${error.indexName}`);
    }

    // 记录错误
    this.errorHistory.push(error);
    this.onErrorCallback?.(error);

    // 检查是否超过最大重试次数
    if (error.retryCount >= this.options.maxRetries) {
      this.recordFailure(error.indexName);
      throw new Error(`Max retries exceeded for ${error.operation} on index ${error.indexName}`);
    }

    // 查找合适的恢复策略
    const strategy = this.findStrategy(error);
    if (!strategy) {
      this.recordFailure(error.indexName);
      throw error.error;
    }

    try {
      console.log(`Attempting recovery using strategy ${strategy.name} for ${error.operation} on index ${error.indexName}`);
      
      // 增加重试计数
      error.retryCount++;
      
      // 执行恢复策略
      const result = await strategy.recover(error, originalFunction);
      
      // 恢复成功
      this.recordSuccess(error.indexName);
      this.onRecoveryCallback?.(error, true, result);
      
      console.log(`Successfully recovered ${error.operation} on index ${error.indexName} using ${strategy.name}`);
      return result;
    } catch (recoveryError) {
      console.warn(`Recovery failed for ${error.operation} on index ${error.indexName}:`, recoveryError);
      
      // 更新错误信息
      error.error = recoveryError as Error;
      error.timestamp = Date.now();
      
      // 递归重试
      return this.attemptRecovery(error, originalFunction);
    }
  }

  // 查找合适的恢复策略
  private findStrategy(error: DataLoadError): RecoveryStrategy | null {
    return this.strategies.find(strategy => strategy.canHandle(error)) || null;
  }

  // 计算延迟时间
  private calculateDelay(retryCount: number): number {
    if (!this.options.enableExponentialBackoff) {
      return this.options.baseDelay;
    }

    const delay = this.options.baseDelay * Math.pow(2, retryCount);
    return Math.min(delay, this.options.maxDelay);
  }

  // 延迟函数
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 检查熔断器是否打开
  private isCircuitBreakerOpen(indexName: string): boolean {
    const breaker = this.circuitBreakers.get(indexName);
    if (!breaker) return false;

    const now = Date.now();
    
    switch (breaker.state) {
      case 'open':
        if (now >= breaker.nextAttemptTime) {
          breaker.state = 'half-open';
          return false;
        }
        return true;
      
      case 'half-open':
        return false;
      
      case 'closed':
      default:
        return false;
    }
  }

  // 记录失败
  private recordFailure(indexName: string): void {
    if (!this.options.enableCircuitBreaker) return;

    let breaker = this.circuitBreakers.get(indexName);
    if (!breaker) {
      breaker = {
        state: 'closed',
        failureCount: 0,
        lastFailureTime: 0,
        nextAttemptTime: 0
      };
      this.circuitBreakers.set(indexName, breaker);
    }

    breaker.failureCount++;
    breaker.lastFailureTime = Date.now();

    if (breaker.failureCount >= this.options.circuitBreakerThreshold) {
      breaker.state = 'open';
      breaker.nextAttemptTime = Date.now() + this.options.circuitBreakerTimeout;
      console.warn(`Circuit breaker opened for index ${indexName}`);
    }
  }

  // 记录成功
  private recordSuccess(indexName: string): void {
    const breaker = this.circuitBreakers.get(indexName);
    if (breaker) {
      if (breaker.state === 'half-open') {
        breaker.state = 'closed';
        breaker.failureCount = 0;
        console.log(`Circuit breaker closed for index ${indexName}`);
      } else if (breaker.state === 'closed') {
        breaker.failureCount = Math.max(0, breaker.failureCount - 1);
      }
    }
  }

  // 获取错误统计
  getErrorStats(): {
    totalErrors: number;
    errorsByOperation: Record<string, number>;
    errorsByIndex: Record<string, number>;
    averageRetryCount: number;
    circuitBreakerStates: Record<string, string>;
  } {
    const errorsByOperation: Record<string, number> = {};
    const errorsByIndex: Record<string, number> = {};
    let totalRetries = 0;

    this.errorHistory.forEach(error => {
      errorsByOperation[error.operation] = (errorsByOperation[error.operation] || 0) + 1;
      errorsByIndex[error.indexName] = (errorsByIndex[error.indexName] || 0) + 1;
      totalRetries += error.retryCount;
    });

    const circuitBreakerStates: Record<string, string> = {};
    this.circuitBreakers.forEach((breaker, indexName) => {
      circuitBreakerStates[indexName] = breaker.state;
    });

    return {
      totalErrors: this.errorHistory.length,
      errorsByOperation,
      errorsByIndex,
      averageRetryCount: this.errorHistory.length > 0 ? totalRetries / this.errorHistory.length : 0,
      circuitBreakerStates
    };
  }

  // 重置熔断器
  resetCircuitBreaker(indexName: string): void {
    const breaker = this.circuitBreakers.get(indexName);
    if (breaker) {
      breaker.state = 'closed';
      breaker.failureCount = 0;
      breaker.nextAttemptTime = 0;
      console.log(`Circuit breaker reset for index ${indexName}`);
    }
  }

  // 清除错误历史
  clearErrorHistory(): void {
    this.errorHistory = [];
  }

  // 获取最近的错误
  getRecentErrors(count: number = 10): DataLoadError[] {
    return this.errorHistory.slice(-count);
  }
}
