// 索引切换错误处理系统
interface IndexSwitchError {
  type: 'connection_lost' | 'index_not_found' | 'permission_denied' | 'timeout' | 'network_error' | 'server_error';
  message: string;
  indexName: string;
  originalError?: Error;
  recoveryAction?: () => Promise<void>;
  fallbackIndex?: string;
  timestamp: number;
  retryCount: number;
}

interface ErrorRecoveryOptions {
  maxRetries: number;
  retryDelay: number;
  timeoutMs: number;
  enableFallback: boolean;
  enableAutoRecovery: boolean;
}

interface ErrorStats {
  totalErrors: number;
  errorsByType: Record<string, number>;
  recoverySuccessRate: number;
  averageRecoveryTime: number;
}

export class IndexSwitchErrorHandler {
  private errorHistory: IndexSwitchError[] = [];
  private recoveryAttempts = new Map<string, number>();
  private options: ErrorRecoveryOptions;
  private onErrorCallback?: (error: IndexSwitchError) => void;
  private onRecoveryCallback?: (indexName: string, success: boolean) => void;

  constructor(options: Partial<ErrorRecoveryOptions> = {}) {
    this.options = {
      maxRetries: 3,
      retryDelay: 1000,
      timeoutMs: 10000,
      enableFallback: true,
      enableAutoRecovery: true,
      ...options
    };
  }

  // 设置错误回调
  setErrorCallback(callback: (error: IndexSwitchError) => void): void {
    this.onErrorCallback = callback;
  }

  // 设置恢复回调
  setRecoveryCallback(callback: (indexName: string, success: boolean) => void): void {
    this.onRecoveryCallback = callback;
  }

  // 处理索引切换错误
  async handleError(
    error: Error,
    indexName: string,
    switchFunction: (indexName: string) => Promise<void>,
    fallbackIndex?: string
  ): Promise<boolean> {
    const errorType = this.classifyError(error);
    const switchError: IndexSwitchError = {
      type: errorType,
      message: error.message,
      indexName,
      originalError: error,
      fallbackIndex,
      timestamp: Date.now(),
      retryCount: this.recoveryAttempts.get(indexName) || 0
    };

    // 记录错误
    this.errorHistory.push(switchError);
    this.onErrorCallback?.(switchError);

    // 尝试恢复
    const recovered = await this.attemptRecovery(switchError, switchFunction);
    this.onRecoveryCallback?.(indexName, recovered);

    return recovered;
  }

  // 分类错误类型
  private classifyError(error: Error): IndexSwitchError['type'] {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'network_error';
    }
    
    if (message.includes('timeout')) {
      return 'timeout';
    }
    
    if (message.includes('not found') || message.includes('404')) {
      return 'index_not_found';
    }
    
    if (message.includes('permission') || message.includes('unauthorized') || message.includes('403')) {
      return 'permission_denied';
    }
    
    if (message.includes('connection') || message.includes('connect')) {
      return 'connection_lost';
    }
    
    if (message.includes('server') || message.includes('500') || message.includes('502') || message.includes('503')) {
      return 'server_error';
    }
    
    return 'network_error'; // 默认类型
  }

  // 尝试恢复
  private async attemptRecovery(
    error: IndexSwitchError,
    switchFunction: (indexName: string) => Promise<void>
  ): Promise<boolean> {
    const { indexName, type, retryCount } = error;
    
    // 检查是否超过最大重试次数
    if (retryCount >= this.options.maxRetries) {
      console.warn(`Max retries exceeded for index ${indexName}`);
      return this.tryFallback(error, switchFunction);
    }

    // 根据错误类型选择恢复策略
    switch (type) {
      case 'connection_lost':
        return this.recoverFromConnectionLoss(error, switchFunction);
      
      case 'index_not_found':
        return this.recoverFromIndexNotFound(error, switchFunction);
      
      case 'permission_denied':
        return this.recoverFromPermissionDenied(error, switchFunction);
      
      case 'timeout':
        return this.recoverFromTimeout(error, switchFunction);
      
      case 'network_error':
        return this.recoverFromNetworkError(error, switchFunction);
      
      case 'server_error':
        return this.recoverFromServerError(error, switchFunction);
      
      default:
        return this.tryFallback(error, switchFunction);
    }
  }

  // 从连接丢失中恢复
  private async recoverFromConnectionLoss(
    error: IndexSwitchError,
    switchFunction: (indexName: string) => Promise<void>
  ): Promise<boolean> {
    console.log(`Attempting to recover from connection loss for index ${error.indexName}`);
    
    // 等待一段时间后重试
    await this.delay(this.options.retryDelay * (error.retryCount + 1));
    
    try {
      // 增加重试计数
      this.recoveryAttempts.set(error.indexName, error.retryCount + 1);
      
      // 重新尝试连接
      await switchFunction(error.indexName);
      
      // 成功恢复，清除重试计数
      this.recoveryAttempts.delete(error.indexName);
      console.log(`Successfully recovered from connection loss for index ${error.indexName}`);
      return true;
    } catch (retryError) {
      console.warn(`Failed to recover from connection loss for index ${error.indexName}:`, retryError);
      return this.handleError(retryError as Error, error.indexName, switchFunction, error.fallbackIndex);
    }
  }

  // 从索引未找到中恢复
  private async recoverFromIndexNotFound(
    error: IndexSwitchError,
    switchFunction: (indexName: string) => Promise<void>
  ): Promise<boolean> {
    console.log(`Index ${error.indexName} not found, trying fallback`);
    
    // 索引不存在，直接尝试fallback
    return this.tryFallback(error, switchFunction);
  }

  // 从权限拒绝中恢复
  private async recoverFromPermissionDenied(
    error: IndexSwitchError,
    switchFunction: (indexName: string) => Promise<void>
  ): Promise<boolean> {
    console.log(`Permission denied for index ${error.indexName}, trying fallback`);
    
    // 权限问题通常无法自动恢复，尝试fallback
    return this.tryFallback(error, switchFunction);
  }

  // 从超时中恢复
  private async recoverFromTimeout(
    error: IndexSwitchError,
    switchFunction: (indexName: string) => Promise<void>
  ): Promise<boolean> {
    console.log(`Timeout occurred for index ${error.indexName}, retrying with longer timeout`);
    
    // 增加延迟时间后重试
    await this.delay(this.options.retryDelay * 2);
    
    try {
      this.recoveryAttempts.set(error.indexName, error.retryCount + 1);
      
      // 使用更长的超时时间重试
      await Promise.race([
        switchFunction(error.indexName),
        this.createTimeoutPromise(this.options.timeoutMs * 2)
      ]);
      
      this.recoveryAttempts.delete(error.indexName);
      console.log(`Successfully recovered from timeout for index ${error.indexName}`);
      return true;
    } catch (retryError) {
      console.warn(`Failed to recover from timeout for index ${error.indexName}:`, retryError);
      return this.handleError(retryError as Error, error.indexName, switchFunction, error.fallbackIndex);
    }
  }

  // 从网络错误中恢复
  private async recoverFromNetworkError(
    error: IndexSwitchError,
    switchFunction: (indexName: string) => Promise<void>
  ): Promise<boolean> {
    console.log(`Network error for index ${error.indexName}, retrying`);
    
    // 指数退避重试
    const delay = this.options.retryDelay * Math.pow(2, error.retryCount);
    await this.delay(delay);
    
    try {
      this.recoveryAttempts.set(error.indexName, error.retryCount + 1);
      await switchFunction(error.indexName);
      
      this.recoveryAttempts.delete(error.indexName);
      console.log(`Successfully recovered from network error for index ${error.indexName}`);
      return true;
    } catch (retryError) {
      console.warn(`Failed to recover from network error for index ${error.indexName}:`, retryError);
      return this.handleError(retryError as Error, error.indexName, switchFunction, error.fallbackIndex);
    }
  }

  // 从服务器错误中恢复
  private async recoverFromServerError(
    error: IndexSwitchError,
    switchFunction: (indexName: string) => Promise<void>
  ): Promise<boolean> {
    console.log(`Server error for index ${error.indexName}, retrying`);
    
    // 等待服务器恢复
    await this.delay(this.options.retryDelay * 3);
    
    try {
      this.recoveryAttempts.set(error.indexName, error.retryCount + 1);
      await switchFunction(error.indexName);
      
      this.recoveryAttempts.delete(error.indexName);
      console.log(`Successfully recovered from server error for index ${error.indexName}`);
      return true;
    } catch (retryError) {
      console.warn(`Failed to recover from server error for index ${error.indexName}:`, retryError);
      return this.handleError(retryError as Error, error.indexName, switchFunction, error.fallbackIndex);
    }
  }

  // 尝试fallback索引
  private async tryFallback(
    error: IndexSwitchError,
    switchFunction: (indexName: string) => Promise<void>
  ): Promise<boolean> {
    if (!this.options.enableFallback || !error.fallbackIndex) {
      console.warn(`No fallback available for index ${error.indexName}`);
      return false;
    }

    console.log(`Trying fallback index ${error.fallbackIndex} for ${error.indexName}`);
    
    try {
      await switchFunction(error.fallbackIndex);
      console.log(`Successfully switched to fallback index ${error.fallbackIndex}`);
      return true;
    } catch (fallbackError) {
      console.error(`Fallback also failed for index ${error.indexName}:`, fallbackError);
      return false;
    }
  }

  // 创建超时Promise
  private createTimeoutPromise(timeoutMs: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Operation timed out after ${timeoutMs}ms`));
      }, timeoutMs);
    });
  }

  // 延迟函数
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 获取错误统计
  getErrorStats(): ErrorStats {
    const totalErrors = this.errorHistory.length;
    const errorsByType: Record<string, number> = {};
    let totalRecoveryTime = 0;
    let successfulRecoveries = 0;

    this.errorHistory.forEach(error => {
      errorsByType[error.type] = (errorsByType[error.type] || 0) + 1;
      
      // 计算恢复时间（简化计算）
      if (error.retryCount > 0) {
        totalRecoveryTime += this.options.retryDelay * error.retryCount;
        if (error.retryCount < this.options.maxRetries) {
          successfulRecoveries++;
        }
      }
    });

    return {
      totalErrors,
      errorsByType,
      recoverySuccessRate: totalErrors > 0 ? successfulRecoveries / totalErrors : 0,
      averageRecoveryTime: successfulRecoveries > 0 ? totalRecoveryTime / successfulRecoveries : 0
    };
  }

  // 清除错误历史
  clearErrorHistory(): void {
    this.errorHistory = [];
    this.recoveryAttempts.clear();
  }

  // 获取最近的错误
  getRecentErrors(count: number = 10): IndexSwitchError[] {
    return this.errorHistory.slice(-count);
  }
}
