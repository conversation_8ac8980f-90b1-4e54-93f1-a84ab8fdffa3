{"version": 3, "file": "src_renderer_components_Monitor_ClusterOverview_tsx.renderer.js", "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;;AAEuD;;AAEvD,iBAAiB,iEAAgB;AACjC,aAAa,+CAA+C;AAC5D;;AAE+B;AAC/B;;;;;;;;;;;;;;;;ACXA;AACA;AACA;;AAEuD;;AAEvD,gBAAgB,iEAAgB;AAChC,aAAa,gEAAgE;AAC7E,aAAa,6DAA6D;AAC1E,aAAa,oDAAoD;AACjE,aAAa,gCAAgC;AAC7C;;AAE8B;AAC9B;;;;;;;;;;;;;;;;ACdA;AACA;AACA;;AAEuD;;AAEvD,YAAY,iEAAgB;AAC5B;AACA;AACA,MAAM;AACN;AACA,aAAa,wDAAwD;AACrE,aAAa,6BAA6B;AAC1C,aAAa,8BAA8B;AAC3C,aAAa,6BAA6B;AAC1C,aAAa,4BAA4B;AACzC,aAAa,8BAA8B;AAC3C,aAAa,6BAA6B;AAC1C,aAAa,4BAA4B;AACzC,aAAa,6BAA6B;AAC1C;;AAE0B;AAC1B;;;;;;;;;;;;;;;;ACvBA;AACA;AACA;;AAEuD;;AAEvD,kBAAkB,iEAAgB;AAClC,aAAa,sDAAsD;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,wDAAwD;AACrE,aAAa,0DAA0D;AACvE;;AAEgC;AAChC;;;;;;;;;;;;;;;;ACpBA;AACA;AACA;;AAEuD;;AAEvD,oBAAoB,iEAAgB;AACpC,aAAa,8BAA8B;AAC3C,aAAa,+BAA+B;AAC5C,aAAa,+BAA+B;AAC5C,aAAa,+BAA+B;AAC5C,aAAa,6BAA6B;AAC1C,aAAa,8BAA8B;AAC3C,aAAa,8BAA8B;AAC3C,aAAa,8BAA8B;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEkC;AAClC;;;;;;;;;;;;;;;;ACzBA;AACA;AACA;;AAEuD;;AAEvD,cAAc,iEAAgB;AAC9B,aAAa,8BAA8B;AAC3C;;AAE4B;AAC5B;;;;;;;;;;;;;;;;ACXA;AACA;AACA;;AAEuD;;AAEvD,eAAe,iEAAgB;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,sDAAsD;AACnE,aAAa,wDAAwD;AACrE;;AAE6B;AAC7B;;;;;;;;;;;;;;;;ACpCA;AACA;AACA;;AAEuD;;AAEvD,qBAAqB,iEAAgB;AACrC,iBAAiB,sDAAsD;AACvE,iBAAiB,4CAA4C;AAC7D;;AAEmC;AACnC;;;;;;;;;;;;;;;;ACZA;AACA;AACA;;AAEuD;;AAEvD,cAAc,iEAAgB;AAC9B,aAAa,+DAA+D;AAC5E,eAAe,wCAAwC;AACvD,aAAa,gDAAgD;AAC7D,aAAa,+CAA+C;AAC5D;;AAE4B;AAC5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACd+D;AACZ;AACW;AAC5B;AACE;AAC6E;AACjH;AACA;AACA,cAAc,qDAAI;AAClB;AACA;AACA;AACA,KAAK;AACL;AACA,cAAc,oDAAW;AACzB;AACA;AACA;AACA,KAAK;AACL;AACA,cAAc,oDAAa;AAC3B;AACA;AACA;AACA,KAAK;AACL;AACA,cAAc,oDAAa;AAC3B;AACA;AACA;AACA,KAAK;AACL;AACO,uBAAuB,WAAW;AACzC,gCAAgC,+CAAQ;AACxC,wCAAwC,+CAAQ;AAChD,4CAA4C,+CAAQ;AACpD,gCAAgC,+CAAQ;AACxC,8BAA8B,mEAAiB;AAC/C,IAAI,gDAAS;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,kBAAkB;AACnE;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,yDAAyD,kBAAkB;AAC3E;AACA;AACA,6BAA6B;AAC7B;AACA,qBAAqB;AACrB;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,qEAAqE,+BAA+B;AACpG;AACA;AACA;AACA,qEAAqE,mCAAmC;AACxG;AACA;AACA,oDAAoD,SAAS;AAC7D,sEAAsE,mBAAmB;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,UAAU;AAChC;AACA,sBAAsB,WAAW;AACjC;AACA,sBAAsB,UAAU;AAChC;AACA;AACA,YAAY,uDAAK,UAAU,WAAW,6CAAE,qCAAqC,uDAAK,UAAU,2DAA2D,uDAAK,UAAU,iDAAiD,sDAAI,SAAS,6HAA6H,6BAA6B,uDAAK,WAAW,qLAAqL,KAAK,GAAG,uDAAK,UAAU,iDAAiD,sDAAI,UAAU;AACzpB,sCAAsC,4BAA4B;AAClE,sCAAsC,mCAAmC;AACzE,sCAAsC,yBAAyB;AAC/D,yCAAyC,YAAY,MAAM,sDAAI,aAAa,0CAA0C,6CAAE;AACxH;AACA,yJAAyJ,UAAU,GAAG,sDAAI,aAAa,sOAAsO,sDAAI,CAAC,qDAAQ,IAAI,UAAU,GAAG,IAAI,IAAI,oBAAoB,uDAAK,CAAC,0CAAI,IAAI,6BAA6B,sDAAI,SAAS,gIAAgI,GAAG,sDAAI,UAAU,0DAA0D,uDAAK,UAAU,4GAA4G,uDAAK,UAAU,gCAAgC,uDAAK,UAAU,iDAAiD,sDAAI,SAAS,sFAAsF,GAAG,sDAAI,WAAW,WAAW,6CAAE,qJAAqJ,IAAI,GAAG,sDAAI,QAAQ,8FAA8F,IAAI,GAAG,sDAAI,aAAa,oEAAoE,6CAAE;AAC16C;AACA,8KAA8K,sDAAI,CAAC,oDAAI,IAAI,UAAU,IAAI,sDAAI,CAAC,oDAAO,IAAI,UAAU,GAAG,IAAI,cAAc,IAAI,IAAI,sDAAI,UAAU,iEAAiE,uDAAK,CAAC,0CAAI,IAAI,yCAAyC,sDAAI,CAAC,oDAAW,IAAI,oDAAoD,GAAG,sDAAI,SAAS,mIAAmI,GAAG,sDAAI,QAAQ;AAChnB;AACA,sDAAsD,IAAI;AAC1D;AACA;AACA,4BAA4B,sDAAI,CAAC,0CAAI,IAAI,WAAW,6CAAE,8HAA8H,uDAAK,UAAU,gDAAgD,sDAAI,kBAAkB,WAAW,6CAAE,kDAAkD,GAAG,uDAAK,UAAU,wCAAwC,uDAAK,UAAU,sDAAsD,sDAAI,SAAS,2FAA2F,GAAG,sDAAI,WAAW,WAAW,6CAAE,wGAAwG,0BAA0B,sDAAI,WAAW,2JAA2J,yBAAyB,sDAAI,WAAW,uJAAuJ,KAAK,GAAG,sDAAI,QAAQ,mFAAmF,GAAG,uDAAK,UAAU,gGAAgG,uDAAK,UAAU,iDAAiD,sDAAI,CAAC,qDAAK,IAAI,UAAU,sCAAsC,wBAAwB,uDAAK,UAAU,iDAAiD,sDAAI,CAAC,oDAAW,IAAI,UAAU,8DAA8D,KAAK,IAAI,gDAAgD,uDAAK,UAAU,iDAAiD,sDAAI,aAAa,gRAAgR,sDAAI,CAAC,oDAAW,IAAI,UAAU,GAAG,GAAG,sDAAI,aAAa,wRAAwR,sDAAI,CAAC,qDAAC,IAAI,UAAU,GAAG,IAAI,KAAK,GAAG;AAC90E,iBAAiB,IAAI,IAAI;AACzB;;;;;;;;;;;;;;;;;;;AC1H+D;AACrC;AACU;AAC7B,4BAA4B,iHAAiH;AACpJ;AACA;AACA;AACA;AACA,YAAY,uDAAK,UAAU,WAAW,6CAAE,4EAA4E,uDAAK,UAAU,yEAAyE,sDAAI,aAAa,wGAAwG,GAAG,sDAAI,aAAa,+OAA+O,IAAI,iBAAiB,sDAAI,UAAU,0EAA0E,uDAAK,WAAW,6GAA6G,GAAG,KAAK;AAC1zB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTsF;AACnC;AACiB;AACN;AAC5B;AACM;AACY;AACV;AACI;AACQ;AACV;AACU;AACU;AAC5B;AACwG;AAC5I;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,2BAA2B,YAAY;AAC9C,4BAA4B,+CAAQ;AACpC,kCAAkC,+CAAQ;AAC1C,8BAA8B,+CAAQ;AACtC,0CAA0C,+CAAQ;AAClD,sCAAsC,+CAAQ;AAC9C,sBAAsB,yEAAoB;AAC1C,8BAA8B,mEAAiB;AAC/C,IAAI,gDAAS;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,qDAAqD;AACrD;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,gBAAgB,uDAAK,UAAU,WAAW,8CAAE,gEAAgE,sDAAI,CAAC,gDAAO,IAAI,YAAY,GAAG,sDAAI,WAAW,+GAA+G,IAAI;AAC7Q;AACA;AACA,gBAAgB,sDAAI,CAAC,0CAAI,IAAI,WAAW,8CAAE,8BAA8B,uDAAK,UAAU,gFAAgF,sDAAI,CAAC,qDAAa,IAAI,UAAU,GAAG,sDAAI,WAAW,iBAAiB,IAAI,GAAG;AACjP;AACA;AACA,gBAAgB,sDAAI,CAAC,0CAAI,IAAI,WAAW,8CAAE,8BAA8B,sDAAI,UAAU,mHAAmH,GAAG;AAC5M;AACA,YAAY,0CAA0C;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,uDAAK,UAAU,WAAW,8CAAE,qCAAqC,uDAAK,UAAU,2DAA2D,uDAAK,UAAU,WAAW,sDAAI,SAAS,8GAA8G,GAAG,sDAAI,QAAQ,mGAAmG,IAAI,GAAG,uDAAK,UAAU,iDAAiD,sDAAI,CAAC,6DAAe,IAAI,0CAA0C,mBAAmB,uDAAK,UAAU,yIAAyI,KAAK,IAAI,GAAG,sDAAI,UAAU;AAC5uB,sBAAsB,oCAAoC,qDAAS,EAAE;AACrE,sBAAsB,yCAAyC,qDAAU,EAAE;AAC3E,sBAAsB,oCAAoC,qDAAa,EAAE;AACzE,sBAAsB,oCAAoC,qDAAM,EAAE;AAClE,yBAAyB,wBAAwB,MAAM,uDAAK,aAAa,6CAA6C,8CAAE;AACxH;AACA,mIAAmI,sDAAI,SAAS,UAAU,WAAW,UAAU,gCAAgC,uDAAK,CAAC,uDAAS,IAAI,WAAW,uDAAK,UAAU,8EAA8E,sDAAI,CAAC,mDAAU,IAAI,sFAAsF,oCAAoC,cAAc,sDAAI,CAAC,qDAAM,IAAI,UAAU,GAAG,GAAG,sDAAI,CAAC,mDAAU,IAAI,mFAAmF,+CAA+C,YAAY,sDAAI,CAAC,qDAAQ,IAAI,UAAU,GAAG,GAAG,sDAAI,CAAC,mDAAU,IAAI,6CAA6C,4BAA4B,GAAG,8DAA8D;AACn2B,yCAAyC,iCAAiC;AAC1E,qDAAqD,sDAAI,CAAC,qDAAQ,IAAI,UAAU,GAAG,GAAG,sDAAI,CAAC,mDAAU,IAAI,yJAAyJ,sDAAI,CAAC,qDAAS,IAAI,UAAU,GAAG,IAAI,GAAG,uDAAK,UAAU,+DAA+D,sDAAI,CAAC,mDAAU,IAAI,2CAA2C,2BAA2B,uEAAuE,sDAAI,CAAC,qDAAQ,IAAI,UAAU,aAAa,sDAAI,CAAC,+DAAgB,IAAI;AACvlB;AACA;AACA;AACA,yDAAyD,GAAG,GAAG,sDAAI,CAAC,mDAAU,IAAI,mDAAmD,yBAAyB,iBAAiB,8BAA8B,IAAI,8BAA8B,SAAS,sDAAI,CAAC,qDAAK,IAAI,UAAU,aAAa,sDAAI,CAAC,+DAAgB,IAAI;AACtT;AACA;AACA;AACA,yDAAyD,GAAG,GAAG,sDAAI,CAAC,mDAAU,IAAI,2CAA2C,0BAA0B,iBAAiB,+BAA+B,IAAI,6BAA6B,SAAS,sDAAI,CAAC,qDAAK,IAAI,UAAU,aAAa,sDAAI,CAAC,+DAAgB,IAAI;AAC/S;AACA;AACA;AACA,yDAAyD,GAAG,IAAI,GAAG,uDAAK,CAAC,0CAAI,IAAI,6BAA6B,sDAAI,SAAS,kIAAkI,GAAG,uDAAK,UAAU,8EAA8E,uDAAK,UAAU,WAAW,sDAAI,QAAQ,6FAA6F,GAAG,sDAAI,QAAQ,0HAA0H,IAAI,GAAG,uDAAK,UAAU,WAAW,sDAAI,QAAQ,mGAAmG,GAAG,sDAAI,QAAQ,kHAAkH,IAAI,GAAG,uDAAK,UAAU,WAAW,sDAAI,QAAQ,yGAAyG,GAAG,sDAAI,QAAQ,sHAAsH,IAAI,GAAG,uDAAK,UAAU,WAAW,sDAAI,QAAQ,yGAAyG,GAAG,sDAAI,QAAQ,wHAAwH,IAAI,GAAG,uDAAK,UAAU,WAAW,sDAAI,QAAQ,yGAAyG,GAAG,sDAAI,QAAQ,WAAW,8CAAE;AACpmD;AACA,4IAA4I,IAAI,GAAG,uDAAK,UAAU,WAAW,sDAAI,QAAQ,yGAAyG,GAAG,sDAAI,QAAQ,4HAA4H,IAAI,GAAG,uDAAK,UAAU,WAAW,sDAAI,QAAQ,yGAAyG,GAAG,uDAAK,QAAQ,sJAAsJ,IAAI,GAAG,uDAAK,UAAU,WAAW,sDAAI,QAAQ,+GAA+G,GAAG,uDAAK,QAAQ,6IAA6I,IAAI,IAAI,IAAI,GAAG,sDAAI,CAAC,uDAAY,IAAI,mBAAmB,IAAI,oCAAoC,sDAAI,CAAC,gEAAgB,IAAI,+BAA+B,sDAAI,CAAC,sDAAW,IAAI,+BAA+B,sDAAI,CAAC,0EAAqB,IAAI,KAAK;AAC/wC;;;;;;;;;;;;;;;;;;;ACrI+D;AACrC;AACU;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACO,2BAA2B,kDAAkD;AACpF;AACA;AACA,YAAY,uDAAK,UAAU,WAAW,6CAAE,mDAAmD,sDAAI,UAAU,WAAW,6CAAE,yFAAyF,gBAAgB,sDAAI,WAAW,WAAW,6CAAE,4EAA4E,KAAK;AAC5U;;;;;;;;;;;;;;;;;;;;ACzC+D;AACrC;AACQ;AACE;AAC7B,sBAAsB,2DAA2D;AACxF,YAAY,sDAAI,CAAC,0CAAI,IAAI,WAAW,6CAAE,8BAA8B,uDAAK,UAAU,0DAA0D,uDAAK,UAAU,gCAAgC,uDAAK,UAAU,+DAA+D,sDAAI,UAAU,qEAAqE,IAAI,sDAAI,SAAS,0FAA0F,IAAI,GAAG,sDAAI,UAAU,6BAA6B,sDAAI,WAAW,6FAA6F,GAAG,gBAAgB,sDAAI,QAAQ,iFAAiF,cAAc,uDAAK,UAAU,sDAAsD,uDAAK,WAAW,WAAW,6CAAE;AACt0B;AACA,wIAAwI,GAAG,sDAAI,WAAW,sGAAsG,IAAI,KAAK,eAAe,sDAAI,UAAU,uCAAuC,IAAI,GAAG;AACpV;;;;;;;;;;;;;;;;;;;;;ACR+D;AACrC;AACQ;AACoB;AAClB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,wBAAwB,mBAAmB;AAClD;AACA;AACA,gBAAgB,sDAAI,CAAC,0CAAI,IAAI,WAAW,6CAAE,8BAA8B,sDAAI,UAAU,mHAAmH,GAAG;AAC5M;AACA,YAAY,uDAAK,UAAU,WAAW,6CAAE,qCAAqC,uDAAK,SAAS,iKAAiK,GAAG,sDAAI,UAAU,wEAAwE,uDAAK,CAAC,0CAAI,IAAI,6BAA6B,uDAAK,UAAU,+DAA+D,uDAAK,UAAU,WAAW,sDAAI,SAAS,sFAAsF,GAAG,uDAAK,QAAQ,wGAAwG,IAAI,GAAG,sDAAI,UAAU,qEAAqE,sDAAI,WAAW,WAAW,6CAAE,oFAAoF,WAAW,IAAI,GAAG,uDAAK,UAAU,+DAA+D,uDAAK,UAAU,iDAAiD,sDAAI,CAAC,+DAAgB,IAAI;AACtkC;AACA;AACA;AACA,iEAAiE,GAAG,uDAAK,UAAU,WAAW,sDAAI,QAAQ,6GAA6G,GAAG,uDAAK,QAAQ,kJAAkJ,IAAI,IAAI,GAAG,uDAAK,UAAU,iDAAiD,sDAAI,CAAC,+DAAgB,IAAI;AAC7d;AACA;AACA;AACA,iEAAiE,GAAG,uDAAK,UAAU,WAAW,sDAAI,QAAQ,qHAAqH,GAAG,uDAAK,QAAQ,qKAAqK,IAAI,IAAI,GAAG,uDAAK,UAAU,iDAAiD,sDAAI,CAAC,+DAAgB,IAAI;AACxf;AACA;AACA;AACA,iEAAiE,GAAG,uDAAK,UAAU,WAAW,sDAAI,QAAQ,6GAA6G,GAAG,uDAAK,QAAQ,+KAA+K,IAAI,IAAI,IAAI,GAAG,uDAAK,UAAU,uFAAuF,uDAAK,UAAU,2DAA2D,sDAAI,WAAW,2HAA2H,GAAG,uDAAK,WAAW;AAClvB,8IAA8I,IAAI,GAAG,sDAAI,UAAU,wFAAwF,sDAAI,UAAU;AACzQ,sDAAsD,yGAAyG;AAC/J,2CAA2C,GAAG,IAAI,IAAI,aAAa,IAAI;AACvE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7CsF;AAC1B;AACE;AAC5B;AACE;AAC4F;AAChI;AACA;AACA;AACA;AACA;AACA,cAAc,oDAAG;AACjB,qBAAqB,2BAA2B;AAChD,KAAK;AACL;AACA;AACA;AACA;AACA,cAAc,qDAAW;AACzB,qBAAqB,2BAA2B;AAChD,KAAK;AACL;AACA;AACA;AACA;AACA,cAAc,oDAAQ;AACtB,qBAAqB,2BAA2B;AAChD,KAAK;AACL;AACA;AACA;AACA;AACA,cAAc,oDAAS;AACvB,qBAAqB,2BAA2B;AAChD,KAAK;AACL;AACA;AACA;AACA;AACA,cAAc,oDAAQ;AACtB,qBAAqB,+BAA+B;AACpD,KAAK;AACL;AACA;AACA;AACA;AACA,cAAc,oDAAa;AAC3B,qBAAqB,yBAAyB;AAC9C,KAAK;AACL;AACO,4BAA4B,0BAA0B;AAC7D,gDAAgD,+CAAQ;AACxD,sCAAsC,+CAAQ,KAAK;AACnD,kCAAkC,+CAAQ;AAC1C,gCAAgC,+CAAQ;AACxC,8BAA8B,mEAAiB;AAC/C,IAAI,gDAAS;AACb;AACA;AACA;AACA;AACA;AACA,yDAAyD;AACzD;AACA,KAAK;AACL,sBAAsB,8CAAO;AAC7B;AACA;AACA;AACA;AACA,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,EAAE,GAAG,EAAE;AAC7B,SAAS;AACT,gBAAgB,uDAAK,UAAU,yEAAyE,sDAAI,WAAW,UAAU,sDAAI,cAAc,iFAAiF,sDAAI,WAAW,kGAAkG,GAAG,GAAG,GAAG,sDAAI,WAAW,mDAAmD,wBAAwB,uDAAK,CAAC,uDAAS,IAAI,WAAW,sDAAI,WAAW,gPAAgP,GAAG,sDAAI,WAAW,kPAAkP,IAAI,IAAI,sDAAI,WAAW,gBAAgB,QAAQ,sDAAsD,GAAG,sDAAI,eAAe,uHAAuH;AAC3sC;AACA;AACA,4BAA4B,sDAAI,aAAa,6HAA6H,sDAAI,YAAY,aAAa,YAAY,IAAI,uBAAuB,EAAE,YAAY,GAAG,GAAG;AAClQ,iBAAiB,IAAI;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,uDAAK,UAAU,WAAW,6CAAE,qCAAqC,uDAAK,UAAU,2DAA2D,sDAAI,SAAS,6HAA6H,GAAG,sDAAI,UAAU;AAClT,8BAA8B,wBAAwB;AACtD,8BAA8B,wBAAwB;AACtD,8BAA8B,0BAA0B;AACxD,iCAAiC,cAAc,MAAM,sDAAI,aAAa,+CAA+C,6CAAE;AACvH;AACA,iJAAiJ,YAAY,IAAI,GAAG,sDAAI,UAAU;AAClL;AACA;AACA;AACA,4BAA4B,uDAAK,aAAa,wDAAwD,6CAAE;AACxG;AACA,+IAA+I,uDAAK,UAAU,sDAAsD,sDAAI,eAAe,mBAAmB,uBAAuB,GAAG,sDAAI,WAAW,gGAAgG,IAAI,GAAG,sDAAI,UAAU,+GAA+G,IAAI;AAC3gB,iBAAiB,GAAG,GAAG,uDAAK,CAAC,0CAAI,IAAI,6BAA6B,uDAAK,UAAU,gEAAgE,uDAAK,UAAU,iDAAiD,sDAAI,kBAAkB,mBAAmB,uBAAuB,GAAG,uDAAK,UAAU,WAAW,sDAAI,SAAS,kGAAkG,GAAG,uDAAK,UAAU,iDAAiD,uDAAK,WAAW,WAAW,6CAAE;AAC7f;AACA,6JAA6J,oBAAoB,uDAAK,UAAU,wFAAwF,sDAAI,CAAC,qDAAU,IAAI,qCAAqC,4CAA4C,sDAAI,CAAC,qDAAY,IAAI,uCAAuC,MAAM,sDAAI,CAAC,qDAAK,IAAI,yCAAyC,IAAI,uDAAK,WAAW,WAAW,6CAAE;AACthB;AACA,kNAAkN,IAAI,KAAK,IAAI,IAAI,GAAG,sDAAI,UAAU,WAAW,6CAAE;AACjQ;AACA;AACA,6EAA6E,IAAI,GAAG,sDAAI,UAAU,wFAAwF,sDAAI,UAAU,qGAAqG,uDAAK,UAAU,qCAAqC,sDAAI,CAAC,oDAAQ,IAAI,gDAAgD,GAAG,sDAAI,QAAQ,sCAAsC,GAAG,sDAAI,QAAQ,uFAAuF,IAAI,GAAG,IAAI,oBAAoB,uDAAK,UAAU,+EAA+E,sDAAI,SAAS,4GAA4G,GAAG,uDAAK,UAAU,uEAAuE,uDAAK,UAAU,WAAW,sDAAI,WAAW,4FAA4F,GAAG,uDAAK,UAAU,6FAA6F,sDAAI,CAAC,qDAAU,IAAI,qCAAqC,4CAA4C,sDAAI,CAAC,qDAAY,IAAI,uCAAuC,MAAM,sDAAI,CAAC,qDAAK,IAAI,yCAAyC,IAAI,sDAAI,WAAW;AACh3C,+GAA+G,IAAI,IAAI,GAAG,uDAAK,UAAU,WAAW,sDAAI,WAAW,4GAA4G,GAAG,uDAAK,UAAU,mIAAmI,IAAI,GAAG,uDAAK,UAAU,WAAW,sDAAI,WAAW,sFAAsF,GAAG,uDAAK,UAAU,sDAAsD,sDAAI,UAAU,mFAAmF,sDAAI,UAAU,gFAAgF,UAAU,8BAA8B,MAAM,GAAG,GAAG,uDAAK,WAAW,8HAA8H,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI;AAC5gC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClI+D;AACZ;AACW;AAC5B;AACE;AACgE;AAC7F,iCAAiC,YAAY;AACpD,4CAA4C,+CAAQ;AACpD;AACA;AACA;AACA,KAAK;AACL,wCAAwC,+CAAQ;AAChD,8BAA8B,mEAAiB;AAC/C,IAAI,gDAAS;AACb;AACA;AACA;AACA;AACA;AACA,2DAA2D;AAC3D;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,0BAA0B,oDAAW;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,oDAAa;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,oDAAa;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,qDAAG;AACzB;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,sBAAsB,oDAAS;AAC/B;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,kBAAkB,oDAAQ;AAC1B;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,YAAY,uDAAK,UAAU,WAAW,6CAAE,qCAAqC,sDAAI,CAAC,0CAAI,IAAI,WAAW,6CAAE,kEAAkE,uDAAK,UAAU,gDAAgD,sDAAI,kBAAkB,WAAW,6CAAE,gDAAgD,GAAG,uDAAK,UAAU,gCAAgC,uDAAK,UAAU,gEAAgE,sDAAI,SAAS,mGAAmG,GAAG,uDAAK,UAAU,gGAAgG,sDAAI,CAAC,oDAAK,IAAI,UAAU,mEAAmE,IAAI,GAAG,sDAAI,QAAQ,wFAAwF,GAAG,uDAAK,UAAU,+DAA+D,uDAAK,UAAU,iFAAiF,sDAAI,UAAU,8GAA8G,GAAG,sDAAI,UAAU,mGAAmG,IAAI,GAAG,uDAAK,UAAU,iFAAiF,sDAAI,UAAU,uHAAuH,GAAG,sDAAI,UAAU,mGAAmG,IAAI,GAAG,uDAAK,UAAU,iFAAiF,sDAAI,UAAU,WAAW,6CAAE;AACvuD,4GAA4G,GAAG,sDAAI,UAAU,6FAA6F,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,uDAAK,UAAU,+DAA+D,uDAAK,CAAC,0CAAI,IAAI,6BAA6B,uDAAK,UAAU,sDAAsD,sDAAI,CAAC,oDAAa,IAAI,qCAAqC,GAAG,sDAAI,SAAS,iHAAiH,sCAAsC,sDAAI,WAAW,yJAAyJ,KAAK,uCAAuC,uDAAK,UAAU,0CAA0C,sDAAI,CAAC,oDAAW,IAAI,oDAAoD,GAAG,sDAAI,QAAQ,iJAAiJ,IAAI,MAAM,sDAAI,UAAU,6EAA6E,uDAAK,UAAU,4FAA4F,sDAAI,CAAC,oDAAa,IAAI,0DAA0D,GAAG,sDAAI,WAAW,sEAAsE,IAAI,YAAY,KAAK,GAAG,uDAAK,CAAC,0CAAI,IAAI,6BAA6B,uDAAK,UAAU,sDAAsD,sDAAI,CAAC,oDAAS,IAAI,sCAAsC,GAAG,sDAAI,SAAS,iHAAiH,+CAA+C,sDAAI,WAAW,sKAAsK,KAAK,gDAAgD,uDAAK,UAAU,0CAA0C,sDAAI,CAAC,qDAAM,IAAI,oDAAoD,GAAG,sDAAI,QAAQ,iJAAiJ,IAAI,MAAM,sDAAI,UAAU,+FAA+F,uDAAK,UAAU,8FAA8F,sDAAI,CAAC,oDAAS,IAAI,2DAA2D,GAAG,sDAAI,WAAW,+EAA+E,IAAI,YAAY,KAAK,IAAI,GAAG,uDAAK,CAAC,0CAAI,IAAI,6BAA6B,sDAAI,SAAS,sHAAsH,GAAG,sDAAI,UAAU;AACh+F;AACA,oCAAoC,uDAAK,aAAa,WAAW,6CAAE,+HAA+H,uDAAK,UAAU,sDAAsD,sDAAI,eAAe,WAAW,6CAAE,2CAA2C,GAAG,sDAAI,SAAS,+GAA+G,IAAI,GAAG,sDAAI,QAAQ,2FAA2F,IAAI;AACnkB,yBAAyB,GAAG,IAAI,GAAG,uDAAK,CAAC,0CAAI,IAAI,6BAA6B,sDAAI,SAAS,kIAAkI,GAAG,sDAAI,UAAU;AAC9O;AACA;AACA;AACA;AACA,sCAAsC,oDAAW;AACjD,6BAA6B;AAC7B;AACA;AACA;AACA;AACA,4EAA4E,oDAAa,GAAG,oDAAW;AACvG,6BAA6B;AAC7B;AACA;AACA;AACA;AACA,2EAA2E,oDAAa,GAAG,oDAAW;AACtG,6BAA6B;AAC7B;AACA;AACA;AACA;AACA,sCAAsC,oDAAQ;AAC9C,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA,oCAAoC,uDAAK,UAAU,sFAAsF,sDAAI,kBAAkB,WAAW,6CAAE,yCAAyC,GAAG,sDAAI,UAAU,oGAAoG,GAAG,sDAAI,UAAU,WAAW,6CAAE,iEAAiE,IAAI;AAC7a,yBAAyB,GAAG,IAAI,IAAI;AACpC", "sources": ["webpack://es-client/./node_modules/lucide-react/dist/esm/icons/activity.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/bell-off.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/cpu.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/hard-drive.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/memory-stick.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/minus.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/server.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/trending-down.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/users.mjs", "webpack://es-client/./src/renderer/components/Monitor/AlertSystem.tsx", "webpack://es-client/./src/renderer/components/Monitor/CircularProgress.tsx", "webpack://es-client/./src/renderer/components/Monitor/ClusterOverview.tsx", "webpack://es-client/./src/renderer/components/Monitor/HealthIndicator.tsx", "webpack://es-client/./src/renderer/components/Monitor/MetricCard.tsx", "webpack://es-client/./src/renderer/components/Monitor/NodesMonitor.tsx", "webpack://es-client/./src/renderer/components/Monitor/PerformanceChart.tsx", "webpack://es-client/./src/renderer/components/Monitor/SystemHealthDashboard.tsx"], "sourcesContent": ["/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst Activity = createLucideIcon(\"Activity\", [\n  [\"path\", { d: \"M22 12h-4l-3 9L9 3l-3 9H2\", key: \"d5dnw9\" }]\n]);\n\nexport { Activity as default };\n//# sourceMappingURL=activity.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst BellOff = createLucideIcon(\"BellOff\", [\n  [\"path\", { d: \"M8.7 3A6 6 0 0 1 18 8a21.3 21.3 0 0 0 .6 5\", key: \"o7mx20\" }],\n  [\"path\", { d: \"M17 17H3s3-2 3-9a4.67 4.67 0 0 1 .3-1.7\", key: \"16f1lm\" }],\n  [\"path\", { d: \"M10.3 21a1.94 1.94 0 0 0 3.4 0\", key: \"qgo35s\" }],\n  [\"path\", { d: \"m2 2 20 20\", key: \"1ooewy\" }]\n]);\n\nexport { BellOff as default };\n//# sourceMappingURL=bell-off.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst Cpu = createLucideIcon(\"Cpu\", [\n  [\n    \"rect\",\n    { x: \"4\", y: \"4\", width: \"16\", height: \"16\", rx: \"2\", key: \"1vbyd7\" }\n  ],\n  [\"rect\", { x: \"9\", y: \"9\", width: \"6\", height: \"6\", key: \"o3kz5p\" }],\n  [\"path\", { d: \"M15 2v2\", key: \"13l42r\" }],\n  [\"path\", { d: \"M15 20v2\", key: \"15mkzm\" }],\n  [\"path\", { d: \"M2 15h2\", key: \"1gxd5l\" }],\n  [\"path\", { d: \"M2 9h2\", key: \"1bbxkp\" }],\n  [\"path\", { d: \"M20 15h2\", key: \"19e6y8\" }],\n  [\"path\", { d: \"M20 9h2\", key: \"19tzq7\" }],\n  [\"path\", { d: \"M9 2v2\", key: \"165o2o\" }],\n  [\"path\", { d: \"M9 20v2\", key: \"i2bqo8\" }]\n]);\n\nexport { Cpu as default };\n//# sourceMappingURL=cpu.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst HardDrive = createLucideIcon(\"HardDrive\", [\n  [\"line\", { x1: \"22\", x2: \"2\", y1: \"12\", y2: \"12\", key: \"1y58io\" }],\n  [\n    \"path\",\n    {\n      d: \"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z\",\n      key: \"oot6mr\"\n    }\n  ],\n  [\"line\", { x1: \"6\", x2: \"6.01\", y1: \"16\", y2: \"16\", key: \"sgf278\" }],\n  [\"line\", { x1: \"10\", x2: \"10.01\", y1: \"16\", y2: \"16\", key: \"1l4acy\" }]\n]);\n\nexport { HardDrive as default };\n//# sourceMappingURL=hard-drive.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst MemoryStick = createLucideIcon(\"MemoryStick\", [\n  [\"path\", { d: \"M6 19v-3\", key: \"1nvgqn\" }],\n  [\"path\", { d: \"M10 19v-3\", key: \"iu8nkm\" }],\n  [\"path\", { d: \"M14 19v-3\", key: \"kcehxu\" }],\n  [\"path\", { d: \"M18 19v-3\", key: \"1vh91z\" }],\n  [\"path\", { d: \"M8 11V9\", key: \"63erz4\" }],\n  [\"path\", { d: \"M16 11V9\", key: \"fru6f3\" }],\n  [\"path\", { d: \"M12 11V9\", key: \"ha00sb\" }],\n  [\"path\", { d: \"M2 15h20\", key: \"16ne18\" }],\n  [\n    \"path\",\n    {\n      d: \"M2 7a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v1.1a2 2 0 0 0 0 3.837V17a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-5.1a2 2 0 0 0 0-3.837Z\",\n      key: \"lhddv3\"\n    }\n  ]\n]);\n\nexport { MemoryStick as default };\n//# sourceMappingURL=memory-stick.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst Minus = createLucideIcon(\"Minus\", [\n  [\"path\", { d: \"M5 12h14\", key: \"1ays0h\" }]\n]);\n\nexport { Minus as default };\n//# sourceMappingURL=minus.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst Server = createLucideIcon(\"Server\", [\n  [\n    \"rect\",\n    {\n      width: \"20\",\n      height: \"8\",\n      x: \"2\",\n      y: \"2\",\n      rx: \"2\",\n      ry: \"2\",\n      key: \"ngkwjq\"\n    }\n  ],\n  [\n    \"rect\",\n    {\n      width: \"20\",\n      height: \"8\",\n      x: \"2\",\n      y: \"14\",\n      rx: \"2\",\n      ry: \"2\",\n      key: \"iecqi9\"\n    }\n  ],\n  [\"line\", { x1: \"6\", x2: \"6.01\", y1: \"6\", y2: \"6\", key: \"16zg32\" }],\n  [\"line\", { x1: \"6\", x2: \"6.01\", y1: \"18\", y2: \"18\", key: \"nzw8ys\" }]\n]);\n\nexport { Server as default };\n//# sourceMappingURL=server.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst TrendingDown = createLucideIcon(\"TrendingDown\", [\n  [\"polyline\", { points: \"22 17 13.5 8.5 8.5 13.5 2 7\", key: \"1r2t7k\" }],\n  [\"polyline\", { points: \"16 17 22 17 22 11\", key: \"11uiuu\" }]\n]);\n\nexport { TrendingDown as default };\n//# sourceMappingURL=trending-down.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst Users = createLucideIcon(\"Users\", [\n  [\"path\", { d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\", key: \"1yyitq\" }],\n  [\"circle\", { cx: \"9\", cy: \"7\", r: \"4\", key: \"nufk8\" }],\n  [\"path\", { d: \"M22 21v-2a4 4 0 0 0-3-3.87\", key: \"kshegd\" }],\n  [\"path\", { d: \"M16 3.13a4 4 0 0 1 0 7.75\", key: \"1da9ce\" }]\n]);\n\nexport { Users as default };\n//# sourceMappingURL=users.mjs.map\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { useState, useEffect } from 'react';\nimport { MonitoringService } from '../../services/monitoring';\nimport { Card } from '../UI/Card';\nimport { cn } from '../../utils/cn';\nimport { AlertTriangle, AlertCircle, Info, CheckCircle, X, Settings, Bell, BellOff, Clock, } from 'lucide-react';\nconst severityConfig = {\n    low: {\n        icon: Info,\n        color: 'text-blue-600 dark:text-blue-400',\n        bgColor: 'bg-blue-50 dark:bg-blue-900/20',\n        borderColor: 'border-blue-200 dark:border-blue-800',\n    },\n    medium: {\n        icon: AlertCircle,\n        color: 'text-yellow-600 dark:text-yellow-400',\n        bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',\n        borderColor: 'border-yellow-200 dark:border-yellow-800',\n    },\n    high: {\n        icon: AlertTriangle,\n        color: 'text-orange-600 dark:text-orange-400',\n        bgColor: 'bg-orange-50 dark:bg-orange-900/20',\n        borderColor: 'border-orange-200 dark:border-orange-800',\n    },\n    critical: {\n        icon: AlertTriangle,\n        color: 'text-red-600 dark:text-red-400',\n        bgColor: 'bg-red-50 dark:bg-red-900/20',\n        borderColor: 'border-red-200 dark:border-red-800',\n    },\n};\nexport const AlertSystem = ({ className }) => {\n    const [alerts, setAlerts] = useState([]);\n    const [alertRules, setAlertRules] = useState([]);\n    const [showSettings, setShowSettings] = useState(false);\n    const [filter, setFilter] = useState('active');\n    const monitoringService = MonitoringService.getInstance();\n    useEffect(() => {\n        // Load initial data\n        setAlerts(monitoringService.getAllAlerts());\n        setAlertRules(monitoringService.getAlertRules());\n        // Subscribe to new alerts\n        const unsubscribe = monitoringService.addAlertCallback((newAlert) => {\n            setAlerts(prev => [newAlert, ...prev]);\n            // Show browser notification for critical alerts\n            if (newAlert.severity === 'critical' && 'Notification' in window) {\n                if (Notification.permission === 'granted') {\n                    new Notification(`ES 监控警告: ${newAlert.ruleName}`, {\n                        body: newAlert.message,\n                        icon: '/favicon.ico',\n                    });\n                }\n                else if (Notification.permission !== 'denied') {\n                    Notification.requestPermission().then(permission => {\n                        if (permission === 'granted') {\n                            new Notification(`ES 监控警告: ${newAlert.ruleName}`, {\n                                body: newAlert.message,\n                                icon: '/favicon.ico',\n                            });\n                        }\n                    });\n                }\n            }\n        });\n        return unsubscribe;\n    }, []);\n    const filteredAlerts = alerts.filter(alert => {\n        switch (filter) {\n            case 'active':\n                return !alert.acknowledged && !alert.resolvedAt;\n            case 'acknowledged':\n                return alert.acknowledged || alert.resolvedAt;\n            default:\n                return true;\n        }\n    });\n    const activeAlertsCount = alerts.filter(a => !a.acknowledged && !a.resolvedAt).length;\n    const handleAcknowledge = (alertId) => {\n        monitoringService.acknowledgeAlert(alertId);\n        setAlerts(prev => prev.map(alert => alert.id === alertId ? { ...alert, acknowledged: true } : alert));\n    };\n    const handleResolve = (alertId) => {\n        monitoringService.resolveAlert(alertId);\n        setAlerts(prev => prev.map(alert => alert.id === alertId ? { ...alert, resolvedAt: Date.now() } : alert));\n    };\n    const handleToggleRule = (ruleId, enabled) => {\n        monitoringService.updateAlertRule(ruleId, { enabled });\n        setAlertRules(prev => prev.map(rule => rule.id === ruleId ? { ...rule, enabled } : rule));\n    };\n    const formatTimestamp = (timestamp) => {\n        const date = new Date(timestamp);\n        const now = new Date();\n        const diffMs = now.getTime() - timestamp;\n        const diffMins = Math.floor(diffMs / (1000 * 60));\n        const diffHours = Math.floor(diffMins / 60);\n        const diffDays = Math.floor(diffHours / 24);\n        if (diffMins < 1)\n            return '刚刚';\n        if (diffMins < 60)\n            return `${diffMins} 分钟前`;\n        if (diffHours < 24)\n            return `${diffHours} 小时前`;\n        if (diffDays < 7)\n            return `${diffDays} 天前`;\n        return date.toLocaleDateString();\n    };\n    return (_jsxs(\"div\", { className: cn('space-y-4', className), children: [_jsxs(\"div\", { className: \"flex items-center justify-between\", children: [_jsxs(\"div\", { className: \"flex items-center gap-3\", children: [_jsx(\"h3\", { className: \"text-lg font-semibold text-neutral-900 dark:text-neutral-100\", children: \"\\u667A\\u80FD\\u8B66\\u544A\\u7CFB\\u7EDF\" }), activeAlertsCount > 0 && (_jsxs(\"span\", { className: \"px-2 py-1 text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 rounded-full\", children: [activeAlertsCount, \" \\u4E2A\\u6D3B\\u8DC3\\u8B66\\u544A\"] }))] }), _jsxs(\"div\", { className: \"flex items-center gap-2\", children: [_jsx(\"div\", { className: \"flex bg-neutral-100 dark:bg-neutral-800 rounded-lg p-1\", children: [\n                                    { key: 'active', label: '活跃' },\n                                    { key: 'acknowledged', label: '已处理' },\n                                    { key: 'all', label: '全部' },\n                                ].map(({ key, label }) => (_jsx(\"button\", { onClick: () => setFilter(key), className: cn('px-3 py-1 text-sm font-medium rounded-md transition-colors', filter === key\n                                        ? 'bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 shadow-sm'\n                                        : 'text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100'), children: label }, key))) }), _jsx(\"button\", { onClick: () => setShowSettings(!showSettings), className: \"p-2 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 transition-colors\", title: \"\\u8B66\\u544A\\u89C4\\u5219\\u8BBE\\u7F6E\", children: _jsx(Settings, { size: 18 }) })] })] }), showSettings && (_jsxs(Card, { className: \"p-4\", children: [_jsx(\"h4\", { className: \"text-md font-medium text-neutral-900 dark:text-neutral-100 mb-3\", children: \"\\u8B66\\u544A\\u89C4\\u5219\\u914D\\u7F6E\" }), _jsx(\"div\", { className: \"space-y-3\", children: alertRules.map(rule => (_jsxs(\"div\", { className: \"flex items-center justify-between p-3 bg-neutral-50 dark:bg-neutral-800 rounded-lg\", children: [_jsxs(\"div\", { className: \"flex-1\", children: [_jsxs(\"div\", { className: \"flex items-center gap-2\", children: [_jsx(\"h5\", { className: \"font-medium text-neutral-900 dark:text-neutral-100\", children: rule.name }), _jsx(\"span\", { className: cn('px-2 py-1 text-xs font-medium rounded-full', severityConfig[rule.severity].bgColor, severityConfig[rule.severity].color), children: rule.severity })] }), _jsx(\"p\", { className: \"text-sm text-neutral-600 dark:text-neutral-400 mt-1\", children: rule.description })] }), _jsx(\"button\", { onClick: () => handleToggleRule(rule.id, !rule.enabled), className: cn('p-2 rounded-lg transition-colors', rule.enabled\n                                        ? 'text-green-600 hover:bg-green-50 dark:text-green-400 dark:hover:bg-green-900/20'\n                                        : 'text-neutral-400 hover:bg-neutral-100 dark:hover:bg-neutral-700'), title: rule.enabled ? '禁用规则' : '启用规则', children: rule.enabled ? _jsx(Bell, { size: 18 }) : _jsx(BellOff, { size: 18 }) })] }, rule.id))) })] })), _jsx(\"div\", { className: \"space-y-3\", children: filteredAlerts.length === 0 ? (_jsxs(Card, { className: \"p-6 text-center\", children: [_jsx(CheckCircle, { className: \"mx-auto mb-3 text-green-500\", size: 48 }), _jsx(\"h4\", { className: \"text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-2\", children: filter === 'active' ? '没有活跃警告' : '没有警告记录' }), _jsx(\"p\", { className: \"text-neutral-600 dark:text-neutral-400\", children: filter === 'active'\n                                ? '集群运行状态良好，所有指标都在正常范围内。'\n                                : '当前筛选条件下没有找到警告记录。' })] })) : (filteredAlerts.map(alert => {\n                    const config = severityConfig[alert.severity];\n                    const IconComponent = config.icon;\n                    return (_jsx(Card, { className: cn('p-4 border-l-4', config.bgColor, config.borderColor, alert.acknowledged || alert.resolvedAt ? 'opacity-60' : ''), children: _jsxs(\"div\", { className: \"flex items-start gap-3\", children: [_jsx(IconComponent, { className: cn('mt-0.5 flex-shrink-0', config.color), size: 20 }), _jsxs(\"div\", { className: \"flex-1 min-w-0\", children: [_jsxs(\"div\", { className: \"flex items-center gap-2 mb-1\", children: [_jsx(\"h4\", { className: \"font-medium text-neutral-900 dark:text-neutral-100\", children: alert.ruleName }), _jsx(\"span\", { className: cn('px-2 py-1 text-xs font-medium rounded-full', config.bgColor, config.color), children: alert.severity }), alert.acknowledged && (_jsx(\"span\", { className: \"px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded-full\", children: \"\\u5DF2\\u786E\\u8BA4\" })), alert.resolvedAt && (_jsx(\"span\", { className: \"px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full\", children: \"\\u5DF2\\u89E3\\u51B3\" }))] }), _jsx(\"p\", { className: \"text-neutral-700 dark:text-neutral-300 mb-2\", children: alert.message }), _jsxs(\"div\", { className: \"flex items-center gap-4 text-sm text-neutral-500 dark:text-neutral-400\", children: [_jsxs(\"div\", { className: \"flex items-center gap-1\", children: [_jsx(Clock, { size: 14 }), formatTimestamp(alert.timestamp)] }), alert.resolvedAt && (_jsxs(\"div\", { className: \"flex items-center gap-1\", children: [_jsx(CheckCircle, { size: 14 }), \"\\u89E3\\u51B3\\u4E8E \", formatTimestamp(alert.resolvedAt)] }))] })] }), !alert.acknowledged && !alert.resolvedAt && (_jsxs(\"div\", { className: \"flex items-center gap-1\", children: [_jsx(\"button\", { onClick: () => handleAcknowledge(alert.id), className: \"p-1.5 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded transition-colors\", title: \"\\u786E\\u8BA4\\u8B66\\u544A\", children: _jsx(CheckCircle, { size: 16 }) }), _jsx(\"button\", { onClick: () => handleResolve(alert.id), className: \"p-1.5 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded transition-colors\", title: \"\\u6807\\u8BB0\\u4E3A\\u5DF2\\u89E3\\u51B3\", children: _jsx(X, { size: 16 }) })] }))] }) }, alert.id));\n                })) })] }));\n};\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React from 'react';\nimport { cn } from '../../utils/cn';\nexport const CircularProgress = ({ value, size = 60, strokeWidth = 4, color = '#3b82f6', backgroundColor = '#e5e7eb', showValue = true, className, }) => {\n    const radius = (size - strokeWidth) / 2;\n    const circumference = radius * 2 * Math.PI;\n    const strokeDasharray = circumference;\n    const strokeDashoffset = circumference - (value / 100) * circumference;\n    return (_jsxs(\"div\", { className: cn('relative inline-flex items-center justify-center', className), children: [_jsxs(\"svg\", { width: size, height: size, className: \"transform -rotate-90\", children: [_jsx(\"circle\", { cx: size / 2, cy: size / 2, r: radius, stroke: backgroundColor, strokeWidth: strokeWidth, fill: \"none\" }), _jsx(\"circle\", { cx: size / 2, cy: size / 2, r: radius, stroke: color, strokeWidth: strokeWidth, fill: \"none\", strokeDasharray: strokeDasharray, strokeDashoffset: strokeDashoffset, strokeLinecap: \"round\", className: \"transition-all duration-300 ease-out\" })] }), showValue && (_jsx(\"div\", { className: \"absolute inset-0 flex items-center justify-center\", children: _jsxs(\"span\", { className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\", children: [Math.round(value), \"%\"] }) }))] }));\n};\n", "import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from \"react/jsx-runtime\";\nimport React, { useEffect, useState } from 'react';\nimport { ElasticsearchService } from '../../services/elasticsearch';\nimport { MonitoringService } from '../../services/monitoring';\nimport { Card } from '../UI/Card';\nimport { Spinner } from '../UI/Spinner';\nimport { HealthIndicator } from './HealthIndicator';\nimport { MetricCard } from './MetricCard';\nimport { NodesMonitor } from './NodesMonitor';\nimport { CircularProgress } from './CircularProgress';\nimport { AlertSystem } from './AlertSystem';\nimport { PerformanceChart } from './PerformanceChart';\nimport { SystemHealthDashboard } from './SystemHealthDashboard';\nimport { cn } from '../../utils/cn';\nimport { Server, Database, HardDrive, Activity, Users, FileText, Clock, AlertTriangle, TrendingUp, BarChart3, Shield, } from 'lucide-react';\nconst formatBytes = (bytes) => {\n    if (bytes === 0)\n        return '0 B';\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\nconst formatNumber = (num) => {\n    if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + 'M';\n    }\n    if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n};\nexport const ClusterOverview = ({ className, }) => {\n    const [data, setData] = useState(null);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [lastUpdated, setLastUpdated] = useState(null);\n    const [activeTab, setActiveTab] = useState('overview');\n    const esService = ElasticsearchService.getInstance();\n    const monitoringService = MonitoringService.getInstance();\n    useEffect(() => {\n        const loadInitialData = async () => {\n            if (!esService.isConnected()) {\n                setError('未连接到 Elasticsearch 集群');\n                setLoading(false);\n                return;\n            }\n            try {\n                setLoading(true);\n                setError(null);\n                const [clusterHealth, nodesStats, clusterStats] = await Promise.all([\n                    esService.getClusterHealth(),\n                    esService.getNodesStats(),\n                    esService.getClusterStats(),\n                ]);\n                const monitoringData = {\n                    timestamp: Date.now(),\n                    clusterHealth,\n                    nodesStats,\n                    clusterStats,\n                };\n                setData(monitoringData);\n                setLastUpdated(new Date());\n                // Add data to monitoring service for analysis\n                monitoringService.addMonitoringData(monitoringData);\n            }\n            catch (err) {\n                console.error('Failed to load cluster data:', err);\n                setError(err instanceof Error ? err.message : '加载集群数据失败');\n            }\n            finally {\n                setLoading(false);\n            }\n        };\n        loadInitialData();\n        // Set up real-time monitoring\n        const unsubscribe = esService.addMonitoringCallback((monitoringData) => {\n            setData(monitoringData);\n            setLastUpdated(new Date());\n            setError(null);\n            // Add data to monitoring service for analysis\n            monitoringService.addMonitoringData(monitoringData);\n        });\n        // Start monitoring if not already started\n        if (!esService.isMonitoring()) {\n            esService.startRealTimeMonitoring(5000); // Update every 5 seconds\n        }\n        return () => {\n            unsubscribe();\n        };\n    }, []);\n    if (loading) {\n        return (_jsxs(\"div\", { className: cn('flex items-center justify-center p-8', className), children: [_jsx(Spinner, { size: \"lg\" }), _jsx(\"span\", { className: \"ml-3 text-neutral-600 dark:text-neutral-300\", children: \"\\u52A0\\u8F7D\\u96C6\\u7FA4\\u6570\\u636E...\" })] }));\n    }\n    if (error) {\n        return (_jsx(Card, { className: cn('p-6', className), children: _jsxs(\"div\", { className: \"flex items-center gap-3 text-red-600 dark:text-red-400\", children: [_jsx(AlertTriangle, { size: 20 }), _jsx(\"span\", { children: error })] }) }));\n    }\n    if (!data) {\n        return (_jsx(Card, { className: cn('p-6', className), children: _jsx(\"div\", { className: \"text-center text-neutral-500 dark:text-neutral-400\", children: \"\\u6682\\u65E0\\u96C6\\u7FA4\\u6570\\u636E\" }) }));\n    }\n    const { clusterHealth, nodesStats, clusterStats } = data;\n    // Calculate aggregate metrics\n    const totalCpuPercent = Object.values(nodesStats).reduce((sum, node) => sum + node.os.cpu.percent, 0) / Object.keys(nodesStats).length;\n    const totalMemoryUsed = Object.values(nodesStats).reduce((sum, node) => sum + node.os.mem.used_in_bytes, 0);\n    const totalMemoryTotal = Object.values(nodesStats).reduce((sum, node) => sum + node.os.mem.total_in_bytes, 0);\n    const memoryPercent = (totalMemoryUsed / totalMemoryTotal) * 100;\n    const totalJvmHeapUsed = Object.values(nodesStats).reduce((sum, node) => sum + node.jvm.mem.heap_used_in_bytes, 0);\n    const totalJvmHeapMax = Object.values(nodesStats).reduce((sum, node) => sum + node.jvm.mem.heap_max_in_bytes, 0);\n    const jvmHeapPercent = (totalJvmHeapUsed / totalJvmHeapMax) * 100;\n    return (_jsxs(\"div\", { className: cn('space-y-6', className), children: [_jsxs(\"div\", { className: \"flex items-center justify-between\", children: [_jsxs(\"div\", { children: [_jsx(\"h2\", { className: \"text-2xl font-bold text-neutral-900 dark:text-neutral-100\", children: \"\\u96C6\\u7FA4\\u76D1\\u63A7\" }), _jsx(\"p\", { className: \"text-sm text-neutral-500 dark:text-neutral-400\", children: clusterHealth.cluster_name })] }), _jsxs(\"div\", { className: \"flex items-center gap-4\", children: [_jsx(HealthIndicator, { health: clusterHealth.status, size: \"lg\" }), lastUpdated && (_jsxs(\"div\", { className: \"text-sm text-neutral-500 dark:text-neutral-400\", children: [\"\\u6700\\u540E\\u66F4\\u65B0: \", lastUpdated.toLocaleTimeString()] }))] })] }), _jsx(\"div\", { className: \"flex bg-neutral-100 dark:bg-neutral-800 rounded-lg p-1\", children: [\n                    { key: 'overview', label: '概览', icon: BarChart3 },\n                    { key: 'performance', label: '性能分析', icon: TrendingUp },\n                    { key: 'alerts', label: '智能警告', icon: AlertTriangle },\n                    { key: 'health', label: '系统健康', icon: Shield },\n                ].map(({ key, label, icon: Icon }) => (_jsxs(\"button\", { onClick: () => setActiveTab(key), className: cn('flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors', activeTab === key\n                        ? 'bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 shadow-sm'\n                        : 'text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100'), children: [_jsx(Icon, { size: 16 }), label] }, key))) }), activeTab === 'overview' && (_jsxs(_Fragment, { children: [_jsxs(\"div\", { className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\", children: [_jsx(MetricCard, { title: \"\\u8282\\u70B9\\u6570\\u91CF\", value: clusterHealth.number_of_nodes, subtitle: `${clusterHealth.number_of_data_nodes} 个数据节点`, icon: _jsx(Server, { size: 20 }) }), _jsx(MetricCard, { title: \"\\u7D22\\u5F15\\u6570\\u91CF\", value: clusterStats.indices.count, subtitle: `${formatNumber(clusterStats.indices.docs.count)} 个文档`, icon: _jsx(Database, { size: 20 }) }), _jsx(MetricCard, { title: \"\\u5206\\u7247\\u72B6\\u6001\", value: `${clusterHealth.active_shards}/${clusterHealth.active_shards + clusterHealth.unassigned_shards}`, subtitle: clusterHealth.unassigned_shards > 0\n                                    ? `${clusterHealth.unassigned_shards} 个未分配`\n                                    : '全部已分配', icon: _jsx(FileText, { size: 20 }) }), _jsx(MetricCard, { title: \"\\u5B58\\u50A8\\u5927\\u5C0F\", value: formatBytes(clusterStats.indices.store.size_in_bytes), subtitle: \"\\u603B\\u5B58\\u50A8\\u4F7F\\u7528\\u91CF\", icon: _jsx(HardDrive, { size: 20 }) })] }), _jsxs(\"div\", { className: \"grid grid-cols-1 md:grid-cols-3 gap-4\", children: [_jsx(MetricCard, { title: \"CPU \\u4F7F\\u7528\\u7387\", value: `${totalCpuPercent.toFixed(1)}%`, subtitle: \"\\u96C6\\u7FA4\\u5E73\\u5747 CPU \\u4F7F\\u7528\\u7387\", icon: _jsx(Activity, { size: 20 }), children: _jsx(CircularProgress, { value: totalCpuPercent, size: 60, color: totalCpuPercent > 80\n                                        ? '#ef4444'\n                                        : totalCpuPercent > 60\n                                            ? '#f59e0b'\n                                            : '#10b981' }) }), _jsx(MetricCard, { title: \"\\u5185\\u5B58\\u4F7F\\u7528\\u7387\", value: `${memoryPercent.toFixed(1)}%`, subtitle: `${formatBytes(totalMemoryUsed)} / ${formatBytes(totalMemoryTotal)}`, icon: _jsx(Users, { size: 20 }), children: _jsx(CircularProgress, { value: memoryPercent, size: 60, color: memoryPercent > 80\n                                        ? '#ef4444'\n                                        : memoryPercent > 60\n                                            ? '#f59e0b'\n                                            : '#10b981' }) }), _jsx(MetricCard, { title: \"JVM \\u5806\\u5185\\u5B58\", value: `${jvmHeapPercent.toFixed(1)}%`, subtitle: `${formatBytes(totalJvmHeapUsed)} / ${formatBytes(totalJvmHeapMax)}`, icon: _jsx(Clock, { size: 20 }), children: _jsx(CircularProgress, { value: jvmHeapPercent, size: 60, color: jvmHeapPercent > 80\n                                        ? '#ef4444'\n                                        : jvmHeapPercent > 60\n                                            ? '#f59e0b'\n                                            : '#10b981' }) })] }), _jsxs(Card, { className: \"p-6\", children: [_jsx(\"h3\", { className: \"text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4\", children: \"\\u96C6\\u7FA4\\u5065\\u5EB7\\u8BE6\\u60C5\" }), _jsxs(\"div\", { className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\", children: [_jsxs(\"div\", { children: [_jsx(\"p\", { className: \"text-sm text-neutral-500 dark:text-neutral-400\", children: \"\\u4E3B\\u5206\\u7247\" }), _jsx(\"p\", { className: \"text-lg font-semibold text-neutral-900 dark:text-neutral-100\", children: clusterHealth.active_primary_shards })] }), _jsxs(\"div\", { children: [_jsx(\"p\", { className: \"text-sm text-neutral-500 dark:text-neutral-400\", children: \"\\u6D3B\\u8DC3\\u5206\\u7247\" }), _jsx(\"p\", { className: \"text-lg font-semibold text-neutral-900 dark:text-neutral-100\", children: clusterHealth.active_shards })] }), _jsxs(\"div\", { children: [_jsx(\"p\", { className: \"text-sm text-neutral-500 dark:text-neutral-400\", children: \"\\u91CD\\u5B9A\\u4F4D\\u5206\\u7247\" }), _jsx(\"p\", { className: \"text-lg font-semibold text-neutral-900 dark:text-neutral-100\", children: clusterHealth.relocating_shards })] }), _jsxs(\"div\", { children: [_jsx(\"p\", { className: \"text-sm text-neutral-500 dark:text-neutral-400\", children: \"\\u521D\\u59CB\\u5316\\u5206\\u7247\" }), _jsx(\"p\", { className: \"text-lg font-semibold text-neutral-900 dark:text-neutral-100\", children: clusterHealth.initializing_shards })] }), _jsxs(\"div\", { children: [_jsx(\"p\", { className: \"text-sm text-neutral-500 dark:text-neutral-400\", children: \"\\u672A\\u5206\\u914D\\u5206\\u7247\" }), _jsx(\"p\", { className: cn('text-lg font-semibold', clusterHealth.unassigned_shards > 0\n                                                    ? 'text-red-600 dark:text-red-400'\n                                                    : 'text-neutral-900 dark:text-neutral-100'), children: clusterHealth.unassigned_shards })] }), _jsxs(\"div\", { children: [_jsx(\"p\", { className: \"text-sm text-neutral-500 dark:text-neutral-400\", children: \"\\u5F85\\u5904\\u7406\\u4EFB\\u52A1\" }), _jsx(\"p\", { className: \"text-lg font-semibold text-neutral-900 dark:text-neutral-100\", children: clusterHealth.number_of_pending_tasks })] }), _jsxs(\"div\", { children: [_jsx(\"p\", { className: \"text-sm text-neutral-500 dark:text-neutral-400\", children: \"\\u5206\\u7247\\u5B8C\\u6210\\u5EA6\" }), _jsxs(\"p\", { className: \"text-lg font-semibold text-neutral-900 dark:text-neutral-100\", children: [clusterHealth.active_shards_percent_as_number.toFixed(1), \"%\"] })] }), _jsxs(\"div\", { children: [_jsx(\"p\", { className: \"text-sm text-neutral-500 dark:text-neutral-400\", children: \"\\u6700\\u5927\\u7B49\\u5F85\\u65F6\\u95F4\" }), _jsxs(\"p\", { className: \"text-lg font-semibold text-neutral-900 dark:text-neutral-100\", children: [clusterHealth.task_max_waiting_in_queue_millis, \"ms\"] })] })] })] }), _jsx(NodesMonitor, { nodes: nodesStats })] })), activeTab === 'performance' && (_jsx(PerformanceChart, {})), activeTab === 'alerts' && (_jsx(AlertSystem, {})), activeTab === 'health' && (_jsx(SystemHealthDashboard, {}))] }));\n};\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React from 'react';\nimport { cn } from '../../utils/cn';\nconst healthConfig = {\n    green: {\n        color: 'bg-green-500',\n        textColor: 'text-green-600',\n        label: '健康',\n        description: '集群运行正常',\n    },\n    yellow: {\n        color: 'bg-yellow-500',\n        textColor: 'text-yellow-600',\n        label: '警告',\n        description: '集群存在警告',\n    },\n    red: {\n        color: 'bg-red-500',\n        textColor: 'text-red-600',\n        label: '错误',\n        description: '集群存在错误',\n    },\n};\nconst sizeConfig = {\n    sm: {\n        dot: 'w-2 h-2',\n        text: 'text-xs',\n    },\n    md: {\n        dot: 'w-3 h-3',\n        text: 'text-sm',\n    },\n    lg: {\n        dot: 'w-4 h-4',\n        text: 'text-base',\n    },\n};\nexport const HealthIndicator = ({ health, size = 'md', showText = true, className, }) => {\n    const config = healthConfig[health];\n    const sizeStyles = sizeConfig[size];\n    return (_jsxs(\"div\", { className: cn('flex items-center gap-2', className), children: [_jsx(\"div\", { className: cn('rounded-full flex-shrink-0', config.color, sizeStyles.dot), title: config.description }), showText && (_jsx(\"span\", { className: cn('font-medium', config.textColor, sizeStyles.text), children: config.label }))] }));\n};\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React from 'react';\nimport { Card } from '../UI/Card';\nimport { cn } from '../../utils/cn';\nexport const MetricCard = ({ title, value, subtitle, icon, trend, className, children, }) => {\n    return (_jsx(Card, { className: cn('p-6', className), children: _jsxs(\"div\", { className: \"flex items-start justify-between\", children: [_jsxs(\"div\", { className: \"flex-1\", children: [_jsxs(\"div\", { className: \"flex items-center gap-2 mb-2\", children: [icon && (_jsx(\"div\", { className: \"text-neutral-500 dark:text-neutral-400\", children: icon })), _jsx(\"h3\", { className: \"text-sm font-medium text-neutral-600 dark:text-neutral-300\", children: title })] }), _jsx(\"div\", { className: \"mb-1\", children: _jsx(\"span\", { className: \"text-2xl font-semibold text-neutral-900 dark:text-neutral-100\", children: value }) }), subtitle && (_jsx(\"p\", { className: \"text-sm text-neutral-500 dark:text-neutral-400\", children: subtitle })), trend && (_jsxs(\"div\", { className: \"flex items-center gap-1 mt-2\", children: [_jsxs(\"span\", { className: cn('text-xs font-medium', trend.isPositive\n                                        ? 'text-green-600 dark:text-green-400'\n                                        : 'text-red-600 dark:text-red-400'), children: [trend.isPositive ? '+' : '', trend.value, \"%\"] }), _jsx(\"span\", { className: \"text-xs text-neutral-500 dark:text-neutral-400\", children: \"vs \\u4E0A\\u6B21\\u66F4\\u65B0\" })] }))] }), children && _jsx(\"div\", { className: \"ml-4\", children: children })] }) }));\n};\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React from 'react';\nimport { Card } from '../UI/Card';\nimport { CircularProgress } from './CircularProgress';\nimport { cn } from '../../utils/cn';\nconst formatBytes = (bytes) => {\n    if (bytes === 0)\n        return '0 B';\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\nconst getRoleColor = (role) => {\n    const roleColors = {\n        master: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',\n        data: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',\n        ingest: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',\n        ml: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',\n        coordinating_only: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',\n    };\n    return (roleColors[role] ||\n        'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200');\n};\nexport const NodesMonitor = ({ nodes, className, }) => {\n    const nodeEntries = Object.entries(nodes);\n    if (nodeEntries.length === 0) {\n        return (_jsx(Card, { className: cn('p-6', className), children: _jsx(\"div\", { className: \"text-center text-neutral-500 dark:text-neutral-400\", children: \"\\u6682\\u65E0\\u8282\\u70B9\\u6570\\u636E\" }) }));\n    }\n    return (_jsxs(\"div\", { className: cn('space-y-4', className), children: [_jsxs(\"h3\", { className: \"text-lg font-semibold text-neutral-900 dark:text-neutral-100\", children: [\"\\u8282\\u70B9\\u76D1\\u63A7 (\", nodeEntries.length, \" \\u4E2A\\u8282\\u70B9)\"] }), _jsx(\"div\", { className: \"grid gap-4\", children: nodeEntries.map(([nodeId, node]) => (_jsxs(Card, { className: \"p-6\", children: [_jsxs(\"div\", { className: \"flex items-start justify-between mb-4\", children: [_jsxs(\"div\", { children: [_jsx(\"h4\", { className: \"font-medium text-neutral-900 dark:text-neutral-100\", children: node.name }), _jsxs(\"p\", { className: \"text-sm text-neutral-500 dark:text-neutral-400\", children: [node.host, \" (\", node.ip, \")\"] })] }), _jsx(\"div\", { className: \"flex flex-wrap gap-1\", children: node.roles.map(role => (_jsx(\"span\", { className: cn('px-2 py-1 text-xs font-medium rounded-full', getRoleColor(role)), children: role }, role))) })] }), _jsxs(\"div\", { className: \"grid grid-cols-1 md:grid-cols-3 gap-6\", children: [_jsxs(\"div\", { className: \"flex items-center gap-4\", children: [_jsx(CircularProgress, { value: node.os.cpu.percent, size: 50, color: node.os.cpu.percent > 80\n                                                ? '#ef4444'\n                                                : node.os.cpu.percent > 60\n                                                    ? '#f59e0b'\n                                                    : '#10b981' }), _jsxs(\"div\", { children: [_jsx(\"p\", { className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\", children: \"CPU \\u4F7F\\u7528\\u7387\" }), _jsxs(\"p\", { className: \"text-xs text-neutral-500 dark:text-neutral-400\", children: [\"\\u8D1F\\u8F7D: \", node.os.cpu.load_average?.['1m']?.toFixed(2) || 'N/A'] })] })] }), _jsxs(\"div\", { className: \"flex items-center gap-4\", children: [_jsx(CircularProgress, { value: node.os.mem.used_percent, size: 50, color: node.os.mem.used_percent > 80\n                                                ? '#ef4444'\n                                                : node.os.mem.used_percent > 60\n                                                    ? '#f59e0b'\n                                                    : '#10b981' }), _jsxs(\"div\", { children: [_jsx(\"p\", { className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\", children: \"\\u5185\\u5B58\\u4F7F\\u7528\\u7387\" }), _jsxs(\"p\", { className: \"text-xs text-neutral-500 dark:text-neutral-400\", children: [formatBytes(node.os.mem.used_in_bytes), \" /\", ' ', formatBytes(node.os.mem.total_in_bytes)] })] })] }), _jsxs(\"div\", { className: \"flex items-center gap-4\", children: [_jsx(CircularProgress, { value: node.jvm.mem.heap_used_percent, size: 50, color: node.jvm.mem.heap_used_percent > 80\n                                                ? '#ef4444'\n                                                : node.jvm.mem.heap_used_percent > 60\n                                                    ? '#f59e0b'\n                                                    : '#10b981' }), _jsxs(\"div\", { children: [_jsx(\"p\", { className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\", children: \"JVM \\u5806\\u5185\\u5B58\" }), _jsxs(\"p\", { className: \"text-xs text-neutral-500 dark:text-neutral-400\", children: [formatBytes(node.jvm.mem.heap_used_in_bytes), \" /\", ' ', formatBytes(node.jvm.mem.heap_max_in_bytes)] })] })] })] }), _jsxs(\"div\", { className: \"mt-4 pt-4 border-t border-neutral-200 dark:border-neutral-700\", children: [_jsxs(\"div\", { className: \"flex items-center justify-between\", children: [_jsx(\"span\", { className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\", children: \"\\u78C1\\u76D8\\u4F7F\\u7528\\u60C5\\u51B5\" }), _jsxs(\"span\", { className: \"text-sm text-neutral-500 dark:text-neutral-400\", children: [formatBytes(node.fs.total.total_in_bytes -\n                                                    node.fs.total.available_in_bytes), ' ', \"/ \", formatBytes(node.fs.total.total_in_bytes)] })] }), _jsx(\"div\", { className: \"mt-2 w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2\", children: _jsx(\"div\", { className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\", style: {\n                                            width: `${((node.fs.total.total_in_bytes - node.fs.total.available_in_bytes) / node.fs.total.total_in_bytes) * 100}%`,\n                                        } }) })] })] }, nodeId))) })] }));\n};\n", "import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { useState, useEffect, useMemo } from 'react';\nimport { MonitoringService } from '../../services/monitoring';\nimport { Card } from '../UI/Card';\nimport { cn } from '../../utils/cn';\nimport { TrendingUp, TrendingDown, Minus, Activity, MemoryStick, HardDrive, Cpu, Database, AlertTriangle, } from 'lucide-react';\nconst metricConfig = {\n    cpuUsage: {\n        name: 'CPU 使用率',\n        unit: '%',\n        color: '#3b82f6',\n        icon: Cpu,\n        threshold: { warning: 70, critical: 85 },\n    },\n    memoryUsage: {\n        name: '内存使用率',\n        unit: '%',\n        color: '#10b981',\n        icon: MemoryStick,\n        threshold: { warning: 75, critical: 90 },\n    },\n    jvmHeapUsage: {\n        name: 'JVM 堆内存',\n        unit: '%',\n        color: '#f59e0b',\n        icon: Activity,\n        threshold: { warning: 80, critical: 95 },\n    },\n    diskUsage: {\n        name: '磁盘使用率',\n        unit: '%',\n        color: '#8b5cf6',\n        icon: HardDrive,\n        threshold: { warning: 80, critical: 90 },\n    },\n    activeShards: {\n        name: '活跃分片',\n        unit: '',\n        color: '#06b6d4',\n        icon: Database,\n        threshold: { warning: 1000, critical: 2000 },\n    },\n    unassignedShards: {\n        name: '未分配分片',\n        unit: '',\n        color: '#ef4444',\n        icon: AlertTriangle,\n        threshold: { warning: 1, critical: 5 },\n    },\n};\nexport const PerformanceChart = ({ className, height = 300, }) => {\n    const [selectedMetric, setSelectedMetric] = useState('cpuUsage');\n    const [timeRange, setTimeRange] = useState(1); // hours\n    const [metrics, setMetrics] = useState([]);\n    const [trends, setTrends] = useState([]);\n    const monitoringService = MonitoringService.getInstance();\n    useEffect(() => {\n        const updateData = () => {\n            setMetrics(monitoringService.getPerformanceMetrics(timeRange));\n            setTrends(monitoringService.getTrendAnalysis());\n        };\n        updateData();\n        const interval = setInterval(updateData, 30000); // Update every 30 seconds\n        return () => clearInterval(interval);\n    }, [timeRange]);\n    const chartData = useMemo(() => {\n        return metrics.map(metric => ({\n            timestamp: metric.timestamp,\n            value: metric[selectedMetric],\n            label: new Date(metric.timestamp).toLocaleTimeString(),\n        }));\n    }, [metrics, selectedMetric]);\n    const currentTrend = trends.find(t => t.metric === selectedMetric);\n    const config = metricConfig[selectedMetric];\n    const IconComponent = config.icon;\n    const renderMiniChart = (data) => {\n        if (data.length < 2)\n            return null;\n        const maxValue = Math.max(...data.map(d => d.value));\n        const minValue = Math.min(...data.map(d => d.value));\n        const range = maxValue - minValue || 1;\n        const points = data.map((point, index) => {\n            const x = (index / (data.length - 1)) * 100;\n            const y = 100 - ((point.value - minValue) / range) * 100;\n            return `${x},${y}`;\n        }).join(' ');\n        return (_jsxs(\"svg\", { width: \"100%\", height: height, className: \"overflow-visible\", children: [_jsx(\"defs\", { children: _jsx(\"pattern\", { id: \"grid\", width: \"20\", height: \"20\", patternUnits: \"userSpaceOnUse\", children: _jsx(\"path\", { d: \"M 20 0 L 0 0 0 20\", fill: \"none\", stroke: \"currentColor\", strokeWidth: \"0.5\", opacity: \"0.1\" }) }) }), _jsx(\"rect\", { width: \"100%\", height: \"100%\", fill: \"url(#grid)\" }), config.threshold && (_jsxs(_Fragment, { children: [_jsx(\"line\", { x1: \"0\", y1: 100 - ((config.threshold.warning - minValue) / range) * 100 + '%', x2: \"100%\", y2: 100 - ((config.threshold.warning - minValue) / range) * 100 + '%', stroke: \"#f59e0b\", strokeWidth: \"1\", strokeDasharray: \"5,5\", opacity: \"0.5\" }), _jsx(\"line\", { x1: \"0\", y1: 100 - ((config.threshold.critical - minValue) / range) * 100 + '%', x2: \"100%\", y2: 100 - ((config.threshold.critical - minValue) / range) * 100 + '%', stroke: \"#ef4444\", strokeWidth: \"1\", strokeDasharray: \"5,5\", opacity: \"0.5\" })] })), _jsx(\"path\", { d: `M 0,100 L ${points} L 100,100 Z`, fill: config.color, fillOpacity: \"0.1\" }), _jsx(\"polyline\", { points: points, fill: \"none\", stroke: config.color, strokeWidth: \"2\", strokeLinejoin: \"round\", strokeLinecap: \"round\" }), data.map((point, index) => {\n                    const x = (index / (data.length - 1)) * 100;\n                    const y = 100 - ((point.value - minValue) / range) * 100;\n                    return (_jsx(\"circle\", { cx: x + '%', cy: y + '%', r: \"3\", fill: config.color, className: \"opacity-0 hover:opacity-100 transition-opacity\", children: _jsx(\"title\", { children: `${point.label}: ${point.value.toFixed(1)}${config.unit}` }) }, index));\n                })] }));\n    };\n    const getCurrentValue = () => {\n        if (chartData.length === 0)\n            return 0;\n        return chartData[chartData.length - 1].value;\n    };\n    const getValueStatus = (value) => {\n        if (!config.threshold)\n            return 'normal';\n        if (value >= config.threshold.critical)\n            return 'critical';\n        if (value >= config.threshold.warning)\n            return 'warning';\n        return 'normal';\n    };\n    const currentValue = getCurrentValue();\n    const valueStatus = getValueStatus(currentValue);\n    return (_jsxs(\"div\", { className: cn('space-y-4', className), children: [_jsxs(\"div\", { className: \"flex items-center justify-between\", children: [_jsx(\"h3\", { className: \"text-lg font-semibold text-neutral-900 dark:text-neutral-100\", children: \"\\u6027\\u80FD\\u8D8B\\u52BF\\u5206\\u6790\" }), _jsx(\"div\", { className: \"flex bg-neutral-100 dark:bg-neutral-800 rounded-lg p-1\", children: [\n                            { value: 1, label: '1小时' },\n                            { value: 6, label: '6小时' },\n                            { value: 24, label: '24小时' },\n                        ].map(({ value, label }) => (_jsx(\"button\", { onClick: () => setTimeRange(value), className: cn('px-3 py-1 text-sm font-medium rounded-md transition-colors', timeRange === value\n                                ? 'bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 shadow-sm'\n                                : 'text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100'), children: label }, value))) })] }), _jsx(\"div\", { className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3\", children: Object.entries(metricConfig).map(([key, config]) => {\n                    const metricKey = key;\n                    const isSelected = selectedMetric === metricKey;\n                    const MetricIcon = config.icon;\n                    return (_jsxs(\"button\", { onClick: () => setSelectedMetric(metricKey), className: cn('p-3 rounded-lg border-2 transition-all text-left', isSelected\n                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                            : 'border-neutral-200 dark:border-neutral-700 hover:border-neutral-300 dark:hover:border-neutral-600'), children: [_jsxs(\"div\", { className: \"flex items-center gap-2 mb-1\", children: [_jsx(MetricIcon, { size: 16, style: { color: config.color } }), _jsx(\"span\", { className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\", children: config.name })] }), _jsx(\"div\", { className: \"text-xs text-neutral-600 dark:text-neutral-400\", children: \"\\u70B9\\u51FB\\u67E5\\u770B\\u8D8B\\u52BF\" })] }, key));\n                }) }), _jsxs(Card, { className: \"p-6\", children: [_jsxs(\"div\", { className: \"flex items-center justify-between mb-4\", children: [_jsxs(\"div\", { className: \"flex items-center gap-3\", children: [_jsx(IconComponent, { size: 24, style: { color: config.color } }), _jsxs(\"div\", { children: [_jsx(\"h4\", { className: \"text-lg font-semibold text-neutral-900 dark:text-neutral-100\", children: config.name }), _jsxs(\"div\", { className: \"flex items-center gap-2\", children: [_jsxs(\"span\", { className: cn('text-2xl font-bold', valueStatus === 'critical' ? 'text-red-600 dark:text-red-400' :\n                                                            valueStatus === 'warning' ? 'text-yellow-600 dark:text-yellow-400' :\n                                                                'text-neutral-900 dark:text-neutral-100'), children: [currentValue.toFixed(1), config.unit] }), currentTrend && (_jsxs(\"div\", { className: \"flex items-center gap-1\", children: [currentTrend.trend === 'increasing' ? (_jsx(TrendingUp, { className: \"text-red-500\", size: 16 })) : currentTrend.trend === 'decreasing' ? (_jsx(TrendingDown, { className: \"text-green-500\", size: 16 })) : (_jsx(Minus, { className: \"text-neutral-500\", size: 16 })), _jsxs(\"span\", { className: cn('text-sm font-medium', currentTrend.trend === 'increasing' ? 'text-red-600 dark:text-red-400' :\n                                                                    currentTrend.trend === 'decreasing' ? 'text-green-600 dark:text-green-400' :\n                                                                        'text-neutral-600 dark:text-neutral-400'), children: [currentTrend.changeRate > 0 ? '+' : '', currentTrend.changeRate.toFixed(1), \"%/h\"] })] }))] })] })] }), _jsx(\"div\", { className: cn('px-3 py-1 rounded-full text-sm font-medium', valueStatus === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :\n                                    valueStatus === 'warning' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :\n                                        'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'), children: valueStatus === 'critical' ? '严重' :\n                                    valueStatus === 'warning' ? '警告' : '正常' })] }), _jsx(\"div\", { className: \"relative\", children: chartData.length > 0 ? (renderMiniChart(chartData)) : (_jsx(\"div\", { className: \"flex items-center justify-center h-64 text-neutral-500 dark:text-neutral-400\", children: _jsxs(\"div\", { className: \"text-center\", children: [_jsx(Activity, { size: 48, className: \"mx-auto mb-3 opacity-50\" }), _jsx(\"p\", { children: \"\\u6682\\u65E0\\u6570\\u636E\" }), _jsx(\"p\", { className: \"text-sm\", children: \"\\u7B49\\u5F85\\u76D1\\u63A7\\u6570\\u636E\\u6536\\u96C6...\" })] }) })) }), currentTrend && (_jsxs(\"div\", { className: \"mt-4 p-4 bg-neutral-50 dark:bg-neutral-800 rounded-lg\", children: [_jsx(\"h5\", { className: \"font-medium text-neutral-900 dark:text-neutral-100 mb-2\", children: \"\\u8D8B\\u52BF\\u5206\\u6790\" }), _jsxs(\"div\", { className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\", children: [_jsxs(\"div\", { children: [_jsx(\"span\", { className: \"text-neutral-600 dark:text-neutral-400\", children: \"\\u8D8B\\u52BF\\u65B9\\u5411:\" }), _jsxs(\"div\", { className: \"flex items-center gap-1 mt-1\", children: [currentTrend.trend === 'increasing' ? (_jsx(TrendingUp, { className: \"text-red-500\", size: 16 })) : currentTrend.trend === 'decreasing' ? (_jsx(TrendingDown, { className: \"text-green-500\", size: 16 })) : (_jsx(Minus, { className: \"text-neutral-500\", size: 16 })), _jsx(\"span\", { className: \"font-medium text-neutral-900 dark:text-neutral-100\", children: currentTrend.trend === 'increasing' ? '上升' :\n                                                            currentTrend.trend === 'decreasing' ? '下降' : '稳定' })] })] }), _jsxs(\"div\", { children: [_jsx(\"span\", { className: \"text-neutral-600 dark:text-neutral-400\", children: \"\\u9884\\u6D4B\\u503C (1\\u5C0F\\u65F6\\u540E):\" }), _jsxs(\"div\", { className: \"font-medium text-neutral-900 dark:text-neutral-100 mt-1\", children: [currentTrend.prediction.toFixed(1), config.unit] })] }), _jsxs(\"div\", { children: [_jsx(\"span\", { className: \"text-neutral-600 dark:text-neutral-400\", children: \"\\u7F6E\\u4FE1\\u5EA6:\" }), _jsxs(\"div\", { className: \"flex items-center gap-2 mt-1\", children: [_jsx(\"div\", { className: \"flex-1 bg-neutral-200 dark:bg-neutral-700 rounded-full h-2\", children: _jsx(\"div\", { className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\", style: { width: `${currentTrend.confidence * 100}%` } }) }), _jsxs(\"span\", { className: \"font-medium text-neutral-900 dark:text-neutral-100\", children: [(currentTrend.confidence * 100).toFixed(0), \"%\"] })] })] })] })] }))] })] }));\n};\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { useState, useEffect } from 'react';\nimport { MonitoringService } from '../../services/monitoring';\nimport { Card } from '../UI/Card';\nimport { cn } from '../../utils/cn';\nimport { Shield, AlertTriangle, CheckCircle, Lightbulb, Activity, Clock, Zap, } from 'lucide-react';\nexport const SystemHealthDashboard = ({ className, }) => {\n    const [healthStatus, setHealthStatus] = useState({\n        overall: 'healthy',\n        issues: [],\n        recommendations: [],\n    });\n    const [lastUpdate, setLastUpdate] = useState(new Date());\n    const monitoringService = MonitoringService.getInstance();\n    useEffect(() => {\n        const updateHealth = () => {\n            setHealthStatus(monitoringService.getSystemHealth());\n            setLastUpdate(new Date());\n        };\n        updateHealth();\n        const interval = setInterval(updateHealth, 30000); // Update every 30 seconds\n        return () => clearInterval(interval);\n    }, []);\n    const getHealthConfig = (status) => {\n        switch (status) {\n            case 'healthy':\n                return {\n                    icon: CheckCircle,\n                    color: 'text-green-600 dark:text-green-400',\n                    bgColor: 'bg-green-50 dark:bg-green-900/20',\n                    borderColor: 'border-green-200 dark:border-green-800',\n                    title: '系统健康',\n                    description: '所有系统指标正常',\n                };\n            case 'warning':\n                return {\n                    icon: AlertTriangle,\n                    color: 'text-yellow-600 dark:text-yellow-400',\n                    bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',\n                    borderColor: 'border-yellow-200 dark:border-yellow-800',\n                    title: '需要关注',\n                    description: '发现一些需要关注的问题',\n                };\n            case 'critical':\n                return {\n                    icon: AlertTriangle,\n                    color: 'text-red-600 dark:text-red-400',\n                    bgColor: 'bg-red-50 dark:bg-red-900/20',\n                    borderColor: 'border-red-200 dark:border-red-800',\n                    title: '严重问题',\n                    description: '发现严重问题，需要立即处理',\n                };\n        }\n    };\n    const config = getHealthConfig(healthStatus.overall);\n    const IconComponent = config.icon;\n    const getQuickActions = () => {\n        const actions = [];\n        if (healthStatus.overall === 'critical') {\n            actions.push({\n                icon: Zap,\n                title: '紧急处理',\n                description: '立即检查集群状态',\n                color: 'text-red-600 dark:text-red-400',\n                bgColor: 'bg-red-50 dark:bg-red-900/20',\n            });\n        }\n        if (healthStatus.recommendations.length > 0) {\n            actions.push({\n                icon: Lightbulb,\n                title: '优化建议',\n                description: '查看系统优化建议',\n                color: 'text-blue-600 dark:text-blue-400',\n                bgColor: 'bg-blue-50 dark:bg-blue-900/20',\n            });\n        }\n        actions.push({\n            icon: Activity,\n            title: '性能监控',\n            description: '查看详细性能指标',\n            color: 'text-purple-600 dark:text-purple-400',\n            bgColor: 'bg-purple-50 dark:bg-purple-900/20',\n        });\n        return actions;\n    };\n    const quickActions = getQuickActions();\n    return (_jsxs(\"div\", { className: cn('space-y-6', className), children: [_jsx(Card, { className: cn('p-6 border-l-4', config.bgColor, config.borderColor), children: _jsxs(\"div\", { className: \"flex items-start gap-4\", children: [_jsx(IconComponent, { className: cn('mt-1 flex-shrink-0', config.color), size: 32 }), _jsxs(\"div\", { className: \"flex-1\", children: [_jsxs(\"div\", { className: \"flex items-center justify-between mb-2\", children: [_jsx(\"h3\", { className: \"text-xl font-semibold text-neutral-900 dark:text-neutral-100\", children: config.title }), _jsxs(\"div\", { className: \"flex items-center gap-2 text-sm text-neutral-500 dark:text-neutral-400\", children: [_jsx(Clock, { size: 14 }), \"\\u6700\\u540E\\u66F4\\u65B0: \", lastUpdate.toLocaleTimeString()] })] }), _jsx(\"p\", { className: \"text-neutral-700 dark:text-neutral-300 mb-4\", children: config.description }), _jsxs(\"div\", { className: \"grid grid-cols-1 md:grid-cols-3 gap-4\", children: [_jsxs(\"div\", { className: \"text-center p-3 bg-white dark:bg-neutral-800 rounded-lg\", children: [_jsx(\"div\", { className: \"text-2xl font-bold text-neutral-900 dark:text-neutral-100\", children: healthStatus.issues.length }), _jsx(\"div\", { className: \"text-sm text-neutral-600 dark:text-neutral-400\", children: \"\\u6D3B\\u8DC3\\u95EE\\u9898\" })] }), _jsxs(\"div\", { className: \"text-center p-3 bg-white dark:bg-neutral-800 rounded-lg\", children: [_jsx(\"div\", { className: \"text-2xl font-bold text-neutral-900 dark:text-neutral-100\", children: healthStatus.recommendations.length }), _jsx(\"div\", { className: \"text-sm text-neutral-600 dark:text-neutral-400\", children: \"\\u4F18\\u5316\\u5EFA\\u8BAE\" })] }), _jsxs(\"div\", { className: \"text-center p-3 bg-white dark:bg-neutral-800 rounded-lg\", children: [_jsx(\"div\", { className: cn('text-2xl font-bold', config.color), children: healthStatus.overall === 'healthy' ? '100%' :\n                                                        healthStatus.overall === 'warning' ? '75%' : '25%' }), _jsx(\"div\", { className: \"text-sm text-neutral-600 dark:text-neutral-400\", children: \"\\u5065\\u5EB7\\u5EA6\" })] })] })] })] }) }), _jsxs(\"div\", { className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\", children: [_jsxs(Card, { className: \"p-6\", children: [_jsxs(\"div\", { className: \"flex items-center gap-2 mb-4\", children: [_jsx(AlertTriangle, { className: \"text-red-500\", size: 20 }), _jsx(\"h4\", { className: \"text-lg font-semibold text-neutral-900 dark:text-neutral-100\", children: \"\\u5F53\\u524D\\u95EE\\u9898\" }), healthStatus.issues.length > 0 && (_jsx(\"span\", { className: \"px-2 py-1 text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 rounded-full\", children: healthStatus.issues.length }))] }), healthStatus.issues.length === 0 ? (_jsxs(\"div\", { className: \"text-center py-8\", children: [_jsx(CheckCircle, { className: \"mx-auto mb-3 text-green-500\", size: 48 }), _jsx(\"p\", { className: \"text-neutral-600 dark:text-neutral-400\", children: \"\\u6CA1\\u6709\\u53D1\\u73B0\\u95EE\\u9898\\uFF0C\\u7CFB\\u7EDF\\u8FD0\\u884C\\u6B63\\u5E38\" })] })) : (_jsx(\"div\", { className: \"space-y-3\", children: healthStatus.issues.map((issue, index) => (_jsxs(\"div\", { className: \"flex items-start gap-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg\", children: [_jsx(AlertTriangle, { className: \"text-red-500 flex-shrink-0 mt-0.5\", size: 16 }), _jsx(\"span\", { className: \"text-neutral-700 dark:text-neutral-300\", children: issue })] }, index))) }))] }), _jsxs(Card, { className: \"p-6\", children: [_jsxs(\"div\", { className: \"flex items-center gap-2 mb-4\", children: [_jsx(Lightbulb, { className: \"text-blue-500\", size: 20 }), _jsx(\"h4\", { className: \"text-lg font-semibold text-neutral-900 dark:text-neutral-100\", children: \"\\u4F18\\u5316\\u5EFA\\u8BAE\" }), healthStatus.recommendations.length > 0 && (_jsx(\"span\", { className: \"px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full\", children: healthStatus.recommendations.length }))] }), healthStatus.recommendations.length === 0 ? (_jsxs(\"div\", { className: \"text-center py-8\", children: [_jsx(Shield, { className: \"mx-auto mb-3 text-green-500\", size: 48 }), _jsx(\"p\", { className: \"text-neutral-600 dark:text-neutral-400\", children: \"\\u7CFB\\u7EDF\\u914D\\u7F6E\\u826F\\u597D\\uFF0C\\u6682\\u65E0\\u4F18\\u5316\\u5EFA\\u8BAE\" })] })) : (_jsx(\"div\", { className: \"space-y-3\", children: healthStatus.recommendations.map((recommendation, index) => (_jsxs(\"div\", { className: \"flex items-start gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\", children: [_jsx(Lightbulb, { className: \"text-blue-500 flex-shrink-0 mt-0.5\", size: 16 }), _jsx(\"span\", { className: \"text-neutral-700 dark:text-neutral-300\", children: recommendation })] }, index))) }))] })] }), _jsxs(Card, { className: \"p-6\", children: [_jsx(\"h4\", { className: \"text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4\", children: \"\\u5FEB\\u901F\\u64CD\\u4F5C\" }), _jsx(\"div\", { className: \"grid grid-cols-1 md:grid-cols-3 gap-4\", children: quickActions.map((action, index) => {\n                            const ActionIcon = action.icon;\n                            return (_jsxs(\"button\", { className: cn('p-4 rounded-lg border-2 border-transparent hover:border-current transition-all text-left group', action.bgColor), children: [_jsxs(\"div\", { className: \"flex items-center gap-3 mb-2\", children: [_jsx(ActionIcon, { className: cn('flex-shrink-0', action.color), size: 24 }), _jsx(\"h5\", { className: \"font-medium text-neutral-900 dark:text-neutral-100 group-hover:underline\", children: action.title })] }), _jsx(\"p\", { className: \"text-sm text-neutral-600 dark:text-neutral-400\", children: action.description })] }, index));\n                        }) })] }), _jsxs(Card, { className: \"p-6\", children: [_jsx(\"h4\", { className: \"text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4\", children: \"\\u7CFB\\u7EDF\\u72B6\\u6001\\u6307\\u6807\" }), _jsx(\"div\", { className: \"grid grid-cols-2 md:grid-cols-4 gap-4\", children: [\n                            {\n                                label: '连接状态',\n                                status: 'healthy',\n                                value: '正常',\n                                icon: CheckCircle,\n                            },\n                            {\n                                label: '数据完整性',\n                                status: healthStatus.overall === 'critical' ? 'critical' : 'healthy',\n                                value: healthStatus.overall === 'critical' ? '异常' : '正常',\n                                icon: healthStatus.overall === 'critical' ? AlertTriangle : CheckCircle,\n                            },\n                            {\n                                label: '性能状态',\n                                status: healthStatus.overall === 'warning' ? 'warning' : 'healthy',\n                                value: healthStatus.overall === 'warning' ? '警告' : '良好',\n                                icon: healthStatus.overall === 'warning' ? AlertTriangle : CheckCircle,\n                            },\n                            {\n                                label: '监控状态',\n                                status: 'healthy',\n                                value: '运行中',\n                                icon: Activity,\n                            },\n                        ].map((indicator, index) => {\n                            const IndicatorIcon = indicator.icon;\n                            const statusColor = indicator.status === 'healthy' ? 'text-green-500' :\n                                indicator.status === 'warning' ? 'text-yellow-500' :\n                                    'text-red-500';\n                            return (_jsxs(\"div\", { className: \"text-center p-3 bg-neutral-50 dark:bg-neutral-800 rounded-lg\", children: [_jsx(IndicatorIcon, { className: cn('mx-auto mb-2', statusColor), size: 24 }), _jsx(\"div\", { className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\", children: indicator.label }), _jsx(\"div\", { className: cn('text-xs font-medium', statusColor), children: indicator.value })] }, index));\n                        }) })] })] }));\n};\n"], "names": [], "sourceRoot": ""}