import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';

interface CustomTooltipProps {
  content: string;
  children: React.ReactElement;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
  className?: string;
}

export const CustomTooltip: React.FC<CustomTooltipProps> = ({
  content,
  children,
  placement = 'top',
  delay = 500,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const triggerRef = useRef<HTMLElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const showTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      if (triggerRef.current) {
        const rect = triggerRef.current.getBoundingClientRect();
        const tooltipRect = tooltipRef.current?.getBoundingClientRect();
        
        let x = 0;
        let y = 0;
        
        switch (placement) {
          case 'top':
            x = rect.left + rect.width / 2;
            y = rect.top - 8;
            break;
          case 'bottom':
            x = rect.left + rect.width / 2;
            y = rect.bottom + 8;
            break;
          case 'left':
            x = rect.left - 8;
            y = rect.top + rect.height / 2;
            break;
          case 'right':
            x = rect.right + 8;
            y = rect.top + rect.height / 2;
            break;
        }
        
        setPosition({ x, y });
        setIsVisible(true);
      }
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const clonedChild = React.cloneElement(children, {
    ref: triggerRef,
    onMouseEnter: (e: React.MouseEvent) => {
      showTooltip();
      children.props.onMouseEnter?.(e);
    },
    onMouseLeave: (e: React.MouseEvent) => {
      hideTooltip();
      children.props.onMouseLeave?.(e);
    },
    onFocus: (e: React.FocusEvent) => {
      showTooltip();
      children.props.onFocus?.(e);
    },
    onBlur: (e: React.FocusEvent) => {
      hideTooltip();
      children.props.onBlur?.(e);
    }
  });

  const tooltipElement = isVisible && content ? (
    <div
      ref={tooltipRef}
      className={`
        fixed z-50 px-3 py-2 text-xs font-medium text-white bg-gray-900 dark:bg-gray-700
        rounded-lg shadow-lg border border-gray-200 dark:border-gray-600
        max-w-xs break-words pointer-events-none
        ${placement === 'top' ? 'transform -translate-x-1/2 -translate-y-full' : ''}
        ${placement === 'bottom' ? 'transform -translate-x-1/2' : ''}
        ${placement === 'left' ? 'transform -translate-x-full -translate-y-1/2' : ''}
        ${placement === 'right' ? 'transform -translate-y-1/2' : ''}
        ${className}
      `}
      style={{
        left: position.x,
        top: position.y,
      }}
    >
      {content}
      
      {/* 箭头 */}
      <div
        className={`
          absolute w-2 h-2 bg-gray-900 dark:bg-gray-700 border border-gray-200 dark:border-gray-600
          ${placement === 'top' ? 'bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 rotate-45 border-t-0 border-l-0' : ''}
          ${placement === 'bottom' ? 'top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rotate-45 border-b-0 border-r-0' : ''}
          ${placement === 'left' ? 'right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 rotate-45 border-l-0 border-b-0' : ''}
          ${placement === 'right' ? 'left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2 rotate-45 border-r-0 border-t-0' : ''}
        `}
      />
    </div>
  ) : null;

  return (
    <>
      {clonedChild}
      {tooltipElement && createPortal(tooltipElement, document.body)}
    </>
  );
};

export default CustomTooltip;
