{"version": 3, "file": "src_renderer_components_Query_QueryBuilder_tsx.renderer.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAA+D;AACS;AAC6B;AAC5B;AACY;AAC/B;AAC/C,6BAA6B,0IAA0I;AAC9K,0CAA0C,+CAAQ;AAClD,gCAAgC,+CAAQ;AACxC,sCAAsC,+CAAQ;AAC9C,8CAA8C,+CAAQ;AACtD,4CAA4C,+CAAQ;AACpD,qBAAqB,6CAAM;AAC3B,wBAAwB,6CAAM;AAC9B,oBAAoB,+EAAmB;AACvC,iCAAiC,2FAAyB;AAC1D,2BAA2B,gEAAW,cAAc;AACpD,IAAI,gDAAS;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI,gDAAS;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D,iBAAiB;AAC/E;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,yDAAyD;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,sDAAI,CAAC,oDAAI,IAAI,sBAAsB;AAC1D;AACA,uBAAuB,sDAAI,CAAC,oDAAI,IAAI,sBAAsB;AAC1D;AACA,uBAAuB,sDAAI,CAAC,oDAAQ,IAAI,sBAAsB;AAC9D;AACA,uBAAuB,sDAAI,CAAC,oDAAU,IAAI,sBAAsB;AAChE;AACA;AACA;AACA;AACA,uBAAuB,sDAAI,CAAC,oDAAI,IAAI,sBAAsB;AAC1D;AACA,uBAAuB,sDAAI,CAAC,oDAAI,IAAI,sBAAsB;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,uDAAK,UAAU,wIAAwI,uDAAK,UAAU,oEAAoE,sDAAI,UAAU,qEAAqE,GAAG,uDAAK,UAAU,wCAAwC,uDAAK,UAAU,qDAAqD,sDAAI,WAAW,qGAAqG,mCAAmC,sDAAI,CAAC,oDAAI,IAAI,4EAA4E,4BAA4B,sDAAI,WAAW,iFAAiF,KAAK,8BAA8B,sDAAI,QAAQ,yGAAyG,8BAA8B,uDAAK,QAAQ,0GAA0G,KAAK,IAAI,GAAG,uDAAK,UAAU,mGAAmG,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,oDAAU,IAAI,qCAAqC,GAAG,uDAAK,WAAW,+FAA+F,IAAI,IAAI,sDAAI,WAAW,6CAA6C,8BAA8B,8BAA8B,IAAI,IAAI;AAC3nD,mDAAmD,uDAAK,UAAU,wIAAwI,sDAAI,UAAU,mEAAmE,uDAAK,UAAU,wCAAwC,uDAAK,UAAU,qDAAqD,sDAAI,WAAW,6GAA6G,0BAA0B,sDAAI,WAAW,kFAAkF,KAAK,8BAA8B,sDAAI,QAAQ,yGAAyG,KAAK,GAAG,GAAG,sDAAI,UAAU,kEAAkE,uDAAK,UAAU,qDAAqD,uDAAK,WAAW,iHAAiH,8BAA8B,sDAAI,CAAC,qDAAG,IAAI,qEAAqE,KAAK,GAAG,IAAI;AAChsC;AACA;AACA,gBAAgB,sDAAI,UAAU,cAAc,oDAAoD;AAChG;AACA,qDAAqD;AACrD;AACA,YAAY,uDAAK,UAAU,oDAAoD,uDAAK,UAAU,kCAAkC,sDAAI,YAAY;AAChJ;AACA;AACA;AACA,yBAAyB,8PAA8P,UAAU,GAAG,iBAAiB,sDAAI,UAAU,4EAA4E,sDAAI,UAAU,8FAA8F,GAAG,6BAA6B,sDAAI,UAAU,4EAA4E,sDAAI,CAAC,oDAAM,IAAI,oCAAoC,GAAG,KAAK,wCAAwC,uDAAK,UAAU,uKAAuK,sDAAI,UAAU,4GAA4G,uDAAK,UAAU,2DAA2D,uDAAK,WAAW,iJAAiJ,wBAAwB,sDAAI,aAAa,oKAAoK,KAAK,GAAG,GAAG,sDAAI,UAAU,0GAA0G,GAAG,sDAAI,UAAU,4GAA4G,sDAAI,QAAQ;AAC5sD;AACA,qDAAqD,GAAG,IAAI,KAAK;AACjE;AACA;AACO;AACP,kCAAkC,+CAAQ;AAC1C,sCAAsC,+CAAQ;AAC9C,oBAAoB,+EAAmB;AACvC,wBAAwB,kDAAW;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI,gDAAS;AACb;AACA,KAAK;AACL,2BAA2B,kDAAW;AACtC;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClP+D;AACJ;AACjB;AACS;AACA;AACS;AACa;AACY;AACnD;AACI;AACF;AACwB;AACE;AACuC;AACtF;AACf,YAAY,uCAAuC,EAAE,4DAAa;AAClE,YAAY,QAAQ,EAAE,4DAAa;AACnC,sBAAsB,6CAAM;AAC5B,YAAY,UAAU,EAAE,wEAAe;AACvC,yBAAyB,8EAAmB;AAC5C,iCAAiC,0FAAyB;AAC1D,8CAA8C,+CAAQ;AACtD,0CAA0C,+CAAQ;AAClD,sDAAsD,+CAAQ;AAC9D,kDAAkD,+CAAQ;AAC1D,0DAA0D,+CAAQ;AAClE,gDAAgD,+CAAQ;AACxD,0CAA0C,+CAAQ;AAClD;AACA,IAAI,gDAAS;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D;AAC1D;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD;AACrD;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA,sDAAsD,gBAAgB;AACtE;AACA;AACA;AACA,qEAAqE,gBAAgB;AACrF,wEAAwE,qBAAqB;AAC7F,yEAAyE,gBAAgB;AACzF,qEAAqE;AACrE;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC,4CAA4C,gBAAgB;AAC5D,6CAA6C,gBAAgB;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,2DAA2D;AAC3D,0DAA0D;AAC1D,2DAA2D;AAC3D,0DAA0D;AAC1D,6DAA6D;AAC7D;AACA;AACA;AACA,qCAAqC;AACrC;AACA;AACA;AACA,qDAAqD;AACrD;AACA,qCAAqC;AACrC;AACA;AACA;AACA,oDAAoD,eAAe;AACnE,wDAAwD,eAAe;AACvE,sDAAsD,eAAe;AACrE,sDAAsD,eAAe;AACrE,oEAAoE,gBAAgB;AACpF,qDAAqD;AACrD,yCAAyC;AACzC,qCAAqC;AACrC,gDAAgD,gBAAgB;AAChE,8CAA8C,gBAAgB;AAC9D,6CAA6C,gBAAgB;AAC7D,8CAA8C,gBAAgB;AAC9D;AACA;AACA;AACA,qDAAqD,gBAAgB;AACrE,6DAA6D,gBAAgB;AAC7E,sDAAsD,wBAAwB,kBAAkB;AAChG,gEAAgE;AAChE;AACA,qCAAqC;AACrC,2DAA2D,gBAAgB;AAC3E;AACA;AACA;AACA,qDAAqD,gBAAgB;AACrE,sDAAsD,wBAAwB,kBAAkB;AAChG,oDAAoD;AACpD;AACA;AACA,iCAAiC;AACjC,6BAA6B;AAC7B;AACA;AACA;AACA;AACA,0CAA0C,gBAAgB;AAC1D;AACA;AACA;AACA;AACA;AACA,0DAA0D,uBAAuB;AACjF;AACA;AACA;AACA,yEAAyE,uBAAuB;AAChG,2EAA2E;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC,6BAA6B;AAC7B,oCAAoC,4CAA4C;AAChF,oCAAoC,4BAA4B;AAChE;AACA;AACA,sCAAsC,iBAAiB;AACvD,sCAAsC,gBAAgB;AACtD,sCAAsC,wBAAwB,kBAAkB;AAChF;AACA;AACA;AACA,wDAAwD,wBAAwB,kBAAkB;AAClG,wDAAwD,wBAAwB;AAChF;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA,8CAA8C,gBAAgB;AAC9D,gDAAgD,wBAAwB,kBAAkB;AAC1F,iDAAiD,wBAAwB;AACzE;AACA,6BAA6B;AAC7B,oCAAoC,gBAAgB;AACpD,4CAA4C,gBAAgB;AAC5D,uCAAuC,gBAAgB;AACvD,gDAAgD;AAChD,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,mBAAmB;AACtE;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,+CAA+C,OAAO,QAAQ,IAAI,kBAAkB,QAAQ,8BAA8B,GAAG;AAC7H;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,qDAAqD,gBAAgB,cAAc,oBAAoB,SAAS,MAAM,SAAS,+BAA+B;AAC9J;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,8CAA8C,OAAO,QAAQ,IAAI,kBAAkB,cAAc,yBAAyB,GAAG;AAC7H;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,+CAA+C,OAAO,QAAQ,OAAO,SAAS,MAAM,SAAS,sBAAsB;AACnH;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,+CAA+C,OAAO,QAAQ,IAAI,gBAAgB,OAAO,kBAAkB,KAAK,yBAAyB,GAAG;AAC5I;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,8CAA8C,qBAAqB,yBAAyB,6BAA6B,yBAAyB,+BAA+B,yBAAyB,6BAA6B,uBAAuB,QAAQ;AACtQ;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,gDAAgD,gBAAgB,QAAQ,IAAI;AAC5E;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,kDAAkD,OAAO,QAAQ,IAAI,kBAAkB,WAAW,yBAAyB,GAAG;AAC9H;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,+CAA+C,OAAO,QAAQ,IAAI,kBAAkB,OAAO,gCAAgC,GAAG;AAC9H;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA8C,OAAO,mBAAmB,IAAI,gBAAgB,oBAAoB,QAAQ,2BAA2B,KAAK,GAAG;AAC3J;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,8CAA8C,OAAO,mBAAmB,IAAI,yBAAyB,oBAAoB,aAAa,2CAA2C,KAAK,GAAG;AACzL;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,mDAAmD,SAAS,QAAQ,IAAI,oBAAoB,YAAY,QAAQ,KAAK;AACrH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mGAAmG;AACnG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,WAAW;AAC3D,kDAAkD,YAAY,UAAU,sCAAsC;AAC9G,uDAAuD,mCAAmC;AAC1F,6CAA6C,2CAA2C,GAAG,WAAW;AACtG,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,WAAW;AAC3D,kDAAkD,YAAY,UAAU,mDAAmD,IAAI,yDAAyD;AACxL,6BAA6B;AAC7B;AACA;AACA;AACA;AACA,iGAAiG;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,YAAY;AAC5D,6DAA6D,kCAAkC;AAC/F,gEAAgE,YAAY;AAC5E,6CAA6C,0CAA0C,GAAG,YAAY;AACtG,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,WAAW;AACvD,8CAA8C,YAAY;AAC1D;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,WAAW;AACvD,sDAAsD,YAAY;AAClE,uCAAuC,YAAY;AACnD,yBAAyB;AACzB;AACA;AACA;AACA,yBAAyB;AACzB,aAAa;AACb,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,cAAc,oBAAoB,2BAA2B,KAAK,kBAAkB;AAC5G;AACA,SAAS;AACT;AACA;AACA;AACA,wBAAwB,cAAc,gBAAgB,uBAAuB,qEAAqE,OAAO,KAAK,GAAG;AACjK;AACA,SAAS;AACT;AACA;AACA;AACA,wBAAwB,cAAc,sBAAsB,4GAA4G,KAAK,GAAG;AAChL;AACA,SAAS;AACT;AACA;AACA;AACA,wBAAwB,cAAc,eAAe,6BAA6B,WAAW,sBAAsB,yCAAyC,UAAU,sBAAsB,yCAAyC,WAAW,sBAAsB,kDAAkD,KAAK,GAAG;AAChU;AACA,SAAS;AACT;AACA;AACA;AACA,wBAAwB,cAAc,gBAAgB,uBAAuB,qGAAqG,OAAO,KAAK,GAAG;AACjM;AACA,SAAS;AACT;AACA;AACA;AACA,wBAAwB,2BAA2B,yBAAyB,kBAAkB,mEAAmE,kBAAkB,wBAAwB,oBAAoB,2CAA2C,WAAW,SAAS,OAAO,KAAK,GAAG;AAC7S;AACA,SAAS;AACT;AACA;AACA;AACA,wBAAwB,cAAc,gBAAgB,uBAAuB,sGAAsG,OAAO,KAAK,GAAG;AAClM;AACA,SAAS;AACT;AACA;AACA;AACA,wBAAwB,cAAc,mBAAmB,uBAAuB,6DAA6D,OAAO,KAAK,GAAG;AAC5J;AACA;AACA;AACA,YAAY,uDAAK,UAAU,mCAAmC,uDAAK,CAAC,0CAAI,IAAI,6BAA6B,uDAAK,UAAU,2DAA2D,uDAAK,UAAU,qDAAqD,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,qDAAQ,IAAI,oCAAoC,GAAG,sDAAI,WAAW,yGAAyG,mBAAmB,sDAAI,UAAU,8FAA8F,KAAK,GAAG,uDAAK,UAAU,2EAA2E,uDAAK,UAAU,oEAAoE,sDAAI,CAAC,qDAAK,IAAI,sBAAsB,GAAG,sDAAI,WAAW,4DAA4D,IAAI,MAAM,uDAAK,UAAU,kEAAkE,sDAAI,CAAC,qDAAW,IAAI,sBAAsB,GAAG,sDAAI,WAAW,4DAA4D,IAAI,8CAA8C,uDAAK,CAAC,6CAAK,IAAI,sBAAsB,oCAAoC,wDAAwD,KAAK,IAAI,GAAG,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,8CAAM,IAAI,sFAAsF,GAAG,uDAAK,CAAC,8CAAM,IAAI,sJAAsJ,GAAG,uDAAK,CAAC,8CAAM,IAAI,kJAAkJ,sDAAI,CAAC,qDAAa,IAAI,sBAAsB,GAAG,uDAAK,WAAW,+DAA+D,IAAI,IAAI,IAAI,+CAA+C,sDAAI,UAAU,mHAAmH,uDAAK,UAAU,oDAAoD,sDAAI,CAAC,qDAAW,IAAI,wDAAwD,GAAG,uDAAK,UAAU,WAAW,sDAAI,QAAQ,4GAA4G,GAAG,sDAAI,QAAQ,sFAAsF,IAAI,IAAI,GAAG,KAAK,qDAAqD,uDAAK,CAAC,0CAAI,IAAI,6BAA6B,uDAAK,UAAU,0DAA0D,sDAAI,CAAC,qDAAG,IAAI,sCAAsC,GAAG,sDAAI,SAAS,4GAA4G,GAAG,sDAAI,CAAC,6CAAK,IAAI,sDAAsD,IAAI,GAAG,sDAAI,UAAU,0FAA0F,sDAAI,UAAU,0EAA0E,uDAAK,UAAU,0DAA0D,uDAAK,UAAU,gCAAgC,uDAAK,UAAU,0DAA0D,sDAAI,SAAS,8FAA8F,GAAG,sDAAI,CAAC,6CAAK,IAAI;AAC/4G,kLAAkL,IAAI,GAAG,sDAAI,QAAQ,gGAAgG,GAAG,uDAAK,QAAQ,8GAA8G,sDAAsD,uDAAK,QAAQ,uJAAuJ,KAAK,mCAAmC,sDAAI,CAAC,8CAAM,IAAI,2IAA2I,KAAK,GAAG,YAAY,IAAI,IAAI,uDAAK,CAAC,0CAAI,IAAI,6BAA6B,uDAAK,UAAU,0DAA0D,sDAAI,CAAC,qDAAQ,IAAI,oCAAoC,GAAG,sDAAI,SAAS,0GAA0G,IAAI,GAAG,sDAAI,UAAU,6GAA6G,uDAAK,UAAU,wNAAwN,uDAAK,UAAU,gEAAgE,sDAAI,SAAS,yFAAyF,GAAG,sDAAI,CAAC,6CAAK,IAAI,uEAAuE,IAAI,GAAG,sDAAI,QAAQ,uFAAuF,IAAI,oBAAoB,GAAG,sDAAI,UAAU,2EAA2E,uDAAK,UAAU,oDAAoD,sDAAI,CAAC,qDAAS,IAAI,yDAAyD,GAAG,uDAAK,UAAU,WAAW,sDAAI,QAAQ,yGAAyG,GAAG,sDAAI,QAAQ,0QAA0Q,IAAI,IAAI,GAAG,IAAI,GAAG,sDAAI,CAAC,0CAAI,IAAI,wCAAwC,sDAAI,UAAU,6BAA6B,sDAAI,CAAC,4DAAM,IAAI;AAC5oF,uCAAuC,gBAAgB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA,uDAAuD,eAAe;AACtE;AACA;AACA;AACA,6BAA6B;AAC7B,2BAA2B,GAAG,GAAG,qCAAqC,sDAAI,CAAC,qEAAc,IAAI,wJAAwJ,4BAA4B,sDAAI,CAAC,uEAAe,IAAI,uGAAuG,KAAK;AACrZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACthB+D;AACtB;AACkB;AACR;AACU;AACvB;AACJ;AACQ;AACF;AACc;AACR;AAC/B;AACf,YAAY,gJAAgJ,EAAE,4DAAa;AAC3K,YAAY,cAAc,EAAE,sEAAkB;AAC9C,iCAAiC,0EAAoB;AACrD;AACA,oDAAoD,qDAAc;AAClE,oDAAoD,qDAAc;AAClE,IAAI,gDAAS;AACb;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,UAAU,mCAAmC;AAC7C,UAAU,mCAAmC;AAC7C,UAAU,mCAAmC;AAC7C,UAAU,qCAAqC;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,uDAAK,UAAU,mCAAmC,uDAAK,CAAC,0CAAI,IAAI,6BAA6B,uDAAK,UAAU,gEAAgE,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,oDAAM,IAAI,oCAAoC,GAAG,sDAAI,SAAS,6FAA6F,IAAI,GAAG,uDAAK,UAAU,qDAAqD,uDAAK,CAAC,8CAAM,IAAI,yIAAyI,sDAAI,CAAC,oDAAI,IAAI,sBAAsB,GAAG,sDAAI,WAAW,wBAAwB,IAAI,GAAG,uDAAK,CAAC,8CAAM,IAAI;AACpuB;AACA,yCAAyC,uDAAuD,sDAAI,CAAC,oDAAO,IAAI,sBAAsB,GAAG,sDAAI,WAAW,qBAAqB,IAAI,IAAI,IAAI,GAAG,uDAAK,UAAU,oEAAoE,uDAAK,UAAU,WAAW,sDAAI,YAAY,wGAAwG,uBAAuB,uDAAK,UAAU,gHAAgH,sDAAI,CAAC,iDAAO,IAAI,YAAY,GAAG,sDAAI,WAAW,oEAAoE,IAAI,MAAM,sDAAI,CAAC,mDAAQ,IAAI,kJAAkJ,KAAK,GAAG,uDAAK,UAAU,WAAW,sDAAI,YAAY,sGAAsG,GAAG,uDAAK,UAAU,yDAAyD,uDAAK,YAAY,qDAAqD,sDAAI,YAAY,wJAAwJ,GAAG,sDAAI,WAAW,0CAA0C,IAAI,GAAG,uDAAK,YAAY,qDAAqD,sDAAI,YAAY,+IAA+I,GAAG,sDAAI,WAAW,uCAAuC,IAAI,IAAI,IAAI,GAAG,uDAAK,UAAU,WAAW,sDAAI,YAAY,4GAA4G,GAAG,sDAAI,CAAC,mDAAQ,IAAI,uGAAuG,IAAI,IAAI,GAAG,uDAAK,UAAU,2DAA2D,uDAAK,UAAU,qDAAqD,uDAAK,CAAC,8CAAM,IAAI,0IAA0I,sDAAI,CAAC,iDAAO,IAAI,YAAY,MAAM,sDAAI,CAAC,oDAAI,IAAI,sBAAsB,IAAI,sDAAI,WAAW,0DAA0D,IAAI,GAAG,sDAAI,CAAC,8CAAM,IAAI,6FAA6F,IAAI,mBAAmB,uDAAK,UAAU,uEAAuE,KAAK,IAAI,8CAA8C,sDAAI,CAAC,4DAAkB,IAAI,MAAM,sDAAI,CAAC,wDAAc,IAAI,OAAO,sDAAI,CAAC,0CAAI,IAAI,6BAA6B,uDAAK,UAAU,sEAAsE,sDAAI,CAAC,oDAAM,IAAI,gDAAgD,GAAG,sDAAI,SAAS,6EAA6E,GAAG,sDAAI,QAAQ,iGAAiG,IAAI,GAAG,KAAK;AAC1lG;AACA;AACoE;;;;;;;;;;;;;;;;;;;;;;;ACpEL;AACrC;AACO;AACK;AACF;AACM;AACoB;AAC9D;AACA,MAAM,kCAAkC;AACxC,MAAM,0CAA0C;AAChD,MAAM,sCAAsC;AAC5C,MAAM,8CAA8C;AACpD,MAAM,kCAAkC;AACxC,MAAM,0CAA0C;AAChD,MAAM,gCAAgC;AACtC,MAAM,sCAAsC;AAC5C,MAAM,iCAAiC;AACvC;AACA;AACA,MAAM,4BAA4B;AAClC,MAAM,0BAA0B;AAChC;AACe,0BAA0B,0DAA0D;AACnG;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,uDAAK,UAAU,wCAAwC,sDAAI,CAAC,4CAAK,IAAI;AACzF;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,yBAAyB,wBAAwB,GAAG,sDAAI,CAAC,4CAAK,IAAI;AAClE;AACA;AACA;AACA;AACA,6BAA6B;AAC7B,yBAAyB,wBAAwB,IAAI;AACrD;AACA,gBAAgB,sDAAI,CAAC,sEAAiB,IAAI,0EAA0E,OAAO,kIAAkI;AAC7P;AACA,YAAY,uDAAK,UAAU,uJAAuJ,sDAAI,UAAU,sCAAsC,sDAAI,CAAC,kDAAQ,IAAI,qGAAqG,wBAAwB,sBAAsB,GAAG,IAAI,sDAAI,UAAU,2CAA2C,sDAAI,CAAC,sEAAiB,IAAI,oEAAoE,cAAc,2DAA2D,GAAG,GAAG,sDAAI,UAAU,2CAA2C,sDAAI,CAAC,kDAAQ,IAAI,6EAA6E,iBAAiB,GAAG,GAAG,GAAG,sDAAI,UAAU,mDAAmD,GAAG,sDAAI,UAAU,sCAAsC,sDAAI,CAAC,8CAAM,IAAI,kJAAkJ,sDAAI,CAAC,oDAAC,IAAI,sBAAsB,GAAG,GAAG,IAAI;AAC3mC;;;;;;;;;;;;;;;;;;;;;;;;;;AC/C+D;AACrC;AACuB;AACE;AACS;AACtB;AACJ;AACQ;AACoB;AAChB;AAC/B;AACf,YAAY,iGAAiG,EAAE,4DAAa;AAC5H,YAAY,qBAAqB,EAAE,wEAAe;AAClD;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,kBAAkB,YAAY,GAAG,WAAW;AAC5C,KAAK;AACL;AACA,UAAU,kCAAkC;AAC5C,UAAU,oCAAoC;AAC9C;AACA;AACA,gBAAgB,sDAAI,CAAC,0CAAI,IAAI,4BAA4B,uDAAK,UAAU,0DAA0D,sDAAI,UAAU,2EAA2E,GAAG,sDAAI,WAAW,yFAAyF,IAAI,GAAG;AAC7U;AACA,YAAY,uDAAK,UAAU,mCAAmC,uDAAK,CAAC,0CAAI,IAAI,6BAA6B,uDAAK,UAAU,gEAAgE,sDAAI,SAAS,gGAAgG,GAAG,uDAAK,CAAC,8CAAM,IAAI,wFAAwF,sDAAI,CAAC,oDAAI,IAAI,sBAAsB,GAAG,sDAAI,WAAW,2BAA2B,IAAI,IAAI,0CAA0C,uDAAK,UAAU,2EAA2E,sDAAI,QAAQ,wFAAwF,GAAG,sDAAI,QAAQ,4FAA4F,IAAI,MAAM,sDAAI,UAAU,oFAAoF,sDAAI,CAAC,uDAAc,IAAI,sNAAsN,mBAAmB,KAAK,GAAG,uDAAK,CAAC,0CAAI,IAAI,6BAA6B,uDAAK,UAAU,gEAAgE,uDAAK,SAAS,yGAAyG,sDAAI,CAAC,oDAAW,IAAI,sBAAsB,GAAG,sDAAI,WAAW,wBAAwB,IAAI,6BAA6B,uDAAK,CAAC,8CAAM,IAAI,+GAA+G,sDAAI,CAAC,oDAAI,IAAI,gCAAgC,GAAG,sDAAI,WAAW,wBAAwB,IAAI,KAAK,6BAA6B,sDAAI,UAAU,0EAA0E,sDAAI,QAAQ,+EAA+E,GAAG,MAAM,uDAAK,UAAU,gGAAgG,sDAAI,UAAU,+BAA+B,sDAAI,CAAC,sEAAiB,IAAI,uMAAuM,GAAG,GAAG,sDAAI,UAAU,6BAA6B,sDAAI,CAAC,kDAAQ,IAAI,mIAAmI,GAAG,IAAI,KAAK,IAAI;AAC/hF;;;;;;;;;;;;;;;;;ACxC4C;AACrC;AACP,gDAAgD,+CAAQ;AACxD,IAAI,gDAAS;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,KAAK;AACL;AACA;;;;;;;;;;;;;;;;ACZuD;AAChD;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,gEAAoB;AAC7D;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,OAAO,GAAG,UAAU;AAC7D,uCAAuC,KAAK,GAAG,UAAU;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,SAAS,GAAG,aAAa;AACxE,+CAA+C,SAAS,GAAG,aAAa;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,MAAM,GAAG,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,MAAM,GAAG,MAAM,GAAG,MAAM;AACpD;AACA;AACA;AACA;AACA,yCAAyC,gEAAoB;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,MAAM;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,gEAAoB;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,WAAW;AAC1E;AACA;AACA;AACA;AACA;AACA,kBAAkB,YAAY,EAAE,+BAA+B,wBAAwB,QAAQ;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,MAAM;AAC5C;AACA;AACA;AACA;AACA,sCAAsC,MAAM;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://es-client/./src/renderer/components/Data/FieldAutocomplete.tsx", "webpack://es-client/./src/renderer/components/Query/DSLQueryEditor.tsx", "webpack://es-client/./src/renderer/components/Query/QueryBuilder.tsx", "webpack://es-client/./src/renderer/components/Query/QueryCondition.tsx", "webpack://es-client/./src/renderer/components/Query/VisualQueryBuilder.tsx", "webpack://es-client/./src/renderer/hooks/useDebounce.ts", "webpack://es-client/./src/renderer/services/FieldMappingService.ts"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { Search, Hash, Calendar, Type, ToggleLeft, List, Zap, TrendingUp, Star } from 'lucide-react';\nimport { FieldMappingService } from '../../services/FieldMappingService';\nimport { IntelligentQueryAssistant } from '../../services/IntelligentQueryAssistant';\nimport { useDebounce } from '../../hooks/useDebounce';\nexport const FieldAutocomplete = ({ index, value, onChange, placeholder = '输入字段名...', className = '', showValueSuggestions = false, type = 'field', fieldName, onFieldSelect }) => {\n    const [suggestions, setSuggestions] = useState([]);\n    const [isOpen, setIsOpen] = useState(false);\n    const [isLoading, setIsLoading] = useState(false);\n    const [selectedIndex, setSelectedIndex] = useState(-1);\n    const [showAdvanced, setShowAdvanced] = useState(false);\n    const inputRef = useRef(null);\n    const dropdownRef = useRef(null);\n    const service = FieldMappingService.getInstance();\n    const intelligentAssistant = IntelligentQueryAssistant.getInstance();\n    const debouncedValue = useDebounce(value, 200); // Reduced debounce for faster response\n    useEffect(() => {\n        if (debouncedValue.trim() && index) {\n            loadSuggestions(debouncedValue);\n        }\n        else {\n            setSuggestions([]);\n            setIsOpen(false);\n        }\n    }, [debouncedValue, index, type, fieldName]);\n    useEffect(() => {\n        const handleClickOutside = (event) => {\n            if (dropdownRef.current &&\n                !dropdownRef.current.contains(event.target) &&\n                inputRef.current &&\n                !inputRef.current.contains(event.target)) {\n                setIsOpen(false);\n            }\n        };\n        document.addEventListener('mousedown', handleClickOutside);\n        return () => document.removeEventListener('mousedown', handleClickOutside);\n    }, []);\n    const loadSuggestions = async (query) => {\n        if (!index)\n            return;\n        setIsLoading(true);\n        try {\n            let newSuggestions = [];\n            if (type === 'field') {\n                // Use intelligent assistant for enhanced field suggestions\n                const intelligentSuggestions = await intelligentAssistant.getFieldSuggestions(index, query);\n                // Convert to FieldSuggestion format and add enhanced metadata\n                newSuggestions = intelligentSuggestions.map(suggestion => ({\n                    label: suggestion.name,\n                    type: suggestion.type,\n                    description: suggestion.description || `${suggestion.type} field`,\n                    score: suggestion.popularity,\n                    popularity: suggestion.popularity,\n                    isNested: suggestion.isNested,\n                    parentPath: suggestion.parentPath,\n                    examples: suggestion.examples\n                }));\n                // Also get traditional suggestions as fallback\n                const fallbackSuggestions = await service.getFieldSuggestions(index, query);\n                // Merge and deduplicate suggestions\n                const mergedSuggestions = new Map();\n                [...newSuggestions, ...fallbackSuggestions].forEach(suggestion => {\n                    const key = 'label' in suggestion ? suggestion.label : suggestion.value;\n                    if (!mergedSuggestions.has(key) ||\n                        (mergedSuggestions.get(key).score || 0) < (suggestion.score || 0)) {\n                        mergedSuggestions.set(key, suggestion);\n                    }\n                });\n                newSuggestions = Array.from(mergedSuggestions.values());\n            }\n            else if (type === 'value' && fieldName) {\n                // Use intelligent assistant for enhanced value suggestions\n                const intelligentValueSuggestions = await intelligentAssistant.getValueSuggestions(index, fieldName, query);\n                // Convert to ValueSuggestion format\n                newSuggestions = intelligentValueSuggestions.map(suggestion => ({\n                    value: suggestion.value,\n                    count: Math.round(suggestion.frequency * 1000), // Convert frequency to count\n                    score: suggestion.frequency,\n                    type: suggestion.type,\n                    isExact: suggestion.isExact,\n                    description: suggestion.description\n                }));\n                // Also get traditional suggestions as fallback\n                const fallbackSuggestions = await service.getValueSuggestions(index, fieldName, query);\n                // Merge suggestions\n                const mergedSuggestions = new Map();\n                [...newSuggestions, ...fallbackSuggestions].forEach(suggestion => {\n                    const key = String(suggestion.value);\n                    if (!mergedSuggestions.has(key) ||\n                        (mergedSuggestions.get(key).score || 0) < (suggestion.score || 0)) {\n                        mergedSuggestions.set(key, suggestion);\n                    }\n                });\n                newSuggestions = Array.from(mergedSuggestions.values());\n            }\n            // Sort suggestions by score/relevance\n            newSuggestions.sort((a, b) => (b.score || 0) - (a.score || 0));\n            setSuggestions(newSuggestions.slice(0, 15)); // Limit to top 15 suggestions\n            setIsOpen(newSuggestions.length > 0);\n            setSelectedIndex(-1);\n        }\n        catch (error) {\n            console.error('Failed to load suggestions:', error);\n            setSuggestions([]);\n            setIsOpen(false);\n        }\n        finally {\n            setIsLoading(false);\n        }\n    };\n    const handleInputChange = (e) => {\n        onChange(e.target.value);\n        setSelectedIndex(-1);\n    };\n    const handleKeyDown = (e) => {\n        if (!isOpen || suggestions.length === 0)\n            return;\n        switch (e.key) {\n            case 'ArrowDown':\n                e.preventDefault();\n                setSelectedIndex(prev => (prev + 1) % suggestions.length);\n                break;\n            case 'ArrowUp':\n                e.preventDefault();\n                setSelectedIndex(prev => (prev - 1 + suggestions.length) % suggestions.length);\n                break;\n            case 'Enter':\n                e.preventDefault();\n                if (selectedIndex >= 0 && selectedIndex < suggestions.length) {\n                    selectSuggestion(suggestions[selectedIndex]);\n                }\n                break;\n            case 'Escape':\n                setIsOpen(false);\n                break;\n        }\n    };\n    const selectSuggestion = (suggestion) => {\n        if (type === 'field') {\n            const fieldSuggestion = suggestion;\n            onChange(fieldSuggestion.label);\n            onFieldSelect?.(fieldSuggestion.label);\n        }\n        else {\n            const valueSuggestion = suggestion;\n            onChange(valueSuggestion.value);\n        }\n        setIsOpen(false);\n        setSelectedIndex(-1);\n    };\n    const getFieldIcon = (type) => {\n        switch (type) {\n            case 'text':\n                return _jsx(Type, { className: \"w-4 h-4\" });\n            case 'keyword':\n                return _jsx(Hash, { className: \"w-4 h-4\" });\n            case 'date':\n                return _jsx(Calendar, { className: \"w-4 h-4\" });\n            case 'boolean':\n                return _jsx(ToggleLeft, { className: \"w-4 h-4\" });\n            case 'long':\n            case 'integer':\n            case 'double':\n            case 'float':\n                return _jsx(Hash, { className: \"w-4 h-4\" });\n            default:\n                return _jsx(List, { className: \"w-4 h-4\" });\n        }\n    };\n    const getTypeColor = (type) => {\n        switch (type) {\n            case 'text':\n                return 'text-blue-600 bg-blue-50';\n            case 'keyword':\n                return 'text-green-600 bg-green-50';\n            case 'date':\n                return 'text-purple-600 bg-purple-50';\n            case 'boolean':\n                return 'text-orange-600 bg-orange-50';\n            case 'long':\n            case 'integer':\n            case 'double':\n            case 'float':\n                return 'text-red-600 bg-red-50';\n            default:\n                return 'text-gray-600 bg-gray-50';\n        }\n    };\n    const renderFieldSuggestion = (suggestion) => (_jsxs(\"div\", { className: \"flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-3 flex-1 min-w-0\", children: [_jsx(\"div\", { className: \"flex-shrink-0\", children: getFieldIcon(suggestion.type) }), _jsxs(\"div\", { className: \"flex-1 min-w-0\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-2\", children: [_jsx(\"span\", { className: \"font-medium text-sm text-gray-900 dark:text-white truncate\", children: suggestion.label }), suggestion.popularity > 0.7 && (_jsx(Star, { className: \"w-3 h-3 text-yellow-500 flex-shrink-0\", title: \"Popular field\" })), suggestion.isNested && (_jsx(\"span\", { className: \"text-xs bg-blue-100 text-blue-700 px-1 rounded\", children: \"nested\" }))] }), suggestion.description && (_jsx(\"p\", { className: \"text-xs text-gray-500 dark:text-gray-400 truncate mt-0.5\", children: suggestion.description })), suggestion.parentPath && (_jsxs(\"p\", { className: \"text-xs text-gray-400 dark:text-gray-500 truncate\", children: [\"in \", suggestion.parentPath] }))] })] }), _jsxs(\"div\", { className: \"flex items-center space-x-2 flex-shrink-0\", children: [suggestion.popularity > 0.5 && (_jsxs(\"div\", { className: \"flex items-center space-x-1\", children: [_jsx(TrendingUp, { className: \"w-3 h-3 text-green-500\" }), _jsxs(\"span\", { className: \"text-xs text-green-600\", children: [Math.round(suggestion.popularity * 100), \"%\"] })] })), _jsx(\"span\", { className: `text-xs px-2 py-1 rounded-full ${getTypeColor(suggestion.type)}`, children: suggestion.type })] })] }));\n    const renderValueSuggestion = (suggestion) => (_jsxs(\"div\", { className: \"flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors\", children: [_jsx(\"div\", { className: \"flex items-center space-x-2 flex-1 min-w-0\", children: _jsxs(\"div\", { className: \"flex-1 min-w-0\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-2\", children: [_jsx(\"span\", { className: \"text-sm text-gray-900 dark:text-white font-medium truncate\", children: String(suggestion.value) }), suggestion.isExact && (_jsx(\"span\", { className: \"text-xs bg-green-100 text-green-700 px-1 rounded\", children: \"exact\" }))] }), suggestion.description && (_jsx(\"p\", { className: \"text-xs text-gray-500 dark:text-gray-400 truncate mt-0.5\", children: suggestion.description }))] }) }), _jsx(\"div\", { className: \"flex items-center space-x-2 flex-shrink-0\", children: _jsxs(\"div\", { className: \"flex items-center space-x-1\", children: [_jsxs(\"span\", { className: \"text-xs text-gray-500 dark:text-gray-400\", children: [suggestion.count.toLocaleString(), \" \\u6761\"] }), suggestion.count > 100 && (_jsx(Zap, { className: \"w-3 h-3 text-orange-500\", title: \"High frequency value\" }))] }) })] }));\n    const renderSuggestion = (suggestion, index) => {\n        const isSelected = index === selectedIndex;\n        return (_jsx(\"div\", { className: `${isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : ''} border-b border-gray-100 dark:border-gray-700 last:border-b-0`, onClick: () => selectSuggestion(suggestion), children: type === 'field'\n                ? renderFieldSuggestion(suggestion)\n                : renderValueSuggestion(suggestion) }, index));\n    };\n    return (_jsxs(\"div\", { className: \"relative\", ref: dropdownRef, children: [_jsxs(\"div\", { className: \"relative\", children: [_jsx(\"input\", { ref: inputRef, type: \"text\", value: value, onChange: handleInputChange, onKeyDown: handleKeyDown, onFocus: () => {\n                            if (suggestions.length > 0) {\n                                setIsOpen(true);\n                            }\n                        }, placeholder: placeholder, className: `w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white ${className}` }), isLoading && (_jsx(\"div\", { className: \"absolute right-3 top-1/2 transform -translate-y-1/2\", children: _jsx(\"div\", { className: \"animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full\" }) })), !isLoading && !value && (_jsx(\"div\", { className: \"absolute right-3 top-1/2 transform -translate-y-1/2\", children: _jsx(Search, { className: \"w-4 h-4 text-gray-400\" }) }))] }), isOpen && suggestions.length > 0 && (_jsxs(\"div\", { className: \"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-80 overflow-y-auto\", children: [_jsx(\"div\", { className: \"px-3 py-2 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600\", children: _jsxs(\"div\", { className: \"flex items-center justify-between\", children: [_jsxs(\"span\", { className: \"text-xs font-medium text-gray-600 dark:text-gray-300\", children: [type === 'field' ? '字段建议' : '值建议', \" (\", suggestions.length, \")\"] }), type === 'field' && (_jsx(\"button\", { onClick: () => setShowAdvanced(!showAdvanced), className: \"text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400\", children: showAdvanced ? '简化视图' : '详细视图' }))] }) }), _jsx(\"div\", { className: \"py-1\", children: suggestions.map((suggestion, index) => renderSuggestion(suggestion, index)) }), _jsx(\"div\", { className: \"px-3 py-2 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600\", children: _jsx(\"p\", { className: \"text-xs text-gray-500 dark:text-gray-400\", children: type === 'field'\n                                ? '使用 ↑↓ 键导航，Enter 选择，Esc 关闭'\n                                : '显示最常用的值，按使用频率排序' }) })] }))] }));\n};\n// 自定义Hook用于字段映射\nexport const useFieldMapping = (index) => {\n    const [mapping, setMapping] = useState(null);\n    const [isLoading, setIsLoading] = useState(false);\n    const service = FieldMappingService.getInstance();\n    const loadMapping = useCallback(async () => {\n        if (!index) {\n            setMapping(null);\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const result = await service.getIndexMapping(index);\n            setMapping(result);\n        }\n        catch (error) {\n            console.error('Failed to load field mapping:', error);\n            setMapping(null);\n        }\n        finally {\n            setIsLoading(false);\n        }\n    }, [index, service]);\n    useEffect(() => {\n        loadMapping();\n    }, [loadMapping]);\n    const refreshMapping = useCallback(() => {\n        if (index) {\n            service.refreshMapping(index);\n            loadMapping();\n        }\n    }, [index, service, loadMapping]);\n    return {\n        mapping,\n        isLoading,\n        refreshMapping\n    };\n};\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { useRef, useEffect, useState } from 'react';\nimport Editor from '@monaco-editor/react';\nimport { useQueryStore } from '../../stores/query';\nimport { useThemeStore } from '../../stores/theme';\nimport { useFieldMapping } from '../Data/FieldAutocomplete';\nimport { FieldMappingService } from '../../services/FieldMappingService';\nimport { IntelligentQueryAssistant } from '../../services/IntelligentQueryAssistant';\nimport { Card } from '../UI/Card';\nimport { Button } from '../UI/Button';\nimport { Badge } from '../UI/Badge';\nimport { DSLCodeDisplay } from '../Learning/DSLCodeDisplay';\nimport { LearningSupport } from '../Learning/LearningSupport';\nimport { Check, AlertCircle, Zap, Lightbulb, Sparkles, BookOpen, GraduationCap } from 'lucide-react';\nexport default function DSLQueryEditor() {\n    const { dslQuery, setDslQuery, selectedIndex } = useQueryStore();\n    const { theme } = useThemeStore();\n    const editorRef = useRef(null);\n    const { mapping } = useFieldMapping(selectedIndex || '');\n    const fieldService = FieldMappingService.getInstance();\n    const intelligentAssistant = IntelligentQueryAssistant.getInstance();\n    const [optimizations, setOptimizations] = useState([]);\n    const [isAnalyzing, setIsAnalyzing] = useState(false);\n    const [showOptimizations, setShowOptimizations] = useState(true);\n    const [queryComplexity, setQueryComplexity] = useState('simple');\n    const [showLearningSupport, setShowLearningSupport] = useState(false);\n    const [showDSLDisplay, setShowDSLDisplay] = useState(true);\n    const [parsedQuery, setParsedQuery] = useState(null);\n    // Analyze query when it changes\n    useEffect(() => {\n        const analyzeQuery = async () => {\n            if (!dslQuery.trim() || !selectedIndex) {\n                setParsedQuery(null);\n                return;\n            }\n            try {\n                const parsed = JSON.parse(dslQuery);\n                setParsedQuery(parsed);\n                setIsAnalyzing(true);\n                const analysis = await intelligentAssistant.analyzeQuery(parsed, selectedIndex);\n                if (analysis) {\n                    setOptimizations(analysis.optimizations);\n                    setQueryComplexity(analysis.complexity);\n                }\n            }\n            catch (error) {\n                // Invalid JSON, skip analysis\n                setOptimizations([]);\n                setParsedQuery(null);\n            }\n            finally {\n                setIsAnalyzing(false);\n            }\n        };\n        const timeoutId = setTimeout(analyzeQuery, 1000); // Debounce analysis\n        return () => clearTimeout(timeoutId);\n    }, [dslQuery, selectedIndex]);\n    const handleEditorDidMount = (editor, monaco) => {\n        editorRef.current = editor;\n        // Configure enhanced JSON schema for Elasticsearch query DSL\n        monaco.languages.json.jsonDefaults.setDiagnosticsOptions({\n            validate: true,\n            schemas: [\n                {\n                    uri: 'http://elasticsearch.org/query-dsl.json',\n                    fileMatch: ['*'],\n                    schema: {\n                        type: 'object',\n                        properties: {\n                            query: {\n                                type: 'object',\n                                properties: {\n                                    match_all: {\n                                        type: 'object',\n                                        properties: {\n                                            boost: { type: 'number' }\n                                        }\n                                    },\n                                    match: {\n                                        type: 'object',\n                                        patternProperties: {\n                                            '.*': {\n                                                oneOf: [\n                                                    { type: 'string' },\n                                                    {\n                                                        type: 'object',\n                                                        properties: {\n                                                            query: { type: 'string' },\n                                                            operator: { enum: ['and', 'or'] },\n                                                            fuzziness: { type: 'string' },\n                                                            boost: { type: 'number' }\n                                                        }\n                                                    }\n                                                ]\n                                            }\n                                        }\n                                    },\n                                    term: { type: 'object' },\n                                    terms: { type: 'object' },\n                                    range: {\n                                        type: 'object',\n                                        patternProperties: {\n                                            '.*': {\n                                                type: 'object',\n                                                properties: {\n                                                    gte: {},\n                                                    gt: {},\n                                                    lte: {},\n                                                    lt: {},\n                                                    boost: { type: 'number' }\n                                                }\n                                            }\n                                        }\n                                    },\n                                    exists: {\n                                        type: 'object',\n                                        properties: {\n                                            field: { type: 'string' }\n                                        }\n                                    },\n                                    bool: {\n                                        type: 'object',\n                                        properties: {\n                                            must: { type: 'array' },\n                                            must_not: { type: 'array' },\n                                            should: { type: 'array' },\n                                            filter: { type: 'array' },\n                                            minimum_should_match: { type: 'number' },\n                                            boost: { type: 'number' }\n                                        },\n                                    },\n                                    wildcard: { type: 'object' },\n                                    regexp: { type: 'object' },\n                                    fuzzy: { type: 'object' },\n                                    prefix: { type: 'object' },\n                                    query_string: {\n                                        type: 'object',\n                                        properties: {\n                                            query: { type: 'string' },\n                                            default_field: { type: 'string' },\n                                            fields: { type: 'array', items: { type: 'string' } },\n                                            default_operator: { enum: ['AND', 'OR'] }\n                                        }\n                                    },\n                                    simple_query_string: { type: 'object' },\n                                    multi_match: {\n                                        type: 'object',\n                                        properties: {\n                                            query: { type: 'string' },\n                                            fields: { type: 'array', items: { type: 'string' } },\n                                            type: { enum: ['best_fields', 'most_fields', 'cross_fields', 'phrase', 'phrase_prefix'] }\n                                        }\n                                    }\n                                },\n                            },\n                            sort: {\n                                type: 'array',\n                                items: {\n                                    oneOf: [\n                                        { type: 'string' },\n                                        {\n                                            type: 'object',\n                                            patternProperties: {\n                                                '.*': {\n                                                    oneOf: [\n                                                        { enum: ['asc', 'desc'] },\n                                                        {\n                                                            type: 'object',\n                                                            properties: {\n                                                                order: { enum: ['asc', 'desc'] },\n                                                                missing: { enum: ['_first', '_last'] }\n                                                            }\n                                                        }\n                                                    ]\n                                                }\n                                            }\n                                        }\n                                    ]\n                                },\n                            },\n                            size: { type: 'number', minimum: 0, maximum: 10000 },\n                            from: { type: 'number', minimum: 0 },\n                            _source: {\n                                oneOf: [\n                                    { type: 'boolean' },\n                                    { type: 'string' },\n                                    { type: 'array', items: { type: 'string' } },\n                                    {\n                                        type: 'object',\n                                        properties: {\n                                            includes: { type: 'array', items: { type: 'string' } },\n                                            excludes: { type: 'array', items: { type: 'string' } }\n                                        }\n                                    }\n                                ],\n                            },\n                            highlight: {\n                                type: 'object',\n                                properties: {\n                                    fields: { type: 'object' },\n                                    pre_tags: { type: 'array', items: { type: 'string' } },\n                                    post_tags: { type: 'array', items: { type: 'string' } }\n                                }\n                            },\n                            aggs: { type: 'object' },\n                            aggregations: { type: 'object' },\n                            timeout: { type: 'string' },\n                            track_total_hits: { type: 'boolean' }\n                        },\n                    },\n                },\n            ],\n        });\n        // Add enhanced custom completions for Elasticsearch queries\n        monaco.languages.registerCompletionItemProvider('json', {\n            provideCompletionItems: async (model, position) => {\n                const lineContent = model.getLineContent(position.lineNumber);\n                const lineUntilPosition = lineContent.substring(0, position.column - 1);\n                const suggestions = [\n                    // Basic query types\n                    {\n                        label: 'match_all',\n                        kind: monaco.languages.CompletionItemKind.Snippet,\n                        insertText: '\"match_all\": {\\n  \"boost\": 1.0\\n}',\n                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n                        documentation: 'Matches all documents with optional boost',\n                        detail: 'Basic query - matches everything'\n                    },\n                    {\n                        label: 'match',\n                        kind: monaco.languages.CompletionItemKind.Snippet,\n                        insertText: '\"match\": {\\n  \"${1:field}\": {\\n    \"query\": \"${2:value}\",\\n    \"operator\": \"and\"\\n  }\\n}',\n                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n                        documentation: 'Full-text search with advanced options',\n                        detail: 'Text search query'\n                    },\n                    {\n                        label: 'multi_match',\n                        kind: monaco.languages.CompletionItemKind.Snippet,\n                        insertText: '\"multi_match\": {\\n  \"query\": \"${1:search_text}\",\\n  \"fields\": [\"${2:field1}\", \"${3:field2}\"],\\n  \"type\": \"best_fields\"\\n}',\n                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n                        documentation: 'Search across multiple fields',\n                        detail: 'Multi-field text search'\n                    },\n                    {\n                        label: 'term',\n                        kind: monaco.languages.CompletionItemKind.Snippet,\n                        insertText: '\"term\": {\\n  \"${1:field}\": {\\n    \"value\": \"${2:exact_value}\",\\n    \"boost\": 1.0\\n  }\\n}',\n                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n                        documentation: 'Exact term match with boost',\n                        detail: 'Exact match query'\n                    },\n                    {\n                        label: 'terms',\n                        kind: monaco.languages.CompletionItemKind.Snippet,\n                        insertText: '\"terms\": {\\n  \"${1:field}\": [\"${2:value1}\", \"${3:value2}\"],\\n  \"boost\": 1.0\\n}',\n                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n                        documentation: 'Match any of the provided terms',\n                        detail: 'Multiple exact values'\n                    },\n                    {\n                        label: 'range',\n                        kind: monaco.languages.CompletionItemKind.Snippet,\n                        insertText: '\"range\": {\\n  \"${1:field}\": {\\n    \"gte\": \"${2:from}\",\\n    \"lte\": \"${3:to}\",\\n    \"boost\": 1.0\\n  }\\n}',\n                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n                        documentation: 'Range query with boost',\n                        detail: 'Numeric/date range'\n                    },\n                    {\n                        label: 'bool',\n                        kind: monaco.languages.CompletionItemKind.Snippet,\n                        insertText: '\"bool\": {\\n  \"must\": [\\n    ${1:// Required conditions}\\n  ],\\n  \"should\": [\\n    ${2:// Optional conditions}\\n  ],\\n  \"must_not\": [\\n    ${3:// Excluded conditions}\\n  ],\\n  \"filter\": [\\n    ${4:// Filter conditions}\\n  ]\\n}',\n                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n                        documentation: 'Boolean query with all clauses',\n                        detail: 'Complex boolean logic'\n                    },\n                    {\n                        label: 'exists',\n                        kind: monaco.languages.CompletionItemKind.Snippet,\n                        insertText: '\"exists\": {\\n  \"field\": \"${1:field}\"\\n}',\n                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n                        documentation: 'Check if field exists and has a value',\n                        detail: 'Field existence check'\n                    },\n                    {\n                        label: 'wildcard',\n                        kind: monaco.languages.CompletionItemKind.Snippet,\n                        insertText: '\"wildcard\": {\\n  \"${1:field}\": {\\n    \"value\": \"${2:pattern*}\",\\n    \"boost\": 1.0\\n  }\\n}',\n                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n                        documentation: 'Wildcard pattern matching',\n                        detail: 'Pattern matching with * and ?'\n                    },\n                    {\n                        label: 'fuzzy',\n                        kind: monaco.languages.CompletionItemKind.Snippet,\n                        insertText: '\"fuzzy\": {\\n  \"${1:field}\": {\\n    \"value\": \"${2:term}\",\\n    \"fuzziness\": \"AUTO\"\\n  }\\n}',\n                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n                        documentation: 'Fuzzy matching for typos',\n                        detail: 'Typo-tolerant search'\n                    },\n                    // Aggregations\n                    {\n                        label: 'aggs_terms',\n                        kind: monaco.languages.CompletionItemKind.Snippet,\n                        insertText: '\"aggs\": {\\n  \"${1:aggregation_name}\": {\\n    \"terms\": {\\n      \"field\": \"${2:field}\",\\n      \"size\": 10\\n    }\\n  }\\n}',\n                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n                        documentation: 'Terms aggregation',\n                        detail: 'Group by field values'\n                    },\n                    {\n                        label: 'aggs_date_histogram',\n                        kind: monaco.languages.CompletionItemKind.Snippet,\n                        insertText: '\"aggs\": {\\n  \"${1:aggregation_name}\": {\\n    \"date_histogram\": {\\n      \"field\": \"${2:date_field}\",\\n      \"calendar_interval\": \"day\"\\n    }\\n  }\\n}',\n                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n                        documentation: 'Date histogram aggregation',\n                        detail: 'Time-based grouping'\n                    },\n                    // Sorting\n                    {\n                        label: 'sort_field',\n                        kind: monaco.languages.CompletionItemKind.Snippet,\n                        insertText: '\"sort\": [\\n  {\\n    \"${1:field}\": {\\n      \"order\": \"${2|asc,desc|}\"\\n    }\\n  }\\n]',\n                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n                        documentation: 'Sort by field',\n                        detail: 'Field-based sorting'\n                    }\n                ];\n                // Add intelligent field-specific completions based on index mapping\n                if (selectedIndex && mapping) {\n                    // Check if we're in a field context\n                    const fieldContext = lineUntilPosition.includes('\"field\"') ||\n                        lineUntilPosition.match(/\"(match|term|range|wildcard|fuzzy|exists)\"\\s*:\\s*{\\s*\"?$/);\n                    if (fieldContext) {\n                        // Get intelligent field suggestions\n                        try {\n                            const partialField = lineUntilPosition.match(/\"([^\"]*)\"?\\s*$/)?.[1] || '';\n                            const intelligentFields = await intelligentAssistant.getFieldSuggestions(selectedIndex, partialField);\n                            const fieldSuggestions = intelligentFields.map(field => ({\n                                label: field.name,\n                                kind: monaco.languages.CompletionItemKind.Property,\n                                insertText: `\"${field.name}\"`,\n                                documentation: `${field.type} field - ${field.description || 'No description'}`,\n                                detail: `Popularity: ${Math.round(field.popularity * 100)}%`,\n                                sortText: `${1000 - Math.round(field.popularity * 1000)}_${field.name}` // Sort by popularity\n                            }));\n                            suggestions.push(...fieldSuggestions);\n                        }\n                        catch (error) {\n                            // Fallback to basic field suggestions\n                            const fieldSuggestions = mapping.fields.map(field => ({\n                                label: field.name,\n                                kind: monaco.languages.CompletionItemKind.Property,\n                                insertText: `\"${field.name}\"`,\n                                documentation: `${field.type} field - ${field.searchable ? 'searchable' : 'not searchable'}, ${field.aggregatable ? 'aggregatable' : 'not aggregatable'}`,\n                            }));\n                            suggestions.push(...fieldSuggestions);\n                        }\n                    }\n                    // Add value suggestions for specific fields\n                    const valueContext = lineUntilPosition.match(/\"(match|term|wildcard)\"\\s*:\\s*{\\s*\"([^\"]+)\"\\s*:\\s*\"?$/);\n                    if (valueContext) {\n                        const fieldName = valueContext[2];\n                        try {\n                            const partialValue = lineUntilPosition.match(/\"([^\"]*)\"?\\s*$/)?.[1] || '';\n                            const intelligentValues = await intelligentAssistant.getValueSuggestions(selectedIndex, fieldName, partialValue);\n                            const valueSuggestions = intelligentValues.slice(0, 10).map(value => ({\n                                label: String(value.value),\n                                kind: monaco.languages.CompletionItemKind.Value,\n                                insertText: `\"${value.value}\"`,\n                                documentation: `Frequency: ${Math.round(value.frequency * 100)}%`,\n                                detail: value.description || `${value.type} value`,\n                                sortText: `${1000 - Math.round(value.frequency * 1000)}_${value.value}`\n                            }));\n                            suggestions.push(...valueSuggestions);\n                        }\n                        catch (error) {\n                            console.error('Failed to get value suggestions:', error);\n                        }\n                    }\n                    // Add sort field completions\n                    const sortContext = lineUntilPosition.includes('\"sort\"') ||\n                        lineUntilPosition.match(/\"sort\"\\s*:\\s*\\[\\s*{\\s*\"?$/);\n                    if (sortContext) {\n                        const sortFields = mapping.fields\n                            .filter(field => field.aggregatable)\n                            .map(field => ({\n                            label: field.name,\n                            kind: monaco.languages.CompletionItemKind.Property,\n                            insertText: `\"${field.name}\"`,\n                            documentation: `${field.type} field - sortable`,\n                            detail: 'Sortable field'\n                        }));\n                        suggestions.push(...sortFields);\n                    }\n                    // Add _source field completions\n                    const sourceContext = lineUntilPosition.includes('\"_source\"') ||\n                        lineUntilPosition.match(/\"_source\"\\s*:\\s*\\[?\\s*\"?$/);\n                    if (sourceContext) {\n                        const sourceFields = mapping.fields.map(field => ({\n                            label: field.name,\n                            kind: monaco.languages.CompletionItemKind.Property,\n                            insertText: `\"${field.name}\"`,\n                            documentation: `Include ${field.name} in results`,\n                            detail: `${field.type} field`\n                        }));\n                        suggestions.push(...sourceFields);\n                    }\n                }\n                return { suggestions };\n            },\n        });\n    };\n    const formatQuery = () => {\n        if (editorRef.current) {\n            editorRef.current.getAction('editor.action.formatDocument').run();\n        }\n    };\n    const validateQuery = () => {\n        try {\n            JSON.parse(dslQuery);\n            return { isValid: true, error: null };\n        }\n        catch (error) {\n            return {\n                isValid: false,\n                error: error instanceof Error ? error.message : 'Invalid JSON',\n            };\n        }\n    };\n    const validation = validateQuery();\n    const insertTemplate = (template) => {\n        if (editorRef.current) {\n            const selection = editorRef.current.getSelection();\n            editorRef.current.executeEdits('', [\n                {\n                    range: selection,\n                    text: template,\n                },\n            ]);\n            editorRef.current.focus();\n        }\n    };\n    const handleDSLInsert = (dsl) => {\n        setDslQuery(dsl);\n        if (editorRef.current) {\n            editorRef.current.focus();\n        }\n    };\n    const applyOptimization = (optimization) => {\n        const optimizedQueryString = JSON.stringify(optimization.optimizedQuery, null, 2);\n        setDslQuery(optimizedQueryString);\n        // Remove applied optimization from list\n        setOptimizations(opts => opts.filter(opt => opt !== optimization));\n    };\n    const getComplexityColor = (complexity) => {\n        switch (complexity) {\n            case 'simple': return 'text-green-600 bg-green-50';\n            case 'moderate': return 'text-yellow-600 bg-yellow-50';\n            case 'complex': return 'text-orange-600 bg-orange-50';\n            case 'very_complex': return 'text-red-600 bg-red-50';\n            default: return 'text-gray-600 bg-gray-50';\n        }\n    };\n    const queryTemplates = [\n        {\n            name: 'Match All',\n            category: 'basic',\n            template: '{\\n  \"query\": {\\n    \"match_all\": {\\n      \"boost\": 1.0\\n    }\\n  },\\n  \"size\": 10\\n}',\n            description: '匹配所有文档'\n        },\n        {\n            name: 'Simple Match',\n            category: 'basic',\n            template: '{\\n  \"query\": {\\n    \"match\": {\\n      \"field_name\": {\\n        \"query\": \"search_term\",\\n        \"operator\": \"and\"\\n      }\\n    }\\n  }\\n}',\n            description: '简单文本搜索'\n        },\n        {\n            name: 'Multi Match',\n            category: 'text',\n            template: '{\\n  \"query\": {\\n    \"multi_match\": {\\n      \"query\": \"search_term\",\\n      \"fields\": [\"field1\", \"field2^2\"],\\n      \"type\": \"best_fields\"\\n    }\\n  }\\n}',\n            description: '多字段文本搜索'\n        },\n        {\n            name: 'Boolean Query',\n            category: 'complex',\n            template: '{\\n  \"query\": {\\n    \"bool\": {\\n      \"must\": [\\n        { \"match\": { \"field1\": \"value1\" } }\\n      ],\\n      \"filter\": [\\n        { \"term\": { \"field2\": \"value2\" } }\\n      ],\\n      \"should\": [\\n        { \"match\": { \"field3\": \"value3\" } }\\n      ],\\n      \"minimum_should_match\": 1\\n    }\\n  }\\n}',\n            description: '复杂布尔查询'\n        },\n        {\n            name: 'Range Query',\n            category: 'filter',\n            template: '{\\n  \"query\": {\\n    \"range\": {\\n      \"date_field\": {\\n        \"gte\": \"2023-01-01\",\\n        \"lte\": \"2023-12-31\",\\n        \"format\": \"yyyy-MM-dd\"\\n      }\\n    }\\n  }\\n}',\n            description: '范围查询'\n        },\n        {\n            name: 'Aggregation Query',\n            category: 'analytics',\n            template: '{\\n  \"size\": 0,\\n  \"aggs\": {\\n    \"group_by_field\": {\\n      \"terms\": {\\n        \"field\": \"category.keyword\",\\n        \"size\": 10\\n      },\\n      \"aggs\": {\\n        \"avg_value\": {\\n          \"avg\": {\\n            \"field\": \"price\"\\n          }\\n        }\\n      }\\n    }\\n  }\\n}',\n            description: '聚合分析查询'\n        },\n        {\n            name: 'Fuzzy Search',\n            category: 'text',\n            template: '{\\n  \"query\": {\\n    \"fuzzy\": {\\n      \"field_name\": {\\n        \"value\": \"search_term\",\\n        \"fuzziness\": \"AUTO\",\\n        \"max_expansions\": 50\\n      }\\n    }\\n  }\\n}',\n            description: '模糊搜索'\n        },\n        {\n            name: 'Wildcard Search',\n            category: 'text',\n            template: '{\\n  \"query\": {\\n    \"wildcard\": {\\n      \"field_name\": {\\n        \"value\": \"*search*\",\\n        \"boost\": 1.0\\n      }\\n    }\\n  }\\n}',\n            description: '通配符搜索'\n        }\n    ];\n    return (_jsxs(\"div\", { className: \"space-y-4\", children: [_jsxs(Card, { className: \"p-4\", children: [_jsxs(\"div\", { className: \"flex items-center justify-between\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-4\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-2\", children: [_jsx(Sparkles, { className: \"w-5 h-5 text-blue-500\" }), _jsx(\"span\", { className: \"font-medium text-gray-900 dark:text-white\", children: \"\\u667A\\u80FD DSL \\u7F16\\u8F91\\u5668\" }), isAnalyzing && (_jsx(\"div\", { className: \"animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full\" }))] }), _jsxs(\"div\", { className: \"flex items-center space-x-3\", children: [validation.isValid ? (_jsxs(\"div\", { className: \"flex items-center space-x-1 text-green-600\", children: [_jsx(Check, { className: \"w-4 h-4\" }), _jsx(\"span\", { className: \"text-sm\", children: \"\\u8BED\\u6CD5\\u6B63\\u786E\" })] })) : (_jsxs(\"div\", { className: \"flex items-center space-x-1 text-red-600\", children: [_jsx(AlertCircle, { className: \"w-4 h-4\" }), _jsx(\"span\", { className: \"text-sm\", children: \"\\u8BED\\u6CD5\\u9519\\u8BEF\" })] })), validation.isValid && dslQuery.trim() && (_jsxs(Badge, { className: `text-xs ${getComplexityColor(queryComplexity)}`, children: [\"\\u590D\\u6742\\u5EA6: \", queryComplexity] }))] })] }), _jsxs(\"div\", { className: \"flex items-center space-x-2\", children: [_jsx(Button, { variant: \"outline\", size: \"sm\", onClick: formatQuery, children: \"\\u683C\\u5F0F\\u5316\" }), _jsxs(Button, { variant: \"outline\", size: \"sm\", onClick: () => setShowOptimizations(!showOptimizations), children: [showOptimizations ? '隐藏' : '显示', \"\\u4F18\\u5316\"] }), _jsxs(Button, { variant: \"outline\", size: \"sm\", onClick: () => setShowLearningSupport(!showLearningSupport), className: \"flex items-center space-x-1\", children: [_jsx(GraduationCap, { className: \"w-4 h-4\" }), _jsxs(\"span\", { children: [showLearningSupport ? '隐藏' : '显示', \"\\u5B66\\u4E60\"] })] })] })] }), !validation.isValid && validation.error && (_jsx(\"div\", { className: \"mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md\", children: _jsxs(\"div\", { className: \"flex items-start space-x-2\", children: [_jsx(AlertCircle, { className: \"w-4 h-4 text-red-600 mt-0.5 flex-shrink-0\" }), _jsxs(\"div\", { children: [_jsx(\"p\", { className: \"text-sm font-medium text-red-700 dark:text-red-400\", children: \"JSON \\u8BED\\u6CD5\\u9519\\u8BEF\" }), _jsx(\"p\", { className: \"text-sm text-red-600 dark:text-red-300 mt-1\", children: validation.error })] })] }) }))] }), showOptimizations && optimizations.length > 0 && (_jsxs(Card, { className: \"p-4\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-2 mb-3\", children: [_jsx(Zap, { className: \"w-5 h-5 text-orange-500\" }), _jsx(\"h4\", { className: \"font-semibold text-gray-900 dark:text-white\", children: \"\\u67E5\\u8BE2\\u4F18\\u5316\\u5EFA\\u8BAE\" }), _jsx(Badge, { variant: \"secondary\", children: optimizations.length })] }), _jsx(\"div\", { className: \"space-y-3\", children: optimizations.slice(0, 3).map((optimization, index) => (_jsx(\"div\", { className: \"p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg\", children: _jsxs(\"div\", { className: \"flex items-start justify-between\", children: [_jsxs(\"div\", { className: \"flex-1\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-2 mb-1\", children: [_jsx(\"h5\", { className: \"font-medium text-sm text-gray-900 dark:text-white\", children: optimization.title }), _jsx(Badge, { variant: optimization.severity === 'error' ? 'destructive' :\n                                                            optimization.severity === 'warning' ? 'secondary' : 'outline', className: \"text-xs\", children: optimization.severity })] }), _jsx(\"p\", { className: \"text-xs text-gray-600 dark:text-gray-400 mb-2\", children: optimization.description }), _jsxs(\"p\", { className: \"text-xs text-blue-600 dark:text-blue-400\", children: [\"\\u5EFA\\u8BAE: \", optimization.suggestion] }), optimization.estimatedImprovement?.performance && (_jsxs(\"p\", { className: \"text-xs text-green-600 dark:text-green-400 mt-1\", children: [\"\\u9884\\u671F\\u63D0\\u5347: \", optimization.estimatedImprovement.performance] }))] }), optimization.autoApplicable && (_jsx(Button, { variant: \"outline\", size: \"sm\", onClick: () => applyOptimization(optimization), className: \"ml-3 flex-shrink-0\", children: \"\\u5E94\\u7528\" }))] }) }, index))) })] })), _jsxs(Card, { className: \"p-4\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-2 mb-4\", children: [_jsx(BookOpen, { className: \"w-5 h-5 text-gray-500\" }), _jsx(\"h4\", { className: \"font-medium text-gray-900 dark:text-white\", children: \"\\u667A\\u80FD\\u67E5\\u8BE2\\u6A21\\u677F\" })] }), _jsx(\"div\", { className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\", children: queryTemplates.map(template => (_jsxs(\"div\", { className: \"p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-300 dark:hover:border-blue-600 cursor-pointer transition-colors\", onClick: () => insertTemplate(template.template), children: [_jsxs(\"div\", { className: \"flex items-center justify-between mb-2\", children: [_jsx(\"h5\", { className: \"font-medium text-sm text-gray-900 dark:text-white\", children: template.name }), _jsx(Badge, { variant: \"outline\", className: \"text-xs\", children: template.category })] }), _jsx(\"p\", { className: \"text-xs text-gray-600 dark:text-gray-400\", children: template.description })] }, template.name))) }), _jsx(\"div\", { className: \"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\", children: _jsxs(\"div\", { className: \"flex items-start space-x-2\", children: [_jsx(Lightbulb, { className: \"w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0\" }), _jsxs(\"div\", { children: [_jsx(\"p\", { className: \"text-sm font-medium text-blue-800 dark:text-blue-200\", children: \"\\u667A\\u80FD\\u63D0\\u793A\" }), _jsx(\"p\", { className: \"text-xs text-blue-700 dark:text-blue-300 mt-1\", children: \"\\u8F93\\u5165\\u65F6\\u4F1A\\u81EA\\u52A8\\u63D0\\u4F9B\\u5B57\\u6BB5\\u540D\\u548C\\u503C\\u7684\\u667A\\u80FD\\u5EFA\\u8BAE\\u3002\\u4F7F\\u7528 Ctrl+Space \\u624B\\u52A8\\u89E6\\u53D1\\u81EA\\u52A8\\u5B8C\\u6210\\u3002\" })] })] }) })] }), _jsx(Card, { className: \"overflow-hidden\", children: _jsx(\"div\", { className: \"h-96\", children: _jsx(Editor, { height: \"100%\", defaultLanguage: \"json\", theme: theme === 'dark' ? 'vs-dark' : 'vs-light', value: dslQuery, onChange: value => setDslQuery(value || ''), onMount: handleEditorDidMount, options: {\n                            minimap: { enabled: false },\n                            scrollBeyondLastLine: false,\n                            fontSize: 14,\n                            lineNumbers: 'on',\n                            roundedSelection: false,\n                            scrollbar: {\n                                vertical: 'auto',\n                                horizontal: 'auto',\n                            },\n                            automaticLayout: true,\n                            tabSize: 2,\n                            insertSpaces: true,\n                            wordWrap: 'on',\n                            bracketPairColorization: { enabled: true },\n                            suggest: {\n                                showKeywords: true,\n                                showSnippets: true,\n                            },\n                        } }) }) }), showDSLDisplay && parsedQuery && (_jsx(DSLCodeDisplay, { query: parsedQuery, isVisible: showDSLDisplay, onToggleVisibility: () => setShowDSLDisplay(!showDSLDisplay), showExplanations: true, className: \"mt-4\" })), showLearningSupport && (_jsx(LearningSupport, { currentQuery: parsedQuery, userLevel: \"intermediate\", onDSLInsert: handleDSLInsert, className: \"mt-4\" }))] }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { useEffect } from 'react';\nimport { Search, Play, Save, History } from 'lucide-react';\nimport { useQueryStore } from '../../stores/query';\nimport { useConnectionStore } from '../../stores/connection';\nimport { Button } from '../UI/Button';\nimport { Card } from '../UI/Card';\nimport { Dropdown } from '../UI/Dropdown';\nimport { Spinner } from '../UI/Spinner';\nimport VisualQueryBuilder from './VisualQueryBuilder';\nimport DSLQueryEditor from './DSLQueryEditor';\nexport default function QueryBuilder() {\n    const { selectedIndex, queryMode, isSearching, searchError, pageSize, setSelectedIndex, setQueryMode, executeQuery, setPageSize, clearResults, reset, } = useQueryStore();\n    const { clusterInfo } = useConnectionStore();\n    const elasticsearchService = ElasticsearchService.getInstance();\n    // Load real indices from Elasticsearch\n    const [availableIndices, setAvailableIndices] = React.useState([]);\n    const [isLoadingIndices, setIsLoadingIndices] = React.useState(false);\n    useEffect(() => {\n        loadAvailableIndices();\n    }, [clusterInfo]);\n    const loadAvailableIndices = async () => {\n        if (!clusterInfo)\n            return;\n        setIsLoadingIndices(true);\n        try {\n            const indices = await elasticsearchService.listIndices();\n            setAvailableIndices(indices.map(index => index.name));\n        }\n        catch (error) {\n            console.error('Failed to load indices:', error);\n            setAvailableIndices([]);\n        }\n        finally {\n            setIsLoadingIndices(false);\n        }\n    };\n    const indexOptions = availableIndices.map(index => ({\n        value: index,\n        label: index,\n    }));\n    const pageSizeOptions = [\n        { value: '10', label: '10 per page' },\n        { value: '20', label: '20 per page' },\n        { value: '50', label: '50 per page' },\n        { value: '100', label: '100 per page' },\n    ];\n    const handleExecuteQuery = async () => {\n        if (!selectedIndex) {\n            return;\n        }\n        try {\n            await executeQuery();\n        }\n        catch (error) {\n            console.error('Query execution failed:', error);\n        }\n    };\n    const handleSaveQuery = () => {\n        // This would open a modal to save the query\n        // For now, we'll just show an alert\n        alert('Save query functionality will be implemented in the saved queries component');\n    };\n    return (_jsxs(\"div\", { className: \"space-y-6\", children: [_jsxs(Card, { className: \"p-6\", children: [_jsxs(\"div\", { className: \"flex items-center justify-between mb-6\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-3\", children: [_jsx(Search, { className: \"w-6 h-6 text-blue-500\" }), _jsx(\"h2\", { className: \"text-xl font-semibold text-gray-900 dark:text-white\", children: \"Query Builder\" })] }), _jsxs(\"div\", { className: \"flex items-center space-x-3\", children: [_jsxs(Button, { variant: \"outline\", size: \"sm\", onClick: handleSaveQuery, disabled: !selectedIndex, className: \"flex items-center space-x-2\", children: [_jsx(Save, { className: \"w-4 h-4\" }), _jsx(\"span\", { children: \"Save Query\" })] }), _jsxs(Button, { variant: \"outline\", size: \"sm\", onClick: () => {\n                                            /* Open history */\n                                        }, className: \"flex items-center space-x-2\", children: [_jsx(History, { className: \"w-4 h-4\" }), _jsx(\"span\", { children: \"History\" })] })] })] }), _jsxs(\"div\", { className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\", children: [_jsxs(\"div\", { children: [_jsx(\"label\", { className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\", children: \"Select Index\" }), isLoadingIndices ? (_jsxs(\"div\", { className: \"flex items-center space-x-2 p-2 border border-gray-300 dark:border-gray-600 rounded-md\", children: [_jsx(Spinner, { size: \"sm\" }), _jsx(\"span\", { className: \"text-sm text-gray-500\", children: \"Loading indices...\" })] })) : (_jsx(Dropdown, { options: indexOptions, value: selectedIndex, onChange: setSelectedIndex, placeholder: \"Choose an index\", disabled: availableIndices.length === 0 }))] }), _jsxs(\"div\", { children: [_jsx(\"label\", { className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\", children: \"Query Mode\" }), _jsxs(\"div\", { className: \"flex items-center space-x-4 p-2\", children: [_jsxs(\"label\", { className: \"flex items-center space-x-2\", children: [_jsx(\"input\", { type: \"radio\", name: \"queryMode\", value: \"visual\", checked: queryMode === 'visual', onChange: () => setQueryMode('visual'), className: \"text-blue-500\" }), _jsx(\"span\", { className: \"text-sm\", children: \"Visual\" })] }), _jsxs(\"label\", { className: \"flex items-center space-x-2\", children: [_jsx(\"input\", { type: \"radio\", name: \"queryMode\", value: \"dsl\", checked: queryMode === 'dsl', onChange: () => setQueryMode('dsl'), className: \"text-blue-500\" }), _jsx(\"span\", { className: \"text-sm\", children: \"DSL\" })] })] })] }), _jsxs(\"div\", { children: [_jsx(\"label\", { className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\", children: \"Results per page\" }), _jsx(Dropdown, { options: pageSizeOptions, value: pageSize.toString(), onChange: value => setPageSize(parseInt(value)) })] })] }), _jsxs(\"div\", { className: \"flex items-center justify-between\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-3\", children: [_jsxs(Button, { onClick: handleExecuteQuery, disabled: !selectedIndex || isSearching, className: \"flex items-center space-x-2\", children: [isSearching ? (_jsx(Spinner, { size: \"sm\" })) : (_jsx(Play, { className: \"w-4 h-4\" })), _jsx(\"span\", { children: isSearching ? 'Searching...' : 'Execute Query' })] }), _jsx(Button, { variant: \"outline\", onClick: clearResults, disabled: isSearching, children: \"Clear Results\" })] }), searchError && (_jsxs(\"div\", { className: \"text-red-600 text-sm\", children: [\"Error: \", searchError] }))] })] }), selectedIndex ? (queryMode === 'visual' ? (_jsx(VisualQueryBuilder, {})) : (_jsx(DSLQueryEditor, {}))) : (_jsx(Card, { className: \"p-12\", children: _jsxs(\"div\", { className: \"text-center text-gray-500 dark:text-gray-400\", children: [_jsx(Search, { className: \"w-12 h-12 mx-auto mb-4 opacity-50\" }), _jsx(\"h3\", { className: \"text-lg font-medium mb-2\", children: \"Select an Index to Start\" }), _jsx(\"p\", { children: \"Choose an Elasticsearch index from the dropdown above to begin building your query.\" })] }) }))] }));\n}\n// Import the service at the top level to avoid circular dependencies\nimport { ElasticsearchService } from '../../services/elasticsearch';\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React from 'react';\nimport { X } from 'lucide-react';\nimport { Button } from '../UI/Button';\nimport { Input } from '../UI/Input';\nimport { Dropdown } from '../UI/Dropdown';\nimport { FieldAutocomplete } from '../Data/FieldAutocomplete';\nconst OPERATORS = [\n    { value: 'equals', label: 'Equals' },\n    { value: 'not_equals', label: 'Not Equals' },\n    { value: 'contains', label: 'Contains' },\n    { value: 'not_contains', label: 'Not Contains' },\n    { value: 'exists', label: 'Exists' },\n    { value: 'not_exists', label: 'Not Exists' },\n    { value: 'range', label: 'Range' },\n    { value: 'wildcard', label: 'Wildcard' },\n    { value: 'regexp', label: 'Regex' },\n];\nconst LOGICAL_OPERATORS = [\n    { value: 'AND', label: 'AND' },\n    { value: 'OR', label: 'OR' },\n];\nexport default function QueryCondition({ condition, availableFields, isFirst, onUpdate, onRemove, }) {\n    const renderValueInput = () => {\n        if (condition.operator === 'exists' ||\n            condition.operator === 'not_exists') {\n            return null;\n        }\n        if (condition.operator === 'range') {\n            const rangeValue = typeof condition.value === 'object' ? condition.value : {};\n            return (_jsxs(\"div\", { className: \"flex space-x-2\", children: [_jsx(Input, { placeholder: \"\\u4ECE\", value: rangeValue.gte || rangeValue.gt || '', onChange: e => onUpdate({\n                            value: {\n                                ...rangeValue,\n                                gte: e.target.value || undefined,\n                                gt: undefined,\n                            },\n                        }), className: \"flex-1\" }), _jsx(Input, { placeholder: \"\\u5230\", value: rangeValue.lte || rangeValue.lt || '', onChange: e => onUpdate({\n                            value: {\n                                ...rangeValue,\n                                lte: e.target.value || undefined,\n                                lt: undefined,\n                            },\n                        }), className: \"flex-1\" })] }));\n        }\n        return (_jsx(FieldAutocomplete, { index: index, value: condition.value || '', onChange: value => onUpdate({ value }), placeholder: \"\\u8F93\\u5165\\u503C\", type: \"value\", fieldName: condition.field, showValueSuggestions: true, className: \"flex-1\" }));\n    };\n    return (_jsxs(\"div\", { className: \"flex items-center space-x-3 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700\", children: [!isFirst && (_jsx(\"div\", { className: \"flex-shrink-0\", children: _jsx(Dropdown, { options: LOGICAL_OPERATORS, value: condition.logicalOperator || 'AND', onChange: value => onUpdate({ logicalOperator: value }), className: \"w-20\" }) })), _jsx(\"div\", { className: \"flex-shrink-0 w-48\", children: _jsx(FieldAutocomplete, { index: index, value: condition.field, onChange: value => onUpdate({ field: value }), placeholder: \"\\u9009\\u62E9\\u5B57\\u6BB5\", type: \"field\" }) }), _jsx(\"div\", { className: \"flex-shrink-0 w-32\", children: _jsx(Dropdown, { options: OPERATORS, value: condition.operator, onChange: value => onUpdate({ operator: value }) }) }), _jsx(\"div\", { className: \"flex-1\", children: renderValueInput() }), _jsx(\"div\", { className: \"flex-shrink-0\", children: _jsx(Button, { variant: \"ghost\", size: \"sm\", onClick: onRemove, className: \"text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20\", children: _jsx(X, { className: \"w-4 h-4\" }) }) })] }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React from 'react';\nimport { Plus, ArrowUpDown } from 'lucide-react';\nimport { useQueryStore } from '../../stores/query';\nimport { useFieldMapping } from '../Data/FieldAutocomplete';\nimport { Button } from '../UI/Button';\nimport { Card } from '../UI/Card';\nimport { Dropdown } from '../UI/Dropdown';\nimport { FieldAutocomplete } from '../Data/FieldAutocomplete';\nimport QueryCondition from './QueryCondition';\nexport default function VisualQueryBuilder() {\n    const { selectedIndex, visualQuery, addCondition, removeCondition, updateCondition, updateVisualQuery, } = useQueryStore();\n    const { mapping, isLoading } = useFieldMapping(selectedIndex || '');\n    const handleSetSort = (field, order) => {\n        updateVisualQuery({\n            ...visualQuery,\n            sortField: field,\n            sortOrder: order,\n        });\n    };\n    const handleClearSort = () => {\n        updateVisualQuery({\n            ...visualQuery,\n            sortField: undefined,\n            sortOrder: undefined,\n        });\n    };\n    const availableFields = mapping?.fields || [];\n    const fieldOptions = availableFields.map(field => ({\n        value: field.name,\n        label: `${field.name} (${field.type})`,\n    }));\n    const sortOptions = [\n        { value: 'asc', label: 'Ascending' },\n        { value: 'desc', label: 'Descending' },\n    ];\n    if (isLoading) {\n        return (_jsx(Card, { className: \"p-6\", children: _jsxs(\"div\", { className: \"flex items-center justify-center\", children: [_jsx(\"div\", { className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\" }), _jsx(\"span\", { className: \"ml-3 text-gray-600 dark:text-gray-400\", children: \"Loading index fields...\" })] }) }));\n    }\n    return (_jsxs(\"div\", { className: \"space-y-6\", children: [_jsxs(Card, { className: \"p-6\", children: [_jsxs(\"div\", { className: \"flex items-center justify-between mb-4\", children: [_jsx(\"h3\", { className: \"text-lg font-semibold text-gray-900 dark:text-white\", children: \"Query Conditions\" }), _jsxs(Button, { onClick: addCondition, size: \"sm\", className: \"flex items-center space-x-2\", children: [_jsx(Plus, { className: \"w-4 h-4\" }), _jsx(\"span\", { children: \"Add Condition\" })] })] }), visualQuery.conditions.length === 0 ? (_jsxs(\"div\", { className: \"text-center py-8 text-gray-500 dark:text-gray-400\", children: [_jsx(\"p\", { children: \"No conditions added. Click \\\"Add Condition\\\" to start building your query.\" }), _jsx(\"p\", { className: \"text-sm mt-2\", children: \"Without conditions, this will return all documents.\" })] })) : (_jsx(\"div\", { className: \"space-y-3\", children: visualQuery.conditions.map((condition, index) => (_jsx(QueryCondition, { condition: condition, availableFields: availableFields, isFirst: index === 0, index: selectedIndex || '', onUpdate: updates => updateCondition(condition.id, updates), onRemove: () => removeCondition(condition.id) }, condition.id))) }))] }), _jsxs(Card, { className: \"p-6\", children: [_jsxs(\"div\", { className: \"flex items-center justify-between mb-4\", children: [_jsxs(\"h3\", { className: \"text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2\", children: [_jsx(ArrowUpDown, { className: \"w-5 h-5\" }), _jsx(\"span\", { children: \"Sort Order\" })] }), visualQuery.sortField && (_jsxs(Button, { onClick: handleClearSort, size: \"sm\", variant: \"outline\", className: \"flex items-center space-x-2\", children: [_jsx(Plus, { className: \"w-4 h-4 rotate-45\" }), _jsx(\"span\", { children: \"Clear Sort\" })] }))] }), !visualQuery.sortField ? (_jsx(\"div\", { className: \"text-center py-6 text-gray-500 dark:text-gray-400\", children: _jsx(\"p\", { children: \"No sorting configured. Results will be sorted by relevance score.\" }) })) : (_jsxs(\"div\", { className: \"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\", children: [_jsx(\"div\", { className: \"flex-1\", children: _jsx(FieldAutocomplete, { index: selectedIndex || '', value: visualQuery.sortField, onChange: value => handleSetSort(value, visualQuery.sortOrder || 'asc'), placeholder: \"\\u9009\\u62E9\\u6392\\u5E8F\\u5B57\\u6BB5\", type: \"field\" }) }), _jsx(\"div\", { className: \"w-32\", children: _jsx(Dropdown, { options: sortOptions, value: visualQuery.sortOrder || 'asc', onChange: value => handleSetSort(visualQuery.sortField || '', value) }) })] }))] })] }));\n}\n", "import { useState, useEffect } from 'react';\nexport function useDebounce(value, delay) {\n    const [debouncedValue, setDebouncedValue] = useState(value);\n    useEffect(() => {\n        const handler = setTimeout(() => {\n            setDebouncedValue(value);\n        }, delay);\n        return () => {\n            clearTimeout(handler);\n        };\n    }, [value, delay]);\n    return debouncedValue;\n}\n", "import { ElasticsearchService } from './elasticsearch';\nexport class FieldMappingService {\n    constructor() {\n        this.fieldCache = new Map();\n        this.suggestionCache = new Map();\n        this.valueCache = new Map();\n    }\n    static getInstance() {\n        if (!FieldMappingService.instance) {\n            FieldMappingService.instance = new FieldMappingService();\n        }\n        return FieldMappingService.instance;\n    }\n    async getIndexMapping(index) {\n        if (this.fieldCache.has(index)) {\n            return this.fieldCache.get(index);\n        }\n        try {\n            const elasticsearchService = ElasticsearchService.getInstance();\n            const response = await elasticsearchService.getClient().indices.getMapping({\n                index: index\n            });\n            const mapping = response[index]?.mappings || {};\n            const properties = mapping.properties || {};\n            const fields = this.extractFieldsFromProperties(properties);\n            const indexMapping = {\n                index,\n                fields,\n                timestampField: this.findTimestampField(fields),\n                idField: this.findIdField(fields),\n                nestedFields: this.findNestedFields(properties),\n                dynamic: mapping.dynamic !== false\n            };\n            this.fieldCache.set(index, indexMapping);\n            return indexMapping;\n        }\n        catch (error) {\n            console.error('Failed to get index mapping:', error);\n            return null;\n        }\n    }\n    extractFieldsFromProperties(properties, prefix = '', path = '') {\n        const fields = [];\n        for (const [fieldName, fieldMapping] of Object.entries(properties)) {\n            const fullName = prefix ? `${prefix}.${fieldName}` : fieldName;\n            const fullPath = path ? `${path}.${fieldName}` : fieldName;\n            if (fieldMapping && typeof fieldMapping === 'object') {\n                const mapping = fieldMapping;\n                // 处理多字段映射\n                if (mapping.fields) {\n                    // 添加主字段\n                    fields.push(this.createFieldInfo(fieldName, fullName, mapping, fullPath));\n                    // 添加子字段\n                    for (const [subFieldName, subFieldMapping] of Object.entries(mapping.fields)) {\n                        const subFullName = `${fullName}.${subFieldName}`;\n                        const subFullPath = `${fullPath}.${subFieldName}`;\n                        fields.push(this.createFieldInfo(subFieldName, subFullName, subFieldMapping, subFullPath));\n                    }\n                }\n                else if (mapping.type === 'nested' || mapping.type === 'object') {\n                    // 处理嵌套对象\n                    fields.push({\n                        name: fullName,\n                        type: mapping.type,\n                        searchable: false,\n                        aggregatable: false,\n                        isKeyword: false,\n                        isText: false,\n                        isNumeric: false,\n                        isDate: false,\n                        isBoolean: false,\n                        isObject: true,\n                        isArray: mapping.type === 'nested',\n                        path: fullPath\n                    });\n                    if (mapping.properties) {\n                        const nestedFields = this.extractFieldsFromProperties(mapping.properties, fullName, fullPath);\n                        fields.push(...nestedFields);\n                    }\n                }\n                else {\n                    // 普通字段\n                    fields.push(this.createFieldInfo(fieldName, fullName, mapping, fullPath));\n                }\n            }\n        }\n        return fields;\n    }\n    createFieldInfo(fieldName, fullName, mapping, path) {\n        const type = mapping.type || 'text';\n        return {\n            name: fullName,\n            type,\n            format: mapping.format,\n            analyzer: mapping.analyzer,\n            searchable: this.isSearchable(type, mapping),\n            aggregatable: this.isAggregatable(type, mapping),\n            isKeyword: type === 'keyword',\n            isText: type === 'text',\n            isNumeric: ['long', 'integer', 'short', 'byte', 'double', 'float', 'half_float', 'scaled_float'].includes(type),\n            isDate: type === 'date',\n            isBoolean: type === 'boolean',\n            isObject: false,\n            isArray: false,\n            path\n        };\n    }\n    isSearchable(type, mapping) {\n        if (type === 'text')\n            return true;\n        if (type === 'keyword')\n            return true;\n        if (['long', 'integer', 'short', 'byte', 'double', 'float', 'half_float', 'scaled_float'].includes(type))\n            return true;\n        if (type === 'date')\n            return true;\n        if (type === 'boolean')\n            return true;\n        return false;\n    }\n    isAggregatable(type, mapping) {\n        if (type === 'keyword')\n            return true;\n        if (['long', 'integer', 'short', 'byte', 'double', 'float', 'half_float', 'scaled_float'].includes(type))\n            return true;\n        if (type === 'date')\n            return true;\n        if (type === 'boolean')\n            return true;\n        return false;\n    }\n    findTimestampField(fields) {\n        const timestampFields = ['@timestamp', 'timestamp', 'created_at', 'updated_at', 'date'];\n        return fields.find(field => timestampFields.includes(field.name))?.name;\n    }\n    findIdField(fields) {\n        const idFields = ['id', '_id', 'uuid', 'identifier'];\n        return fields.find(field => idFields.includes(field.name))?.name;\n    }\n    findNestedFields(properties) {\n        const nestedFields = [];\n        for (const [fieldName, fieldMapping] of Object.entries(properties)) {\n            if (fieldMapping && typeof fieldMapping === 'object' && fieldMapping.type === 'nested') {\n                nestedFields.push(fieldName);\n            }\n        }\n        return nestedFields;\n    }\n    async getFieldSuggestions(index, query) {\n        const cacheKey = `${index}:${query}`;\n        if (this.suggestionCache.has(cacheKey)) {\n            return this.suggestionCache.get(cacheKey);\n        }\n        const mapping = await this.getIndexMapping(index);\n        if (!mapping)\n            return [];\n        const suggestions = mapping.fields\n            .filter(field => {\n            const matchesQuery = field.name.toLowerCase().includes(query.toLowerCase());\n            const isRelevant = field.searchable || field.aggregatable;\n            return matchesQuery && isRelevant;\n        })\n            .map(field => ({\n            label: field.name,\n            type: field.type,\n            description: this.getFieldDescription(field),\n            score: this.calculateRelevanceScore(field, query)\n        }))\n            .sort((a, b) => b.score - a.score)\n            .slice(0, 10);\n        this.suggestionCache.set(cacheKey, suggestions);\n        return suggestions;\n    }\n    async getValueSuggestions(index, field, query, maxResults = 10) {\n        const cacheKey = `${index}:${field}:${query}`;\n        if (this.valueCache.has(cacheKey)) {\n            return this.valueCache.get(cacheKey);\n        }\n        try {\n            const elasticsearchService = ElasticsearchService.getInstance();\n            const searchRequest = {\n                index: index,\n                size: 0,\n                aggs: {\n                    suggestions: {\n                        terms: {\n                            field: field,\n                            size: maxResults * 2,\n                            include: query ? `.*${query}.*` : undefined\n                        }\n                    }\n                }\n            };\n            const response = await elasticsearchService.getClient().search(searchRequest);\n            const buckets = response.aggregations?.suggestions?.buckets || [];\n            const suggestions = buckets\n                .map((bucket) => ({\n                value: bucket.key,\n                count: bucket.doc_count,\n                score: bucket.doc_count\n            }))\n                .sort((a, b) => b.score - a.score)\n                .slice(0, maxResults);\n            this.valueCache.set(cacheKey, suggestions);\n            return suggestions;\n        }\n        catch (error) {\n            console.error('Failed to get value suggestions:', error);\n            return [];\n        }\n    }\n    async getFieldValues(index, field) {\n        try {\n            const elasticsearchService = ElasticsearchService.getInstance();\n            const response = await elasticsearchService.getClient().search({\n                index: index,\n                size: 0,\n                aggs: {\n                    unique_values: {\n                        terms: {\n                            field: field,\n                            size: 100\n                        }\n                    }\n                }\n            });\n            const buckets = response.aggregations?.unique_values?.buckets || [];\n            return buckets.map((bucket) => bucket.key);\n        }\n        catch (error) {\n            console.error('Failed to get field values:', error);\n            return [];\n        }\n    }\n    getFieldDescription(field) {\n        const typeDescriptions = {\n            text: '全文搜索字段',\n            keyword: '精确匹配字段',\n            long: '长整数字段',\n            integer: '整数字段',\n            double: '双精度浮点数字段',\n            float: '单精度浮点数字段',\n            date: '日期时间字段',\n            boolean: '布尔字段',\n            object: '嵌套对象字段',\n            nested: '嵌套数组字段'\n        };\n        const description = typeDescriptions[field.type] || `${field.type}类型字段`;\n        const capabilities = [];\n        if (field.searchable)\n            capabilities.push('可搜索');\n        if (field.aggregatable)\n            capabilities.push('可聚合');\n        return `${description}${capabilities.length > 0 ? ` (${capabilities.join(', ')})` : ''}`;\n    }\n    calculateRelevanceScore(field, query) {\n        let score = 0;\n        // 名称匹配度\n        const nameMatch = field.name.toLowerCase().includes(query.toLowerCase());\n        if (nameMatch)\n            score += 10;\n        // 前缀匹配\n        if (field.name.toLowerCase().startsWith(query.toLowerCase()))\n            score += 5;\n        // 字段类型优先级\n        if (field.isText || field.isKeyword)\n            score += 3;\n        if (field.isNumeric || field.isDate)\n            score += 2;\n        // 可搜索性和可聚合性\n        if (field.searchable)\n            score += 1;\n        if (field.aggregatable)\n            score += 1;\n        return score;\n    }\n    clearCache(index) {\n        if (index) {\n            this.fieldCache.delete(index);\n            // 清除相关缓存\n            for (const key of this.suggestionCache.keys()) {\n                if (key.startsWith(`${index}:`)) {\n                    this.suggestionCache.delete(key);\n                }\n            }\n            for (const key of this.valueCache.keys()) {\n                if (key.startsWith(`${index}:`)) {\n                    this.valueCache.delete(key);\n                }\n            }\n        }\n        else {\n            this.fieldCache.clear();\n            this.suggestionCache.clear();\n            this.valueCache.clear();\n        }\n    }\n    async refreshMapping(index) {\n        this.clearCache(index);\n        await this.getIndexMapping(index);\n    }\n}\n"], "names": [], "sourceRoot": ""}