import { useState, useCallback, useEffect, useMemo } from 'react';
import { useToast } from './useToast';
import IndexSwitchOptimizer from '../services/IndexSwitchOptimizer';

export interface TabInfo {
  id: string;
  indexName: string;
  title: string;
  isActive: boolean;
  hasUnsavedChanges?: boolean;
  searchQuery?: string;
  searchResults?: any;
  lastUpdated?: Date;
  viewMode?: 'browser' | 'editor' | 'batch' | 'query';
  metadata?: {
    docCount?: number;
    size?: string;
    health?: 'green' | 'yellow' | 'red';
    status?: 'open' | 'close';
  };
}

export interface MultiTabDataManagerOptions {
  maxTabs?: number;
  enableAutoRefresh?: boolean;
  autoRefreshInterval?: number;
  enablePreload?: boolean;
  onTabChange?: (tabId: string, indexName: string) => void;
  onTabClose?: (tabId: string, indexName: string) => void;
  dataLoader?: (indexName: string) => Promise<any>;
}

export interface MultiTabDataManagerReturn {
  tabs: TabInfo[];
  activeTabId: string | null;
  activeTab: TabInfo | null;
  createTab: (indexName: string, options?: Partial<TabInfo>) => Promise<void>;
  closeTab: (tabId: string, force?: boolean) => Promise<boolean>;
  switchTab: (tabId: string) => void;
  updateTab: (tabId: string, updates: Partial<TabInfo>) => void;
  refreshTab: (tabId?: string) => Promise<void>;
  refreshAllTabs: () => Promise<void>;
  closeAllTabs: () => Promise<void>;
  duplicateTab: (tabId: string) => Promise<void>;
  getTabByIndex: (indexName: string) => TabInfo | null;
  isTabOpen: (indexName: string) => boolean;
  getPerformanceStats: () => any;
}

export const useMultiTabDataManager = (
  options: MultiTabDataManagerOptions = {}
): MultiTabDataManagerReturn => {
  const {
    maxTabs = 5,
    enableAutoRefresh = false,
    autoRefreshInterval = 30000,
    enablePreload = true,
    onTabChange,
    onTabClose,
    dataLoader
  } = options;

  const [tabs, setTabs] = useState<TabInfo[]>([]);
  const [activeTabId, setActiveTabId] = useState<string | null>(null);
  const { success, error } = useToast();
  const switchOptimizer = IndexSwitchOptimizer.getInstance();

  // 获取当前活动标签页
  const activeTab = useMemo(() => {
    return tabs.find(tab => tab.id === activeTabId) || null;
  }, [tabs, activeTabId]);

  // 自动刷新功能
  useEffect(() => {
    if (!enableAutoRefresh || !activeTabId || !dataLoader) return;

    const interval = setInterval(() => {
      refreshTab(activeTabId);
    }, autoRefreshInterval);

    return () => clearInterval(interval);
  }, [enableAutoRefresh, activeTabId, autoRefreshInterval, dataLoader]);

  // 生成唯一的标签页ID
  const generateTabId = useCallback(() => {
    return `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // 创建新标签页
  const createTab = useCallback(async (
    indexName: string, 
    options: Partial<TabInfo> = {}
  ): Promise<void> => {
    // 检查是否已存在该索引的标签页
    const existingTab = tabs.find(tab => tab.indexName === indexName);
    if (existingTab) {
      switchTab(existingTab.id);
      return;
    }

    // 检查标签页数量限制
    if (tabs.length >= maxTabs) {
      error(`最多只能打开 ${maxTabs} 个标签页`);
      return;
    }

    const newTabId = generateTabId();
    const newTab: TabInfo = {
      id: newTabId,
      indexName,
      title: options.title || indexName,
      isActive: true,
      viewMode: options.viewMode || 'browser',
      lastUpdated: new Date(),
      hasUnsavedChanges: false,
      ...options
    };

    try {
      // 使用优化器加载数据
      if (dataLoader) {
        const data = await switchOptimizer.switchIndex(
          indexName,
          dataLoader,
          (stage, progress) => {
            console.log(`标签页 ${indexName} 加载进度: ${stage} - ${progress}%`);
          }
        );
        
        newTab.searchResults = data;
        newTab.lastUpdated = new Date();
      }

      // 更新标签页状态
      setTabs(prev => {
        const updated = prev.map(tab => ({ ...tab, isActive: false }));
        return [...updated, newTab];
      });
      
      setActiveTabId(newTabId);
      onTabChange?.(newTabId, indexName);
      success(`已打开索引: ${indexName}`);

    } catch (err) {
      error(`打开索引 ${indexName} 失败: ${err instanceof Error ? err.message : '未知错误'}`);
    }
  }, [tabs, maxTabs, generateTabId, switchOptimizer, dataLoader, onTabChange, success, error]);

  // 关闭标签页
  const closeTab = useCallback(async (tabId: string, force = false): Promise<boolean> => {
    const tabToClose = tabs.find(tab => tab.id === tabId);
    if (!tabToClose) return false;

    // 检查未保存的更改
    if (tabToClose.hasUnsavedChanges && !force) {
      const confirmed = confirm('该标签页有未保存的更改，确定要关闭吗？');
      if (!confirmed) return false;
    }

    setTabs(prev => {
      const filtered = prev.filter(tab => tab.id !== tabId);
      
      // 如果关闭的是当前活动标签页，切换到其他标签页
      if (tabId === activeTabId) {
        if (filtered.length > 0) {
          const newActiveTab = filtered[filtered.length - 1];
          setActiveTabId(newActiveTab.id);
          onTabChange?.(newActiveTab.id, newActiveTab.indexName);
        } else {
          setActiveTabId(null);
        }
      }
      
      return filtered;
    });

    onTabClose?.(tabId, tabToClose.indexName);
    return true;
  }, [tabs, activeTabId, onTabChange, onTabClose]);

  // 切换标签页
  const switchTab = useCallback((tabId: string) => {
    const tab = tabs.find(t => t.id === tabId);
    if (!tab) return;

    setTabs(prev => prev.map(t => ({ ...t, isActive: t.id === tabId })));
    setActiveTabId(tabId);
    onTabChange?.(tabId, tab.indexName);
  }, [tabs, onTabChange]);

  // 更新标签页
  const updateTab = useCallback((tabId: string, updates: Partial<TabInfo>) => {
    setTabs(prev => prev.map(tab => 
      tab.id === tabId ? { ...tab, ...updates } : tab
    ));
  }, []);

  // 刷新标签页
  const refreshTab = useCallback(async (tabId?: string): Promise<void> => {
    const targetTabId = tabId || activeTabId;
    if (!targetTabId || !dataLoader) return;

    const tab = tabs.find(t => t.id === targetTabId);
    if (!tab) return;

    try {
      const data = await switchOptimizer.switchIndex(
        tab.indexName,
        dataLoader,
        (stage, progress) => {
          console.log(`刷新标签页 ${tab.indexName}: ${stage} - ${progress}%`);
        }
      );

      updateTab(targetTabId, {
        searchResults: data,
        lastUpdated: new Date()
      });

      success(`标签页 ${tab.indexName} 已刷新`);
    } catch (err) {
      error(`刷新标签页 ${tab.indexName} 失败: ${err instanceof Error ? err.message : '未知错误'}`);
    }
  }, [activeTabId, tabs, dataLoader, switchOptimizer, updateTab, success, error]);

  // 刷新所有标签页
  const refreshAllTabs = useCallback(async (): Promise<void> => {
    if (!dataLoader) return;

    const refreshPromises = tabs.map(tab => refreshTab(tab.id));
    await Promise.allSettled(refreshPromises);
    success('所有标签页已刷新');
  }, [tabs, refreshTab, dataLoader, success]);

  // 关闭所有标签页
  const closeAllTabs = useCallback(async (): Promise<void> => {
    const hasUnsavedChanges = tabs.some(tab => tab.hasUnsavedChanges);
    if (hasUnsavedChanges) {
      const confirmed = confirm('有标签页包含未保存的更改，确定要关闭所有标签页吗？');
      if (!confirmed) return;
    }

    setTabs([]);
    setActiveTabId(null);
    success('已关闭所有标签页');
  }, [tabs, success]);

  // 复制标签页
  const duplicateTab = useCallback(async (tabId: string): Promise<void> => {
    const tab = tabs.find(t => t.id === tabId);
    if (!tab) return;

    await createTab(tab.indexName, {
      ...tab,
      title: `${tab.title} (副本)`,
      hasUnsavedChanges: false
    });
  }, [tabs, createTab]);

  // 根据索引名获取标签页
  const getTabByIndex = useCallback((indexName: string): TabInfo | null => {
    return tabs.find(tab => tab.indexName === indexName) || null;
  }, [tabs]);

  // 检查索引是否已打开
  const isTabOpen = useCallback((indexName: string): boolean => {
    return tabs.some(tab => tab.indexName === indexName);
  }, [tabs]);

  // 获取性能统计
  const getPerformanceStats = useCallback(() => {
    return switchOptimizer.getPerformanceStats();
  }, [switchOptimizer]);

  return {
    tabs,
    activeTabId,
    activeTab,
    createTab,
    closeTab,
    switchTab,
    updateTab,
    refreshTab,
    refreshAllTabs,
    closeAllTabs,
    duplicateTab,
    getTabByIndex,
    isTabOpen,
    getPerformanceStats
  };
};

export default useMultiTabDataManager;
