import React, { useState, useEffect } from 'react';
import { 
  ArrowR<PERSON>, 
  BarChart3, 
  Clock, 
  CheckCircle, 
  Info,
  X,
  RefreshCw
} from 'lucide-react';
import { useNavigationMigration } from '../../utils/navigationMigration';

interface MigrationStatusProps {
  className?: string;
  showDetails?: boolean;
}

export const MigrationStatus: React.FC<MigrationStatusProps> = ({
  className = '',
  showDetails = false
}) => {
  const { getStats, isUserAdapted } = useNavigationMigration();
  const [stats, setStats] = useState(getStats());
  const [isExpanded, setIsExpanded] = useState(showDetails);
  const [showNotification, setShowNotification] = useState(false);

  useEffect(() => {
    // 定期更新统计信息
    const interval = setInterval(() => {
      setStats(getStats());
    }, 5000);

    // 检查是否需要显示迁移通知
    if (stats.totalMigrations > 0 && !isUserAdapted()) {
      setShowNotification(true);
    }

    return () => clearInterval(interval);
  }, [stats.totalMigrations]);

  const handleRefresh = () => {
    setStats(getStats());
  };

  const handleDismissNotification = () => {
    setShowNotification(false);
  };

  if (stats.totalMigrations === 0 && !showDetails) {
    return null;
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* 迁移通知 */}
      {showNotification && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 p-4 mb-4">
          <div className="flex items-start">
            <Info className="w-5 h-5 text-blue-400 mt-0.5 mr-3" />
            <div className="flex-1">
              <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                界面已升级
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                索引管理和数据操作已整合为统一的数据管理界面，提供更流畅的使用体验。
              </p>
            </div>
            <button
              onClick={handleDismissNotification}
              className="text-blue-400 hover:text-blue-600 dark:hover:text-blue-200"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* 迁移状态头部 */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <ArrowRight className="w-5 h-5 text-blue-500" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                导航迁移状态
              </h3>
            </div>
            {isUserAdapted() && (
              <div className="flex items-center space-x-1 text-green-600 dark:text-green-400">
                <CheckCircle className="w-4 h-4" />
                <span className="text-sm font-medium">已适应</span>
              </div>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleRefresh}
              className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              title="刷新统计"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
            >
              {isExpanded ? '收起' : '展开'}
            </button>
          </div>
        </div>
      </div>

      {/* 基础统计 */}
      <div className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {stats.totalMigrations}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              总迁移次数
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {Object.keys(stats.migrationsByFromView).length}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              迁移页面数
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {stats.recentMigrations.length}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              最近迁移
            </div>
          </div>
        </div>
      </div>

      {/* 详细信息 */}
      {isExpanded && (
        <div className="border-t border-gray-200 dark:border-gray-700">
          {/* 按来源统计 */}
          <div className="p-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center">
              <BarChart3 className="w-4 h-4 mr-2" />
              按来源统计
            </h4>
            <div className="space-y-2">
              {Object.entries(stats.migrationsBySource).map(([source, count]) => (
                <div key={source} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                    {source === 'auto' ? '自动' : source === 'manual' ? '手动' : '回退'}
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full"
                        style={{ width: `${(count / stats.totalMigrations) * 100}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900 dark:text-white w-8 text-right">
                      {count}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 按页面统计 */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
              按页面统计
            </h4>
            <div className="space-y-2">
              {Object.entries(stats.migrationsByFromView).map(([view, count]) => (
                <div key={view} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {view === 'indices' ? '索引管理' : view === 'data' ? '数据操作' : view}
                  </span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {count} 次
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* 最近迁移记录 */}
          {stats.recentMigrations.length > 0 && (
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                <Clock className="w-4 h-4 mr-2" />
                最近迁移记录
              </h4>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {stats.recentMigrations.map((migration, index) => (
                  <div key={index} className="flex items-center justify-between text-xs">
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-500 dark:text-gray-400">
                        {migration.fromView === 'indices' ? '索引' : '数据'}
                      </span>
                      <ArrowRight className="w-3 h-3 text-gray-400" />
                      <span className="text-gray-600 dark:text-gray-300">
                        统一管理
                      </span>
                    </div>
                    <span className="text-gray-400 dark:text-gray-500">
                      {migration.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MigrationStatus;
