/**
 * 数据状态同步服务
 * 负责协调不同组件间的状态同步，确保数据一致性
 */

import { useUnifiedDataStore, IndexInfo, TabInfo, DataOperationHistory } from '../stores/unifiedDataState';
import { getIndexDataCache } from './IndexDataCache';
import IndexSwitchOptimizer from './IndexSwitchOptimizer';

// 同步事件类型
export type SyncEventType = 
  | 'index-selected'
  | 'index-updated'
  | 'index-favorite-changed'
  | 'index-pinned-changed'
  | 'tab-created'
  | 'tab-updated'
  | 'tab-closed'
  | 'data-refreshed'
  | 'operation-completed';

// 同步事件数据
export interface SyncEvent {
  type: SyncEventType;
  payload: any;
  timestamp: Date;
  source: string;
}

// 同步监听器
export type SyncListener = (event: SyncEvent) => void;

// 同步配置
export interface SyncConfig {
  enableRealTimeSync: boolean;
  syncInterval: number;
  maxRetries: number;
  retryDelay: number;
  enableConflictResolution: boolean;
}

export class DataStateSyncService {
  private static instance: DataStateSyncService;
  private listeners = new Map<SyncEventType, Set<SyncListener>>();
  private syncQueue: SyncEvent[] = [];
  private isProcessing = false;
  private config: SyncConfig;
  private indexCache = getIndexDataCache();
  private switchOptimizer = IndexSwitchOptimizer.getInstance();

  private constructor(config: Partial<SyncConfig> = {}) {
    this.config = {
      enableRealTimeSync: true,
      syncInterval: 1000,
      maxRetries: 3,
      retryDelay: 1000,
      enableConflictResolution: true,
      ...config
    };

    this.startSyncProcessor();
    this.setupStoreSubscriptions();
  }

  public static getInstance(config?: Partial<SyncConfig>): DataStateSyncService {
    if (!DataStateSyncService.instance) {
      DataStateSyncService.instance = new DataStateSyncService(config);
    }
    return DataStateSyncService.instance;
  }

  /**
   * 添加同步事件监听器
   */
  public addEventListener(type: SyncEventType, listener: SyncListener): void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set());
    }
    this.listeners.get(type)!.add(listener);
  }

  /**
   * 移除同步事件监听器
   */
  public removeEventListener(type: SyncEventType, listener: SyncListener): void {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  /**
   * 触发同步事件
   */
  public emit(type: SyncEventType, payload: any, source = 'unknown'): void {
    const event: SyncEvent = {
      type,
      payload,
      timestamp: new Date(),
      source
    };

    if (this.config.enableRealTimeSync) {
      this.processEventImmediately(event);
    } else {
      this.syncQueue.push(event);
    }
  }

  /**
   * 同步索引选择状态
   */
  public async syncIndexSelection(indexName: string, source = 'user'): Promise<void> {
    try {
      const store = useUnifiedDataStore.getState();
      
      // 更新选中索引
      store.setSelectedIndex(indexName);
      
      // 预加载索引数据
      await this.switchOptimizer.switchIndex(
        indexName,
        async (index) => {
          const cachedData = await this.indexCache.get(index);
          if (cachedData) return cachedData;
          
          // 模拟数据加载
          return { documents: [], total: 0, took: 50 };
        }
      );

      // 触发同步事件
      this.emit('index-selected', { indexName }, source);
      
    } catch (error) {
      console.error('索引选择同步失败:', error);
      throw error;
    }
  }

  /**
   * 同步索引信息更新
   */
  public syncIndexUpdate(indexName: string, updates: Partial<IndexInfo>, source = 'system'): void {
    const store = useUnifiedDataStore.getState();
    
    // 更新索引信息
    store.updateIndex(indexName, updates);
    
    // 更新缓存中的元数据
    this.indexCache.updateMetadata(indexName, updates);
    
    // 触发同步事件
    this.emit('index-updated', { indexName, updates }, source);
  }

  /**
   * 同步收藏状态变更
   */
  public syncFavoriteChange(indexName: string, isFavorite: boolean, source = 'user'): void {
    const store = useUnifiedDataStore.getState();
    
    if (isFavorite) {
      store.addToFavorites(indexName);
    } else {
      store.removeFromFavorites(indexName);
    }
    
    // 触发同步事件
    this.emit('index-favorite-changed', { indexName, isFavorite }, source);
  }

  /**
   * 同步置顶状态变更
   */
  public syncPinnedChange(indexName: string, isPinned: boolean, source = 'user'): void {
    const store = useUnifiedDataStore.getState();
    
    if (isPinned) {
      store.addToPinned(indexName);
    } else {
      store.removeFromPinned(indexName);
    }
    
    // 触发同步事件
    this.emit('index-pinned-changed', { indexName, isPinned }, source);
  }

  /**
   * 同步标签页创建
   */
  public syncTabCreation(tab: Omit<TabInfo, 'id'>, source = 'user'): string {
    const store = useUnifiedDataStore.getState();
    
    // 创建标签页
    const tabId = store.createTab(tab);
    
    // 触发同步事件
    this.emit('tab-created', { tabId, tab }, source);
    
    return tabId;
  }

  /**
   * 同步标签页更新
   */
  public syncTabUpdate(tabId: string, updates: Partial<TabInfo>, source = 'user'): void {
    const store = useUnifiedDataStore.getState();
    
    // 更新标签页
    store.updateTab(tabId, updates);
    
    // 触发同步事件
    this.emit('tab-updated', { tabId, updates }, source);
  }

  /**
   * 同步标签页关闭
   */
  public syncTabClose(tabId: string, source = 'user'): void {
    const store = useUnifiedDataStore.getState();
    
    // 关闭标签页
    store.closeTab(tabId);
    
    // 触发同步事件
    this.emit('tab-closed', { tabId }, source);
  }

  /**
   * 同步数据刷新
   */
  public async syncDataRefresh(indexName?: string, source = 'user'): Promise<void> {
    const store = useUnifiedDataStore.getState();
    
    try {
      store.setLoading(true);
      store.setError(null);
      
      if (indexName) {
        // 刷新特定索引
        await this.refreshIndexData(indexName);
      } else {
        // 刷新所有索引
        await this.refreshAllIndices();
      }
      
      store.setLastRefresh(new Date());
      
      // 触发同步事件
      this.emit('data-refreshed', { indexName }, source);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '数据刷新失败';
      store.setError(errorMessage);
      throw error;
    } finally {
      store.setLoading(false);
    }
  }

  /**
   * 同步操作完成
   */
  public syncOperationComplete(operation: Omit<DataOperationHistory, 'id'>, source = 'system'): void {
    const store = useUnifiedDataStore.getState();
    
    // 添加操作历史
    store.addOperationHistory(operation);
    
    // 触发同步事件
    this.emit('operation-completed', { operation }, source);
  }

  /**
   * 获取同步状态
   */
  public getSyncStatus(): {
    isProcessing: boolean;
    queueSize: number;
    lastSyncTime: Date | null;
    syncError: string | null;
  } {
    const store = useUnifiedDataStore.getState();
    return {
      isProcessing: this.isProcessing,
      queueSize: this.syncQueue.length,
      lastSyncTime: store.syncState.lastSyncTime,
      syncError: store.syncState.syncError
    };
  }

  /**
   * 强制同步所有状态
   */
  public async forceSyncAll(): Promise<void> {
    const store = useUnifiedDataStore.getState();
    
    try {
      store.setSyncState({ syncInProgress: true, syncError: null });
      
      // 同步所有待处理的变更
      await this.processSyncQueue();
      
      // 刷新所有数据
      await this.refreshAllIndices();
      
      store.setSyncState({ 
        syncInProgress: false, 
        lastSyncTime: new Date(),
        syncError: null 
      });
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '同步失败';
      store.setSyncState({ 
        syncInProgress: false, 
        syncError: errorMessage 
      });
      throw error;
    }
  }

  /**
   * 设置同步配置
   */
  public updateConfig(config: Partial<SyncConfig>): void {
    this.config = { ...this.config, ...config };
  }

  // 私有方法

  /**
   * 立即处理同步事件
   */
  private processEventImmediately(event: SyncEvent): void {
    const listeners = this.listeners.get(event.type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error(`同步事件监听器错误 (${event.type}):`, error);
        }
      });
    }
  }

  /**
   * 启动同步处理器
   */
  private startSyncProcessor(): void {
    setInterval(() => {
      if (!this.isProcessing && this.syncQueue.length > 0) {
        this.processSyncQueue();
      }
    }, this.config.syncInterval);
  }

  /**
   * 处理同步队列
   */
  private async processSyncQueue(): Promise<void> {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    
    try {
      while (this.syncQueue.length > 0) {
        const event = this.syncQueue.shift()!;
        this.processEventImmediately(event);
      }
    } catch (error) {
      console.error('同步队列处理错误:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 设置Store订阅
   */
  private setupStoreSubscriptions(): void {
    // 监听状态变化并触发相应的同步事件
    useUnifiedDataStore.subscribe(
      (state) => state.selectedIndex,
      (selectedIndex, previousSelectedIndex) => {
        if (selectedIndex !== previousSelectedIndex && selectedIndex) {
          this.emit('index-selected', { indexName: selectedIndex }, 'store');
        }
      }
    );
  }

  /**
   * 刷新索引数据
   */
  private async refreshIndexData(indexName: string): Promise<void> {
    // 清除缓存并重新加载
    this.indexCache.delete(indexName);
    
    // 使用优化器重新加载数据
    await this.switchOptimizer.switchIndex(
      indexName,
      async (index) => {
        // 模拟数据加载
        const mockData = { documents: [], total: 0, took: 50 };
        this.indexCache.set(index, mockData);
        return mockData;
      }
    );
  }

  /**
   * 刷新所有索引
   */
  private async refreshAllIndices(): Promise<void> {
    const store = useUnifiedDataStore.getState();
    const indices = store.indices;
    
    // 并行刷新所有索引
    const refreshPromises = indices.map(index => 
      this.refreshIndexData(index.name).catch(error => {
        console.warn(`刷新索引 ${index.name} 失败:`, error);
      })
    );
    
    await Promise.allSettled(refreshPromises);
  }
}

export default DataStateSyncService;
