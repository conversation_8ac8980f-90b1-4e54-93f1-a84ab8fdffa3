"use strict";
(global["webpackChunkes_client"] = global["webpackChunkes_client"] || []).push([["src_renderer_components_Monitor_ClusterOverview_tsx"],{

/***/ "./node_modules/lucide-react/dist/esm/icons/activity.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/activity.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Activity)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const Activity = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("Activity", [
  ["path", { d: "M22 12h-4l-3 9L9 3l-3 9H2", key: "d5dnw9" }]
]);


//# sourceMappingURL=activity.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/bell-off.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/bell-off.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ BellOff)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const BellOff = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("BellOff", [
  ["path", { d: "M8.7 3A6 6 0 0 1 18 8a21.3 21.3 0 0 0 .6 5", key: "o7mx20" }],
  ["path", { d: "M17 17H3s3-2 3-9a4.67 4.67 0 0 1 .3-1.7", key: "16f1lm" }],
  ["path", { d: "M10.3 21a1.94 1.94 0 0 0 3.4 0", key: "qgo35s" }],
  ["path", { d: "m2 2 20 20", key: "1ooewy" }]
]);


//# sourceMappingURL=bell-off.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/cpu.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/cpu.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Cpu)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const Cpu = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("Cpu", [
  [
    "rect",
    { x: "4", y: "4", width: "16", height: "16", rx: "2", key: "1vbyd7" }
  ],
  ["rect", { x: "9", y: "9", width: "6", height: "6", key: "o3kz5p" }],
  ["path", { d: "M15 2v2", key: "13l42r" }],
  ["path", { d: "M15 20v2", key: "15mkzm" }],
  ["path", { d: "M2 15h2", key: "1gxd5l" }],
  ["path", { d: "M2 9h2", key: "1bbxkp" }],
  ["path", { d: "M20 15h2", key: "19e6y8" }],
  ["path", { d: "M20 9h2", key: "19tzq7" }],
  ["path", { d: "M9 2v2", key: "165o2o" }],
  ["path", { d: "M9 20v2", key: "i2bqo8" }]
]);


//# sourceMappingURL=cpu.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/hard-drive.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/hard-drive.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ HardDrive)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const HardDrive = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("HardDrive", [
  ["line", { x1: "22", x2: "2", y1: "12", y2: "12", key: "1y58io" }],
  [
    "path",
    {
      d: "M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",
      key: "oot6mr"
    }
  ],
  ["line", { x1: "6", x2: "6.01", y1: "16", y2: "16", key: "sgf278" }],
  ["line", { x1: "10", x2: "10.01", y1: "16", y2: "16", key: "1l4acy" }]
]);


//# sourceMappingURL=hard-drive.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/memory-stick.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/memory-stick.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ MemoryStick)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const MemoryStick = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("MemoryStick", [
  ["path", { d: "M6 19v-3", key: "1nvgqn" }],
  ["path", { d: "M10 19v-3", key: "iu8nkm" }],
  ["path", { d: "M14 19v-3", key: "kcehxu" }],
  ["path", { d: "M18 19v-3", key: "1vh91z" }],
  ["path", { d: "M8 11V9", key: "63erz4" }],
  ["path", { d: "M16 11V9", key: "fru6f3" }],
  ["path", { d: "M12 11V9", key: "ha00sb" }],
  ["path", { d: "M2 15h20", key: "16ne18" }],
  [
    "path",
    {
      d: "M2 7a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v1.1a2 2 0 0 0 0 3.837V17a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-5.1a2 2 0 0 0 0-3.837Z",
      key: "lhddv3"
    }
  ]
]);


//# sourceMappingURL=memory-stick.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/minus.mjs":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/minus.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Minus)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const Minus = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("Minus", [
  ["path", { d: "M5 12h14", key: "1ays0h" }]
]);


//# sourceMappingURL=minus.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/server.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/server.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Server)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const Server = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("Server", [
  [
    "rect",
    {
      width: "20",
      height: "8",
      x: "2",
      y: "2",
      rx: "2",
      ry: "2",
      key: "ngkwjq"
    }
  ],
  [
    "rect",
    {
      width: "20",
      height: "8",
      x: "2",
      y: "14",
      rx: "2",
      ry: "2",
      key: "iecqi9"
    }
  ],
  ["line", { x1: "6", x2: "6.01", y1: "6", y2: "6", key: "16zg32" }],
  ["line", { x1: "6", x2: "6.01", y1: "18", y2: "18", key: "nzw8ys" }]
]);


//# sourceMappingURL=server.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/trending-down.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trending-down.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ TrendingDown)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const TrendingDown = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("TrendingDown", [
  ["polyline", { points: "22 17 13.5 8.5 8.5 13.5 2 7", key: "1r2t7k" }],
  ["polyline", { points: "16 17 22 17 22 11", key: "11uiuu" }]
]);


//# sourceMappingURL=trending-down.mjs.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/users.mjs":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/users.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Users)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ "./node_modules/lucide-react/dist/esm/createLucideIcon.mjs");
/**
 * lucide-react v0.0.1 - ISC
 */



const Users = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__["default"])("Users", [
  ["path", { d: "M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2", key: "1yyitq" }],
  ["circle", { cx: "9", cy: "7", r: "4", key: "nufk8" }],
  ["path", { d: "M22 21v-2a4 4 0 0 0-3-3.87", key: "kshegd" }],
  ["path", { d: "M16 3.13a4 4 0 0 1 0 7.75", key: "1da9ce" }]
]);


//# sourceMappingURL=users.mjs.map


/***/ }),

/***/ "./src/renderer/components/Monitor/AlertSystem.tsx":
/*!*********************************************************!*\
  !*** ./src/renderer/components/Monitor/AlertSystem.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AlertSystem: () => (/* binding */ AlertSystem)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _services_monitoring__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../services/monitoring */ "./src/renderer/services/monitoring.ts");
/* harmony import */ var _UI_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../UI/Card */ "./src/renderer/components/UI/Card.tsx");
/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/cn */ "./src/renderer/utils/cn.ts");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/alert-circle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/alert-triangle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/bell-off.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/bell.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/check-circle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/clock.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/info.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/settings.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/x.mjs");






const severityConfig = {
    low: {
        icon: lucide_react__WEBPACK_IMPORTED_MODULE_11__["default"],
        color: 'text-blue-600 dark:text-blue-400',
        bgColor: 'bg-blue-50 dark:bg-blue-900/20',
        borderColor: 'border-blue-200 dark:border-blue-800',
    },
    medium: {
        icon: lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"],
        color: 'text-yellow-600 dark:text-yellow-400',
        bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
        borderColor: 'border-yellow-200 dark:border-yellow-800',
    },
    high: {
        icon: lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"],
        color: 'text-orange-600 dark:text-orange-400',
        bgColor: 'bg-orange-50 dark:bg-orange-900/20',
        borderColor: 'border-orange-200 dark:border-orange-800',
    },
    critical: {
        icon: lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"],
        color: 'text-red-600 dark:text-red-400',
        bgColor: 'bg-red-50 dark:bg-red-900/20',
        borderColor: 'border-red-200 dark:border-red-800',
    },
};
const AlertSystem = ({ className }) => {
    const [alerts, setAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
    const [alertRules, setAlertRules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('active');
    const monitoringService = _services_monitoring__WEBPACK_IMPORTED_MODULE_2__.MonitoringService.getInstance();
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
        // Load initial data
        setAlerts(monitoringService.getAllAlerts());
        setAlertRules(monitoringService.getAlertRules());
        // Subscribe to new alerts
        const unsubscribe = monitoringService.addAlertCallback((newAlert) => {
            setAlerts(prev => [newAlert, ...prev]);
            // Show browser notification for critical alerts
            if (newAlert.severity === 'critical' && 'Notification' in window) {
                if (Notification.permission === 'granted') {
                    new Notification(`ES 监控警告: ${newAlert.ruleName}`, {
                        body: newAlert.message,
                        icon: '/favicon.ico',
                    });
                }
                else if (Notification.permission !== 'denied') {
                    Notification.requestPermission().then(permission => {
                        if (permission === 'granted') {
                            new Notification(`ES 监控警告: ${newAlert.ruleName}`, {
                                body: newAlert.message,
                                icon: '/favicon.ico',
                            });
                        }
                    });
                }
            }
        });
        return unsubscribe;
    }, []);
    const filteredAlerts = alerts.filter(alert => {
        switch (filter) {
            case 'active':
                return !alert.acknowledged && !alert.resolvedAt;
            case 'acknowledged':
                return alert.acknowledged || alert.resolvedAt;
            default:
                return true;
        }
    });
    const activeAlertsCount = alerts.filter(a => !a.acknowledged && !a.resolvedAt).length;
    const handleAcknowledge = (alertId) => {
        monitoringService.acknowledgeAlert(alertId);
        setAlerts(prev => prev.map(alert => alert.id === alertId ? { ...alert, acknowledged: true } : alert));
    };
    const handleResolve = (alertId) => {
        monitoringService.resolveAlert(alertId);
        setAlerts(prev => prev.map(alert => alert.id === alertId ? { ...alert, resolvedAt: Date.now() } : alert));
    };
    const handleToggleRule = (ruleId, enabled) => {
        monitoringService.updateAlertRule(ruleId, { enabled });
        setAlertRules(prev => prev.map(rule => rule.id === ruleId ? { ...rule, enabled } : rule));
    };
    const formatTimestamp = (timestamp) => {
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now.getTime() - timestamp;
        const diffMins = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);
        if (diffMins < 1)
            return '刚刚';
        if (diffMins < 60)
            return `${diffMins} 分钟前`;
        if (diffHours < 24)
            return `${diffHours} 小时前`;
        if (diffDays < 7)
            return `${diffDays} 天前`;
        return date.toLocaleDateString();
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('space-y-4', className), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100", children: "\u667A\u80FD\u8B66\u544A\u7CFB\u7EDF" }), activeAlertsCount > 0 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "px-2 py-1 text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 rounded-full", children: [activeAlertsCount, " \u4E2A\u6D3B\u8DC3\u8B66\u544A"] }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex bg-neutral-100 dark:bg-neutral-800 rounded-lg p-1", children: [
                                    { key: 'active', label: '活跃' },
                                    { key: 'acknowledged', label: '已处理' },
                                    { key: 'all', label: '全部' },
                                ].map(({ key, label }) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => setFilter(key), className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('px-3 py-1 text-sm font-medium rounded-md transition-colors', filter === key
                                        ? 'bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 shadow-sm'
                                        : 'text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100'), children: label }, key))) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => setShowSettings(!showSettings), className: "p-2 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 transition-colors", title: "\u8B66\u544A\u89C4\u5219\u8BBE\u7F6E", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_12__["default"], { size: 18 }) })] })] }), showSettings && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_3__.Card, { className: "p-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-md font-medium text-neutral-900 dark:text-neutral-100 mb-3", children: "\u8B66\u544A\u89C4\u5219\u914D\u7F6E" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-3", children: alertRules.map(rule => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between p-3 bg-neutral-50 dark:bg-neutral-800 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h5", { className: "font-medium text-neutral-900 dark:text-neutral-100", children: rule.name }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('px-2 py-1 text-xs font-medium rounded-full', severityConfig[rule.severity].bgColor, severityConfig[rule.severity].color), children: rule.severity })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-600 dark:text-neutral-400 mt-1", children: rule.description })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => handleToggleRule(rule.id, !rule.enabled), className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('p-2 rounded-lg transition-colors', rule.enabled
                                        ? 'text-green-600 hover:bg-green-50 dark:text-green-400 dark:hover:bg-green-900/20'
                                        : 'text-neutral-400 hover:bg-neutral-100 dark:hover:bg-neutral-700'), title: rule.enabled ? '禁用规则' : '启用规则', children: rule.enabled ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_8__["default"], { size: 18 }) : (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"], { size: 18 }) })] }, rule.id))) })] })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-3", children: filteredAlerts.length === 0 ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_3__.Card, { className: "p-6 text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"], { className: "mx-auto mb-3 text-green-500", size: 48 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-2", children: filter === 'active' ? '没有活跃警告' : '没有警告记录' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-neutral-600 dark:text-neutral-400", children: filter === 'active'
                                ? '集群运行状态良好，所有指标都在正常范围内。'
                                : '当前筛选条件下没有找到警告记录。' })] })) : (filteredAlerts.map(alert => {
                    const config = severityConfig[alert.severity];
                    const IconComponent = config.icon;
                    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_3__.Card, { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('p-4 border-l-4', config.bgColor, config.borderColor, alert.acknowledged || alert.resolvedAt ? 'opacity-60' : ''), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start gap-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconComponent, { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('mt-0.5 flex-shrink-0', config.color), size: 20 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1 min-w-0", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-2 mb-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "font-medium text-neutral-900 dark:text-neutral-100", children: alert.ruleName }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('px-2 py-1 text-xs font-medium rounded-full', config.bgColor, config.color), children: alert.severity }), alert.acknowledged && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded-full", children: "\u5DF2\u786E\u8BA4" })), alert.resolvedAt && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full", children: "\u5DF2\u89E3\u51B3" }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-neutral-700 dark:text-neutral-300 mb-2", children: alert.message }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-4 text-sm text-neutral-500 dark:text-neutral-400", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_10__["default"], { size: 14 }), formatTimestamp(alert.timestamp)] }), alert.resolvedAt && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"], { size: 14 }), "\u89E3\u51B3\u4E8E ", formatTimestamp(alert.resolvedAt)] }))] })] }), !alert.acknowledged && !alert.resolvedAt && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => handleAcknowledge(alert.id), className: "p-1.5 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded transition-colors", title: "\u786E\u8BA4\u8B66\u544A", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"], { size: 16 }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => handleResolve(alert.id), className: "p-1.5 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded transition-colors", title: "\u6807\u8BB0\u4E3A\u5DF2\u89E3\u51B3", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_13__["default"], { size: 16 }) })] }))] }) }, alert.id));
                })) })] }));
};


/***/ }),

/***/ "./src/renderer/components/Monitor/CircularProgress.tsx":
/*!**************************************************************!*\
  !*** ./src/renderer/components/Monitor/CircularProgress.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CircularProgress: () => (/* binding */ CircularProgress)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/cn */ "./src/renderer/utils/cn.ts");



const CircularProgress = ({ value, size = 60, strokeWidth = 4, color = '#3b82f6', backgroundColor = '#e5e7eb', showValue = true, className, }) => {
    const radius = (size - strokeWidth) / 2;
    const circumference = radius * 2 * Math.PI;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (value / 100) * circumference;
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_2__.cn)('relative inline-flex items-center justify-center', className), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("svg", { width: size, height: size, className: "transform -rotate-90", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("circle", { cx: size / 2, cy: size / 2, r: radius, stroke: backgroundColor, strokeWidth: strokeWidth, fill: "none" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("circle", { cx: size / 2, cy: size / 2, r: radius, stroke: color, strokeWidth: strokeWidth, fill: "none", strokeDasharray: strokeDasharray, strokeDashoffset: strokeDashoffset, strokeLinecap: "round", className: "transition-all duration-300 ease-out" })] }), showValue && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute inset-0 flex items-center justify-center", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "text-sm font-medium text-neutral-900 dark:text-neutral-100", children: [Math.round(value), "%"] }) }))] }));
};


/***/ }),

/***/ "./src/renderer/components/Monitor/ClusterOverview.tsx":
/*!*************************************************************!*\
  !*** ./src/renderer/components/Monitor/ClusterOverview.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ClusterOverview: () => (/* binding */ ClusterOverview)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _services_elasticsearch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../services/elasticsearch */ "./src/renderer/services/elasticsearch.ts");
/* harmony import */ var _services_monitoring__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../services/monitoring */ "./src/renderer/services/monitoring.ts");
/* harmony import */ var _UI_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../UI/Card */ "./src/renderer/components/UI/Card.tsx");
/* harmony import */ var _UI_Spinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../UI/Spinner */ "./src/renderer/components/UI/Spinner.tsx");
/* harmony import */ var _HealthIndicator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./HealthIndicator */ "./src/renderer/components/Monitor/HealthIndicator.tsx");
/* harmony import */ var _MetricCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./MetricCard */ "./src/renderer/components/Monitor/MetricCard.tsx");
/* harmony import */ var _NodesMonitor__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./NodesMonitor */ "./src/renderer/components/Monitor/NodesMonitor.tsx");
/* harmony import */ var _CircularProgress__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./CircularProgress */ "./src/renderer/components/Monitor/CircularProgress.tsx");
/* harmony import */ var _AlertSystem__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./AlertSystem */ "./src/renderer/components/Monitor/AlertSystem.tsx");
/* harmony import */ var _PerformanceChart__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./PerformanceChart */ "./src/renderer/components/Monitor/PerformanceChart.tsx");
/* harmony import */ var _SystemHealthDashboard__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./SystemHealthDashboard */ "./src/renderer/components/Monitor/SystemHealthDashboard.tsx");
/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../utils/cn */ "./src/renderer/utils/cn.ts");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/activity.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/alert-triangle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/bar-chart-3.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/clock.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/database.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/file-text.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/hard-drive.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/server.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/shield.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/trending-up.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/users.mjs");















const formatBytes = (bytes) => {
    if (bytes === 0)
        return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
const formatNumber = (num) => {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
};
const ClusterOverview = ({ className, }) => {
    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);
    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');
    const esService = _services_elasticsearch__WEBPACK_IMPORTED_MODULE_2__.ElasticsearchService.getInstance();
    const monitoringService = _services_monitoring__WEBPACK_IMPORTED_MODULE_3__.MonitoringService.getInstance();
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
        const loadInitialData = async () => {
            if (!esService.isConnected()) {
                setError('未连接到 Elasticsearch 集群');
                setLoading(false);
                return;
            }
            try {
                setLoading(true);
                setError(null);
                const [clusterHealth, nodesStats, clusterStats] = await Promise.all([
                    esService.getClusterHealth(),
                    esService.getNodesStats(),
                    esService.getClusterStats(),
                ]);
                const monitoringData = {
                    timestamp: Date.now(),
                    clusterHealth,
                    nodesStats,
                    clusterStats,
                };
                setData(monitoringData);
                setLastUpdated(new Date());
                // Add data to monitoring service for analysis
                monitoringService.addMonitoringData(monitoringData);
            }
            catch (err) {
                console.error('Failed to load cluster data:', err);
                setError(err instanceof Error ? err.message : '加载集群数据失败');
            }
            finally {
                setLoading(false);
            }
        };
        loadInitialData();
        // Set up real-time monitoring
        const unsubscribe = esService.addMonitoringCallback((monitoringData) => {
            setData(monitoringData);
            setLastUpdated(new Date());
            setError(null);
            // Add data to monitoring service for analysis
            monitoringService.addMonitoringData(monitoringData);
        });
        // Start monitoring if not already started
        if (!esService.isMonitoring()) {
            esService.startRealTimeMonitoring(5000); // Update every 5 seconds
        }
        return () => {
            unsubscribe();
        };
    }, []);
    if (loading) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)('flex items-center justify-center p-8', className), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Spinner__WEBPACK_IMPORTED_MODULE_5__.Spinner, { size: "lg" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "ml-3 text-neutral-600 dark:text-neutral-300", children: "\u52A0\u8F7D\u96C6\u7FA4\u6570\u636E..." })] }));
    }
    if (error) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_4__.Card, { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)('p-6', className), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-3 text-red-600 dark:text-red-400", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_15__["default"], { size: 20 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: error })] }) }));
    }
    if (!data) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_4__.Card, { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)('p-6', className), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-center text-neutral-500 dark:text-neutral-400", children: "\u6682\u65E0\u96C6\u7FA4\u6570\u636E" }) }));
    }
    const { clusterHealth, nodesStats, clusterStats } = data;
    // Calculate aggregate metrics
    const totalCpuPercent = Object.values(nodesStats).reduce((sum, node) => sum + node.os.cpu.percent, 0) / Object.keys(nodesStats).length;
    const totalMemoryUsed = Object.values(nodesStats).reduce((sum, node) => sum + node.os.mem.used_in_bytes, 0);
    const totalMemoryTotal = Object.values(nodesStats).reduce((sum, node) => sum + node.os.mem.total_in_bytes, 0);
    const memoryPercent = (totalMemoryUsed / totalMemoryTotal) * 100;
    const totalJvmHeapUsed = Object.values(nodesStats).reduce((sum, node) => sum + node.jvm.mem.heap_used_in_bytes, 0);
    const totalJvmHeapMax = Object.values(nodesStats).reduce((sum, node) => sum + node.jvm.mem.heap_max_in_bytes, 0);
    const jvmHeapPercent = (totalJvmHeapUsed / totalJvmHeapMax) * 100;
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)('space-y-6', className), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-2xl font-bold text-neutral-900 dark:text-neutral-100", children: "\u96C6\u7FA4\u76D1\u63A7" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-500 dark:text-neutral-400", children: clusterHealth.cluster_name })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_HealthIndicator__WEBPACK_IMPORTED_MODULE_6__.HealthIndicator, { health: clusterHealth.status, size: "lg" }), lastUpdated && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-sm text-neutral-500 dark:text-neutral-400", children: ["\u6700\u540E\u66F4\u65B0: ", lastUpdated.toLocaleTimeString()] }))] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex bg-neutral-100 dark:bg-neutral-800 rounded-lg p-1", children: [
                    { key: 'overview', label: '概览', icon: lucide_react__WEBPACK_IMPORTED_MODULE_16__["default"] },
                    { key: 'performance', label: '性能分析', icon: lucide_react__WEBPACK_IMPORTED_MODULE_23__["default"] },
                    { key: 'alerts', label: '智能警告', icon: lucide_react__WEBPACK_IMPORTED_MODULE_15__["default"] },
                    { key: 'health', label: '系统健康', icon: lucide_react__WEBPACK_IMPORTED_MODULE_22__["default"] },
                ].map(({ key, label, icon: Icon }) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", { onClick: () => setActiveTab(key), className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)('flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-colors', activeTab === key
                        ? 'bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 shadow-sm'
                        : 'text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100'), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Icon, { size: 16 }), label] }, key))) }), activeTab === 'overview' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_MetricCard__WEBPACK_IMPORTED_MODULE_7__.MetricCard, { title: "\u8282\u70B9\u6570\u91CF", value: clusterHealth.number_of_nodes, subtitle: `${clusterHealth.number_of_data_nodes} 个数据节点`, icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_21__["default"], { size: 20 }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_MetricCard__WEBPACK_IMPORTED_MODULE_7__.MetricCard, { title: "\u7D22\u5F15\u6570\u91CF", value: clusterStats.indices.count, subtitle: `${formatNumber(clusterStats.indices.docs.count)} 个文档`, icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_18__["default"], { size: 20 }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_MetricCard__WEBPACK_IMPORTED_MODULE_7__.MetricCard, { title: "\u5206\u7247\u72B6\u6001", value: `${clusterHealth.active_shards}/${clusterHealth.active_shards + clusterHealth.unassigned_shards}`, subtitle: clusterHealth.unassigned_shards > 0
                                    ? `${clusterHealth.unassigned_shards} 个未分配`
                                    : '全部已分配', icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_19__["default"], { size: 20 }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_MetricCard__WEBPACK_IMPORTED_MODULE_7__.MetricCard, { title: "\u5B58\u50A8\u5927\u5C0F", value: formatBytes(clusterStats.indices.store.size_in_bytes), subtitle: "\u603B\u5B58\u50A8\u4F7F\u7528\u91CF", icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_20__["default"], { size: 20 }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_MetricCard__WEBPACK_IMPORTED_MODULE_7__.MetricCard, { title: "CPU \u4F7F\u7528\u7387", value: `${totalCpuPercent.toFixed(1)}%`, subtitle: "\u96C6\u7FA4\u5E73\u5747 CPU \u4F7F\u7528\u7387", icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_14__["default"], { size: 20 }), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_CircularProgress__WEBPACK_IMPORTED_MODULE_9__.CircularProgress, { value: totalCpuPercent, size: 60, color: totalCpuPercent > 80
                                        ? '#ef4444'
                                        : totalCpuPercent > 60
                                            ? '#f59e0b'
                                            : '#10b981' }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_MetricCard__WEBPACK_IMPORTED_MODULE_7__.MetricCard, { title: "\u5185\u5B58\u4F7F\u7528\u7387", value: `${memoryPercent.toFixed(1)}%`, subtitle: `${formatBytes(totalMemoryUsed)} / ${formatBytes(totalMemoryTotal)}`, icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_24__["default"], { size: 20 }), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_CircularProgress__WEBPACK_IMPORTED_MODULE_9__.CircularProgress, { value: memoryPercent, size: 60, color: memoryPercent > 80
                                        ? '#ef4444'
                                        : memoryPercent > 60
                                            ? '#f59e0b'
                                            : '#10b981' }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_MetricCard__WEBPACK_IMPORTED_MODULE_7__.MetricCard, { title: "JVM \u5806\u5185\u5B58", value: `${jvmHeapPercent.toFixed(1)}%`, subtitle: `${formatBytes(totalJvmHeapUsed)} / ${formatBytes(totalJvmHeapMax)}`, icon: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_17__["default"], { size: 20 }), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_CircularProgress__WEBPACK_IMPORTED_MODULE_9__.CircularProgress, { value: jvmHeapPercent, size: 60, color: jvmHeapPercent > 80
                                        ? '#ef4444'
                                        : jvmHeapPercent > 60
                                            ? '#f59e0b'
                                            : '#10b981' }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_4__.Card, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4", children: "\u96C6\u7FA4\u5065\u5EB7\u8BE6\u60C5" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-500 dark:text-neutral-400", children: "\u4E3B\u5206\u7247" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100", children: clusterHealth.active_primary_shards })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-500 dark:text-neutral-400", children: "\u6D3B\u8DC3\u5206\u7247" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100", children: clusterHealth.active_shards })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-500 dark:text-neutral-400", children: "\u91CD\u5B9A\u4F4D\u5206\u7247" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100", children: clusterHealth.relocating_shards })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-500 dark:text-neutral-400", children: "\u521D\u59CB\u5316\u5206\u7247" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100", children: clusterHealth.initializing_shards })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-500 dark:text-neutral-400", children: "\u672A\u5206\u914D\u5206\u7247" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_13__.cn)('text-lg font-semibold', clusterHealth.unassigned_shards > 0
                                                    ? 'text-red-600 dark:text-red-400'
                                                    : 'text-neutral-900 dark:text-neutral-100'), children: clusterHealth.unassigned_shards })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-500 dark:text-neutral-400", children: "\u5F85\u5904\u7406\u4EFB\u52A1" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100", children: clusterHealth.number_of_pending_tasks })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-500 dark:text-neutral-400", children: "\u5206\u7247\u5B8C\u6210\u5EA6" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100", children: [clusterHealth.active_shards_percent_as_number.toFixed(1), "%"] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-500 dark:text-neutral-400", children: "\u6700\u5927\u7B49\u5F85\u65F6\u95F4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100", children: [clusterHealth.task_max_waiting_in_queue_millis, "ms"] })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_NodesMonitor__WEBPACK_IMPORTED_MODULE_8__.NodesMonitor, { nodes: nodesStats })] })), activeTab === 'performance' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_PerformanceChart__WEBPACK_IMPORTED_MODULE_11__.PerformanceChart, {})), activeTab === 'alerts' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_AlertSystem__WEBPACK_IMPORTED_MODULE_10__.AlertSystem, {})), activeTab === 'health' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_SystemHealthDashboard__WEBPACK_IMPORTED_MODULE_12__.SystemHealthDashboard, {}))] }));
};


/***/ }),

/***/ "./src/renderer/components/Monitor/HealthIndicator.tsx":
/*!*************************************************************!*\
  !*** ./src/renderer/components/Monitor/HealthIndicator.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HealthIndicator: () => (/* binding */ HealthIndicator)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/cn */ "./src/renderer/utils/cn.ts");



const healthConfig = {
    green: {
        color: 'bg-green-500',
        textColor: 'text-green-600',
        label: '健康',
        description: '集群运行正常',
    },
    yellow: {
        color: 'bg-yellow-500',
        textColor: 'text-yellow-600',
        label: '警告',
        description: '集群存在警告',
    },
    red: {
        color: 'bg-red-500',
        textColor: 'text-red-600',
        label: '错误',
        description: '集群存在错误',
    },
};
const sizeConfig = {
    sm: {
        dot: 'w-2 h-2',
        text: 'text-xs',
    },
    md: {
        dot: 'w-3 h-3',
        text: 'text-sm',
    },
    lg: {
        dot: 'w-4 h-4',
        text: 'text-base',
    },
};
const HealthIndicator = ({ health, size = 'md', showText = true, className, }) => {
    const config = healthConfig[health];
    const sizeStyles = sizeConfig[size];
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_2__.cn)('flex items-center gap-2', className), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_2__.cn)('rounded-full flex-shrink-0', config.color, sizeStyles.dot), title: config.description }), showText && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_2__.cn)('font-medium', config.textColor, sizeStyles.text), children: config.label }))] }));
};


/***/ }),

/***/ "./src/renderer/components/Monitor/MetricCard.tsx":
/*!********************************************************!*\
  !*** ./src/renderer/components/Monitor/MetricCard.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MetricCard: () => (/* binding */ MetricCard)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _UI_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../UI/Card */ "./src/renderer/components/UI/Card.tsx");
/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/cn */ "./src/renderer/utils/cn.ts");




const MetricCard = ({ title, value, subtitle, icon, trend, className, children, }) => {
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.Card, { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_3__.cn)('p-6', className), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-2 mb-2", children: [icon && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-neutral-500 dark:text-neutral-400", children: icon })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-sm font-medium text-neutral-600 dark:text-neutral-300", children: title })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mb-1", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-2xl font-semibold text-neutral-900 dark:text-neutral-100", children: value }) }), subtitle && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-500 dark:text-neutral-400", children: subtitle })), trend && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-1 mt-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_3__.cn)('text-xs font-medium', trend.isPositive
                                        ? 'text-green-600 dark:text-green-400'
                                        : 'text-red-600 dark:text-red-400'), children: [trend.isPositive ? '+' : '', trend.value, "%"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-xs text-neutral-500 dark:text-neutral-400", children: "vs \u4E0A\u6B21\u66F4\u65B0" })] }))] }), children && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "ml-4", children: children })] }) }));
};


/***/ }),

/***/ "./src/renderer/components/Monitor/NodesMonitor.tsx":
/*!**********************************************************!*\
  !*** ./src/renderer/components/Monitor/NodesMonitor.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NodesMonitor: () => (/* binding */ NodesMonitor)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _UI_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../UI/Card */ "./src/renderer/components/UI/Card.tsx");
/* harmony import */ var _CircularProgress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CircularProgress */ "./src/renderer/components/Monitor/CircularProgress.tsx");
/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/cn */ "./src/renderer/utils/cn.ts");





const formatBytes = (bytes) => {
    if (bytes === 0)
        return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
const getRoleColor = (role) => {
    const roleColors = {
        master: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
        data: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
        ingest: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        ml: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
        coordinating_only: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
    };
    return (roleColors[role] ||
        'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200');
};
const NodesMonitor = ({ nodes, className, }) => {
    const nodeEntries = Object.entries(nodes);
    if (nodeEntries.length === 0) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.Card, { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('p-6', className), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-center text-neutral-500 dark:text-neutral-400", children: "\u6682\u65E0\u8282\u70B9\u6570\u636E" }) }));
    }
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('space-y-4', className), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("h3", { className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100", children: ["\u8282\u70B9\u76D1\u63A7 (", nodeEntries.length, " \u4E2A\u8282\u70B9)"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "grid gap-4", children: nodeEntries.map(([nodeId, node]) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_2__.Card, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start justify-between mb-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "font-medium text-neutral-900 dark:text-neutral-100", children: node.name }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-sm text-neutral-500 dark:text-neutral-400", children: [node.host, " (", node.ip, ")"] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex flex-wrap gap-1", children: node.roles.map(role => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('px-2 py-1 text-xs font-medium rounded-full', getRoleColor(role)), children: role }, role))) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_CircularProgress__WEBPACK_IMPORTED_MODULE_3__.CircularProgress, { value: node.os.cpu.percent, size: 50, color: node.os.cpu.percent > 80
                                                ? '#ef4444'
                                                : node.os.cpu.percent > 60
                                                    ? '#f59e0b'
                                                    : '#10b981' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-neutral-900 dark:text-neutral-100", children: "CPU \u4F7F\u7528\u7387" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-xs text-neutral-500 dark:text-neutral-400", children: ["\u8D1F\u8F7D: ", node.os.cpu.load_average?.['1m']?.toFixed(2) || 'N/A'] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_CircularProgress__WEBPACK_IMPORTED_MODULE_3__.CircularProgress, { value: node.os.mem.used_percent, size: 50, color: node.os.mem.used_percent > 80
                                                ? '#ef4444'
                                                : node.os.mem.used_percent > 60
                                                    ? '#f59e0b'
                                                    : '#10b981' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-neutral-900 dark:text-neutral-100", children: "\u5185\u5B58\u4F7F\u7528\u7387" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-xs text-neutral-500 dark:text-neutral-400", children: [formatBytes(node.os.mem.used_in_bytes), " /", ' ', formatBytes(node.os.mem.total_in_bytes)] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_CircularProgress__WEBPACK_IMPORTED_MODULE_3__.CircularProgress, { value: node.jvm.mem.heap_used_percent, size: 50, color: node.jvm.mem.heap_used_percent > 80
                                                ? '#ef4444'
                                                : node.jvm.mem.heap_used_percent > 60
                                                    ? '#f59e0b'
                                                    : '#10b981' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-neutral-900 dark:text-neutral-100", children: "JVM \u5806\u5185\u5B58" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-xs text-neutral-500 dark:text-neutral-400", children: [formatBytes(node.jvm.mem.heap_used_in_bytes), " /", ' ', formatBytes(node.jvm.mem.heap_max_in_bytes)] })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "mt-4 pt-4 border-t border-neutral-200 dark:border-neutral-700", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm font-medium text-neutral-900 dark:text-neutral-100", children: "\u78C1\u76D8\u4F7F\u7528\u60C5\u51B5" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "text-sm text-neutral-500 dark:text-neutral-400", children: [formatBytes(node.fs.total.total_in_bytes -
                                                    node.fs.total.available_in_bytes), ' ', "/ ", formatBytes(node.fs.total.total_in_bytes)] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mt-2 w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-blue-500 h-2 rounded-full transition-all duration-300", style: {
                                            width: `${((node.fs.total.total_in_bytes - node.fs.total.available_in_bytes) / node.fs.total.total_in_bytes) * 100}%`,
                                        } }) })] })] }, nodeId))) })] }));
};


/***/ }),

/***/ "./src/renderer/components/Monitor/PerformanceChart.tsx":
/*!**************************************************************!*\
  !*** ./src/renderer/components/Monitor/PerformanceChart.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PerformanceChart: () => (/* binding */ PerformanceChart)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _services_monitoring__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../services/monitoring */ "./src/renderer/services/monitoring.ts");
/* harmony import */ var _UI_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../UI/Card */ "./src/renderer/components/UI/Card.tsx");
/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/cn */ "./src/renderer/utils/cn.ts");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/activity.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/alert-triangle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/cpu.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/database.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/hard-drive.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/memory-stick.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/minus.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/trending-down.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/trending-up.mjs");






const metricConfig = {
    cpuUsage: {
        name: 'CPU 使用率',
        unit: '%',
        color: '#3b82f6',
        icon: lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"],
        threshold: { warning: 70, critical: 85 },
    },
    memoryUsage: {
        name: '内存使用率',
        unit: '%',
        color: '#10b981',
        icon: lucide_react__WEBPACK_IMPORTED_MODULE_10__["default"],
        threshold: { warning: 75, critical: 90 },
    },
    jvmHeapUsage: {
        name: 'JVM 堆内存',
        unit: '%',
        color: '#f59e0b',
        icon: lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"],
        threshold: { warning: 80, critical: 95 },
    },
    diskUsage: {
        name: '磁盘使用率',
        unit: '%',
        color: '#8b5cf6',
        icon: lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"],
        threshold: { warning: 80, critical: 90 },
    },
    activeShards: {
        name: '活跃分片',
        unit: '',
        color: '#06b6d4',
        icon: lucide_react__WEBPACK_IMPORTED_MODULE_8__["default"],
        threshold: { warning: 1000, critical: 2000 },
    },
    unassignedShards: {
        name: '未分配分片',
        unit: '',
        color: '#ef4444',
        icon: lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"],
        threshold: { warning: 1, critical: 5 },
    },
};
const PerformanceChart = ({ className, height = 300, }) => {
    const [selectedMetric, setSelectedMetric] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('cpuUsage');
    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1); // hours
    const [metrics, setMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
    const [trends, setTrends] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
    const monitoringService = _services_monitoring__WEBPACK_IMPORTED_MODULE_2__.MonitoringService.getInstance();
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
        const updateData = () => {
            setMetrics(monitoringService.getPerformanceMetrics(timeRange));
            setTrends(monitoringService.getTrendAnalysis());
        };
        updateData();
        const interval = setInterval(updateData, 30000); // Update every 30 seconds
        return () => clearInterval(interval);
    }, [timeRange]);
    const chartData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {
        return metrics.map(metric => ({
            timestamp: metric.timestamp,
            value: metric[selectedMetric],
            label: new Date(metric.timestamp).toLocaleTimeString(),
        }));
    }, [metrics, selectedMetric]);
    const currentTrend = trends.find(t => t.metric === selectedMetric);
    const config = metricConfig[selectedMetric];
    const IconComponent = config.icon;
    const renderMiniChart = (data) => {
        if (data.length < 2)
            return null;
        const maxValue = Math.max(...data.map(d => d.value));
        const minValue = Math.min(...data.map(d => d.value));
        const range = maxValue - minValue || 1;
        const points = data.map((point, index) => {
            const x = (index / (data.length - 1)) * 100;
            const y = 100 - ((point.value - minValue) / range) * 100;
            return `${x},${y}`;
        }).join(' ');
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("svg", { width: "100%", height: height, className: "overflow-visible", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("defs", { children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("pattern", { id: "grid", width: "20", height: "20", patternUnits: "userSpaceOnUse", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("path", { d: "M 20 0 L 0 0 0 20", fill: "none", stroke: "currentColor", strokeWidth: "0.5", opacity: "0.1" }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("rect", { width: "100%", height: "100%", fill: "url(#grid)" }), config.threshold && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("line", { x1: "0", y1: 100 - ((config.threshold.warning - minValue) / range) * 100 + '%', x2: "100%", y2: 100 - ((config.threshold.warning - minValue) / range) * 100 + '%', stroke: "#f59e0b", strokeWidth: "1", strokeDasharray: "5,5", opacity: "0.5" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("line", { x1: "0", y1: 100 - ((config.threshold.critical - minValue) / range) * 100 + '%', x2: "100%", y2: 100 - ((config.threshold.critical - minValue) / range) * 100 + '%', stroke: "#ef4444", strokeWidth: "1", strokeDasharray: "5,5", opacity: "0.5" })] })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("path", { d: `M 0,100 L ${points} L 100,100 Z`, fill: config.color, fillOpacity: "0.1" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("polyline", { points: points, fill: "none", stroke: config.color, strokeWidth: "2", strokeLinejoin: "round", strokeLinecap: "round" }), data.map((point, index) => {
                    const x = (index / (data.length - 1)) * 100;
                    const y = 100 - ((point.value - minValue) / range) * 100;
                    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("circle", { cx: x + '%', cy: y + '%', r: "3", fill: config.color, className: "opacity-0 hover:opacity-100 transition-opacity", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("title", { children: `${point.label}: ${point.value.toFixed(1)}${config.unit}` }) }, index));
                })] }));
    };
    const getCurrentValue = () => {
        if (chartData.length === 0)
            return 0;
        return chartData[chartData.length - 1].value;
    };
    const getValueStatus = (value) => {
        if (!config.threshold)
            return 'normal';
        if (value >= config.threshold.critical)
            return 'critical';
        if (value >= config.threshold.warning)
            return 'warning';
        return 'normal';
    };
    const currentValue = getCurrentValue();
    const valueStatus = getValueStatus(currentValue);
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('space-y-4', className), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100", children: "\u6027\u80FD\u8D8B\u52BF\u5206\u6790" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex bg-neutral-100 dark:bg-neutral-800 rounded-lg p-1", children: [
                            { value: 1, label: '1小时' },
                            { value: 6, label: '6小时' },
                            { value: 24, label: '24小时' },
                        ].map(({ value, label }) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => setTimeRange(value), className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('px-3 py-1 text-sm font-medium rounded-md transition-colors', timeRange === value
                                ? 'bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 shadow-sm'
                                : 'text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100'), children: label }, value))) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3", children: Object.entries(metricConfig).map(([key, config]) => {
                    const metricKey = key;
                    const isSelected = selectedMetric === metricKey;
                    const MetricIcon = config.icon;
                    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", { onClick: () => setSelectedMetric(metricKey), className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('p-3 rounded-lg border-2 transition-all text-left', isSelected
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                            : 'border-neutral-200 dark:border-neutral-700 hover:border-neutral-300 dark:hover:border-neutral-600'), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-2 mb-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(MetricIcon, { size: 16, style: { color: config.color } }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm font-medium text-neutral-900 dark:text-neutral-100", children: config.name })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-xs text-neutral-600 dark:text-neutral-400", children: "\u70B9\u51FB\u67E5\u770B\u8D8B\u52BF" })] }, key));
                }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_3__.Card, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between mb-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconComponent, { size: 24, style: { color: config.color } }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100", children: config.name }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('text-2xl font-bold', valueStatus === 'critical' ? 'text-red-600 dark:text-red-400' :
                                                            valueStatus === 'warning' ? 'text-yellow-600 dark:text-yellow-400' :
                                                                'text-neutral-900 dark:text-neutral-100'), children: [currentValue.toFixed(1), config.unit] }), currentTrend && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-1", children: [currentTrend.trend === 'increasing' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_13__["default"], { className: "text-red-500", size: 16 })) : currentTrend.trend === 'decreasing' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_12__["default"], { className: "text-green-500", size: 16 })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_11__["default"], { className: "text-neutral-500", size: 16 })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('text-sm font-medium', currentTrend.trend === 'increasing' ? 'text-red-600 dark:text-red-400' :
                                                                    currentTrend.trend === 'decreasing' ? 'text-green-600 dark:text-green-400' :
                                                                        'text-neutral-600 dark:text-neutral-400'), children: [currentTrend.changeRate > 0 ? '+' : '', currentTrend.changeRate.toFixed(1), "%/h"] })] }))] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('px-3 py-1 rounded-full text-sm font-medium', valueStatus === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                                    valueStatus === 'warning' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                                        'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'), children: valueStatus === 'critical' ? '严重' :
                                    valueStatus === 'warning' ? '警告' : '正常' })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "relative", children: chartData.length > 0 ? (renderMiniChart(chartData)) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center justify-center h-64 text-neutral-500 dark:text-neutral-400", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], { size: 48, className: "mx-auto mb-3 opacity-50" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { children: "\u6682\u65E0\u6570\u636E" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm", children: "\u7B49\u5F85\u76D1\u63A7\u6570\u636E\u6536\u96C6..." })] }) })) }), currentTrend && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "mt-4 p-4 bg-neutral-50 dark:bg-neutral-800 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h5", { className: "font-medium text-neutral-900 dark:text-neutral-100 mb-2", children: "\u8D8B\u52BF\u5206\u6790" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4 text-sm", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-neutral-600 dark:text-neutral-400", children: "\u8D8B\u52BF\u65B9\u5411:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-1 mt-1", children: [currentTrend.trend === 'increasing' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_13__["default"], { className: "text-red-500", size: 16 })) : currentTrend.trend === 'decreasing' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_12__["default"], { className: "text-green-500", size: 16 })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_11__["default"], { className: "text-neutral-500", size: 16 })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "font-medium text-neutral-900 dark:text-neutral-100", children: currentTrend.trend === 'increasing' ? '上升' :
                                                            currentTrend.trend === 'decreasing' ? '下降' : '稳定' })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-neutral-600 dark:text-neutral-400", children: "\u9884\u6D4B\u503C (1\u5C0F\u65F6\u540E):" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "font-medium text-neutral-900 dark:text-neutral-100 mt-1", children: [currentTrend.prediction.toFixed(1), config.unit] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-neutral-600 dark:text-neutral-400", children: "\u7F6E\u4FE1\u5EA6:" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-2 mt-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-1 bg-neutral-200 dark:bg-neutral-700 rounded-full h-2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "bg-blue-500 h-2 rounded-full transition-all duration-300", style: { width: `${currentTrend.confidence * 100}%` } }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "font-medium text-neutral-900 dark:text-neutral-100", children: [(currentTrend.confidence * 100).toFixed(0), "%"] })] })] })] })] }))] })] }));
};


/***/ }),

/***/ "./src/renderer/components/Monitor/SystemHealthDashboard.tsx":
/*!*******************************************************************!*\
  !*** ./src/renderer/components/Monitor/SystemHealthDashboard.tsx ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SystemHealthDashboard: () => (/* binding */ SystemHealthDashboard)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _services_monitoring__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../services/monitoring */ "./src/renderer/services/monitoring.ts");
/* harmony import */ var _UI_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../UI/Card */ "./src/renderer/components/UI/Card.tsx");
/* harmony import */ var _utils_cn__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/cn */ "./src/renderer/utils/cn.ts");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/activity.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/alert-triangle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/check-circle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/clock.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/lightbulb.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/shield.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/zap.mjs");






const SystemHealthDashboard = ({ className, }) => {
    const [healthStatus, setHealthStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({
        overall: 'healthy',
        issues: [],
        recommendations: [],
    });
    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());
    const monitoringService = _services_monitoring__WEBPACK_IMPORTED_MODULE_2__.MonitoringService.getInstance();
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
        const updateHealth = () => {
            setHealthStatus(monitoringService.getSystemHealth());
            setLastUpdate(new Date());
        };
        updateHealth();
        const interval = setInterval(updateHealth, 30000); // Update every 30 seconds
        return () => clearInterval(interval);
    }, []);
    const getHealthConfig = (status) => {
        switch (status) {
            case 'healthy':
                return {
                    icon: lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"],
                    color: 'text-green-600 dark:text-green-400',
                    bgColor: 'bg-green-50 dark:bg-green-900/20',
                    borderColor: 'border-green-200 dark:border-green-800',
                    title: '系统健康',
                    description: '所有系统指标正常',
                };
            case 'warning':
                return {
                    icon: lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"],
                    color: 'text-yellow-600 dark:text-yellow-400',
                    bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
                    borderColor: 'border-yellow-200 dark:border-yellow-800',
                    title: '需要关注',
                    description: '发现一些需要关注的问题',
                };
            case 'critical':
                return {
                    icon: lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"],
                    color: 'text-red-600 dark:text-red-400',
                    bgColor: 'bg-red-50 dark:bg-red-900/20',
                    borderColor: 'border-red-200 dark:border-red-800',
                    title: '严重问题',
                    description: '发现严重问题，需要立即处理',
                };
        }
    };
    const config = getHealthConfig(healthStatus.overall);
    const IconComponent = config.icon;
    const getQuickActions = () => {
        const actions = [];
        if (healthStatus.overall === 'critical') {
            actions.push({
                icon: lucide_react__WEBPACK_IMPORTED_MODULE_11__["default"],
                title: '紧急处理',
                description: '立即检查集群状态',
                color: 'text-red-600 dark:text-red-400',
                bgColor: 'bg-red-50 dark:bg-red-900/20',
            });
        }
        if (healthStatus.recommendations.length > 0) {
            actions.push({
                icon: lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"],
                title: '优化建议',
                description: '查看系统优化建议',
                color: 'text-blue-600 dark:text-blue-400',
                bgColor: 'bg-blue-50 dark:bg-blue-900/20',
            });
        }
        actions.push({
            icon: lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"],
            title: '性能监控',
            description: '查看详细性能指标',
            color: 'text-purple-600 dark:text-purple-400',
            bgColor: 'bg-purple-50 dark:bg-purple-900/20',
        });
        return actions;
    };
    const quickActions = getQuickActions();
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('space-y-6', className), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_3__.Card, { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('p-6 border-l-4', config.bgColor, config.borderColor), children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IconComponent, { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('mt-1 flex-shrink-0', config.color), size: 32 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between mb-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-xl font-semibold text-neutral-900 dark:text-neutral-100", children: config.title }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-2 text-sm text-neutral-500 dark:text-neutral-400", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_8__["default"], { size: 14 }), "\u6700\u540E\u66F4\u65B0: ", lastUpdate.toLocaleTimeString()] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-neutral-700 dark:text-neutral-300 mb-4", children: config.description }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center p-3 bg-white dark:bg-neutral-800 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-2xl font-bold text-neutral-900 dark:text-neutral-100", children: healthStatus.issues.length }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-neutral-600 dark:text-neutral-400", children: "\u6D3B\u8DC3\u95EE\u9898" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center p-3 bg-white dark:bg-neutral-800 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-2xl font-bold text-neutral-900 dark:text-neutral-100", children: healthStatus.recommendations.length }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-neutral-600 dark:text-neutral-400", children: "\u4F18\u5316\u5EFA\u8BAE" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center p-3 bg-white dark:bg-neutral-800 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('text-2xl font-bold', config.color), children: healthStatus.overall === 'healthy' ? '100%' :
                                                        healthStatus.overall === 'warning' ? '75%' : '25%' }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm text-neutral-600 dark:text-neutral-400", children: "\u5065\u5EB7\u5EA6" })] })] })] })] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 lg:grid-cols-2 gap-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_3__.Card, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-2 mb-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"], { className: "text-red-500", size: 20 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100", children: "\u5F53\u524D\u95EE\u9898" }), healthStatus.issues.length > 0 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "px-2 py-1 text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 rounded-full", children: healthStatus.issues.length }))] }), healthStatus.issues.length === 0 ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center py-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"], { className: "mx-auto mb-3 text-green-500", size: 48 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-neutral-600 dark:text-neutral-400", children: "\u6CA1\u6709\u53D1\u73B0\u95EE\u9898\uFF0C\u7CFB\u7EDF\u8FD0\u884C\u6B63\u5E38" })] })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-3", children: healthStatus.issues.map((issue, index) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start gap-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"], { className: "text-red-500 flex-shrink-0 mt-0.5", size: 16 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-neutral-700 dark:text-neutral-300", children: issue })] }, index))) }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_3__.Card, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-2 mb-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"], { className: "text-blue-500", size: 20 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100", children: "\u4F18\u5316\u5EFA\u8BAE" }), healthStatus.recommendations.length > 0 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full", children: healthStatus.recommendations.length }))] }), healthStatus.recommendations.length === 0 ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center py-8", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_10__["default"], { className: "mx-auto mb-3 text-green-500", size: 48 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-neutral-600 dark:text-neutral-400", children: "\u7CFB\u7EDF\u914D\u7F6E\u826F\u597D\uFF0C\u6682\u65E0\u4F18\u5316\u5EFA\u8BAE" })] })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-3", children: healthStatus.recommendations.map((recommendation, index) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"], { className: "text-blue-500 flex-shrink-0 mt-0.5", size: 16 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-neutral-700 dark:text-neutral-300", children: recommendation })] }, index))) }))] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_3__.Card, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4", children: "\u5FEB\u901F\u64CD\u4F5C" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4", children: quickActions.map((action, index) => {
                            const ActionIcon = action.icon;
                            return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("button", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('p-4 rounded-lg border-2 border-transparent hover:border-current transition-all text-left group', action.bgColor), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center gap-3 mb-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ActionIcon, { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('flex-shrink-0', action.color), size: 24 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h5", { className: "font-medium text-neutral-900 dark:text-neutral-100 group-hover:underline", children: action.title })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-neutral-600 dark:text-neutral-400", children: action.description })] }, index));
                        }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_3__.Card, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4", children: "\u7CFB\u7EDF\u72B6\u6001\u6307\u6807" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "grid grid-cols-2 md:grid-cols-4 gap-4", children: [
                            {
                                label: '连接状态',
                                status: 'healthy',
                                value: '正常',
                                icon: lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"],
                            },
                            {
                                label: '数据完整性',
                                status: healthStatus.overall === 'critical' ? 'critical' : 'healthy',
                                value: healthStatus.overall === 'critical' ? '异常' : '正常',
                                icon: healthStatus.overall === 'critical' ? lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"] : lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"],
                            },
                            {
                                label: '性能状态',
                                status: healthStatus.overall === 'warning' ? 'warning' : 'healthy',
                                value: healthStatus.overall === 'warning' ? '警告' : '良好',
                                icon: healthStatus.overall === 'warning' ? lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"] : lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"],
                            },
                            {
                                label: '监控状态',
                                status: 'healthy',
                                value: '运行中',
                                icon: lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"],
                            },
                        ].map((indicator, index) => {
                            const IndicatorIcon = indicator.icon;
                            const statusColor = indicator.status === 'healthy' ? 'text-green-500' :
                                indicator.status === 'warning' ? 'text-yellow-500' :
                                    'text-red-500';
                            return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center p-3 bg-neutral-50 dark:bg-neutral-800 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(IndicatorIcon, { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('mx-auto mb-2', statusColor), size: 24 }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-sm font-medium text-neutral-900 dark:text-neutral-100", children: indicator.label }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: (0,_utils_cn__WEBPACK_IMPORTED_MODULE_4__.cn)('text-xs font-medium', statusColor), children: indicator.value })] }, index));
                        }) })] })] }));
};


/***/ })

}]);
//# sourceMappingURL=src_renderer_components_Monitor_ClusterOverview_tsx.renderer.js.map