import React, { useState, useEffect, useCallback, useRef } from 'react';
import { LayoutMode } from '../Data/UnifiedDataManagementPage';

// 布局偏好接口
interface LayoutPreferences {
  defaultMode: LayoutMode;
  indexPanelWidth: number;
  showIndexPanel: boolean;
  enableAutoCollapse: boolean;
  mobileBreakpoint: number;
  tabletBreakpoint: number;
}

// 索引面板状态接口
interface IndexPanelState {
  isVisible: boolean;
  isCollapsed: boolean;
  width: number;
  position: 'left' | 'right' | 'top' | 'bottom';
  displayMode: 'detailed' | 'compact' | 'minimal';
}

// 数据面板状态接口
interface DataPanelState {
  activeTab: string;
  openTabs: TabInfo[];
  viewMode: 'browser' | 'editor' | 'batch' | 'query';
  splitMode: 'single' | 'horizontal' | 'vertical';
}

// 标签页信息接口
interface TabInfo {
  id: string;
  title: string;
  indexName: string;
  type: 'data' | 'query' | 'settings';
  isActive: boolean;
  isDirty: boolean;
}

// 布局状态接口
interface LayoutState {
  mode: LayoutMode;
  breakpoint: 'mobile' | 'tablet' | 'desktop';
  indexPanel: IndexPanelState;
  dataPanel: DataPanelState;
  isResponsive: boolean;
}

// 组件属性接口
interface ResponsiveLayoutControllerProps {
  children: React.ReactNode;
  preferences: LayoutPreferences;
  onLayoutChange?: (state: LayoutState) => void;
  onPreferencesChange?: (preferences: Partial<LayoutPreferences>) => void;
  className?: string;
}

// 默认偏好设置
const DEFAULT_PREFERENCES: LayoutPreferences = {
  defaultMode: 'sidebar',
  indexPanelWidth: 320,
  showIndexPanel: true,
  enableAutoCollapse: true,
  mobileBreakpoint: 768,
  tabletBreakpoint: 1024
};

// 默认布局状态
const DEFAULT_LAYOUT_STATE: LayoutState = {
  mode: 'sidebar',
  breakpoint: 'desktop',
  indexPanel: {
    isVisible: true,
    isCollapsed: false,
    width: 320,
    position: 'left',
    displayMode: 'detailed'
  },
  dataPanel: {
    activeTab: '',
    openTabs: [],
    viewMode: 'browser',
    splitMode: 'single'
  },
  isResponsive: true
};

export const ResponsiveLayoutController: React.FC<ResponsiveLayoutControllerProps> = ({
  children,
  preferences = DEFAULT_PREFERENCES,
  onLayoutChange,
  onPreferencesChange,
  className = ''
}) => {
  // 状态管理
  const [layoutState, setLayoutState] = useState<LayoutState>(DEFAULT_LAYOUT_STATE);
  const [isInitialized, setIsInitialized] = useState(false);
  
  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  // 检测断点
  const detectBreakpoint = useCallback((width: number): 'mobile' | 'tablet' | 'desktop' => {
    if (width < preferences.mobileBreakpoint) return 'mobile';
    if (width < preferences.tabletBreakpoint) return 'tablet';
    return 'desktop';
  }, [preferences.mobileBreakpoint, preferences.tabletBreakpoint]);

  // 根据断点自动调整布局
  const adjustLayoutForBreakpoint = useCallback((breakpoint: 'mobile' | 'tablet' | 'desktop') => {
    if (!layoutState.isResponsive) return;

    let newMode: LayoutMode = preferences.defaultMode;
    let indexPanelChanges: Partial<IndexPanelState> = {};

    switch (breakpoint) {
      case 'mobile':
        newMode = 'mobile';
        indexPanelChanges = {
          displayMode: 'minimal',
          isCollapsed: preferences.enableAutoCollapse
        };
        break;
      
      case 'tablet':
        newMode = 'tabs';
        indexPanelChanges = {
          displayMode: 'compact',
          isCollapsed: false
        };
        break;
      
      case 'desktop':
        newMode = preferences.defaultMode;
        indexPanelChanges = {
          displayMode: 'detailed',
          isCollapsed: !preferences.showIndexPanel
        };
        break;
    }

    setLayoutState(prev => ({
      ...prev,
      mode: newMode,
      breakpoint,
      indexPanel: {
        ...prev.indexPanel,
        ...indexPanelChanges
      }
    }));
  }, [layoutState.isResponsive, preferences]);

  // 处理窗口大小变化
  const handleResize = useCallback((entries: ResizeObserverEntry[]) => {
    if (!entries.length) return;

    const { width } = entries[0].contentRect;
    const newBreakpoint = detectBreakpoint(width);
    
    if (newBreakpoint !== layoutState.breakpoint) {
      adjustLayoutForBreakpoint(newBreakpoint);
    }
  }, [detectBreakpoint, layoutState.breakpoint, adjustLayoutForBreakpoint]);

  // 初始化 ResizeObserver
  useEffect(() => {
    if (!containerRef.current) return;

    resizeObserverRef.current = new ResizeObserver(handleResize);
    resizeObserverRef.current.observe(containerRef.current);

    // 初始检测
    const initialWidth = containerRef.current.offsetWidth;
    const initialBreakpoint = detectBreakpoint(initialWidth);
    adjustLayoutForBreakpoint(initialBreakpoint);
    setIsInitialized(true);

    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, [handleResize, detectBreakpoint, adjustLayoutForBreakpoint]);

  // 监听偏好设置变化
  useEffect(() => {
    if (!isInitialized) return;

    setLayoutState(prev => ({
      ...prev,
      indexPanel: {
        ...prev.indexPanel,
        width: preferences.indexPanelWidth,
        isVisible: preferences.showIndexPanel,
        isCollapsed: !preferences.showIndexPanel
      }
    }));
  }, [preferences, isInitialized]);

  // 通知布局变化
  useEffect(() => {
    if (isInitialized) {
      onLayoutChange?.(layoutState);
    }
  }, [layoutState, onLayoutChange, isInitialized]);

  // 切换布局模式
  const switchLayoutMode = useCallback((mode: LayoutMode) => {
    setLayoutState(prev => ({
      ...prev,
      mode,
      isResponsive: false // 手动切换后禁用自动响应
    }));

    // 更新偏好设置
    onPreferencesChange?.({ defaultMode: mode });
  }, [onPreferencesChange]);

  // 切换索引面板可见性
  const toggleIndexPanel = useCallback(() => {
    const newVisible = !layoutState.indexPanel.isVisible;
    
    setLayoutState(prev => ({
      ...prev,
      indexPanel: {
        ...prev.indexPanel,
        isVisible: newVisible,
        isCollapsed: !newVisible
      }
    }));

    // 更新偏好设置
    onPreferencesChange?.({ showIndexPanel: newVisible });
  }, [layoutState.indexPanel.isVisible, onPreferencesChange]);

  // 调整索引面板宽度
  const resizeIndexPanel = useCallback((width: number) => {
    const clampedWidth = Math.max(200, Math.min(600, width));
    
    setLayoutState(prev => ({
      ...prev,
      indexPanel: {
        ...prev.indexPanel,
        width: clampedWidth
      }
    }));

    // 更新偏好设置
    onPreferencesChange?.({ indexPanelWidth: clampedWidth });
  }, [onPreferencesChange]);

  // 启用/禁用响应式模式
  const toggleResponsiveMode = useCallback(() => {
    const newResponsive = !layoutState.isResponsive;
    
    setLayoutState(prev => ({
      ...prev,
      isResponsive: newResponsive
    }));

    if (newResponsive) {
      // 重新应用断点逻辑
      adjustLayoutForBreakpoint(layoutState.breakpoint);
    }
  }, [layoutState.isResponsive, layoutState.breakpoint, adjustLayoutForBreakpoint]);

  // 管理数据面板标签页
  const addTab = useCallback((tab: Omit<TabInfo, 'id' | 'isActive'>) => {
    const newTab: TabInfo = {
      ...tab,
      id: `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      isActive: true
    };

    setLayoutState(prev => ({
      ...prev,
      dataPanel: {
        ...prev.dataPanel,
        openTabs: prev.dataPanel.openTabs.map(t => ({ ...t, isActive: false })).concat(newTab),
        activeTab: newTab.id
      }
    }));
  }, []);

  const closeTab = useCallback((tabId: string) => {
    setLayoutState(prev => {
      const remainingTabs = prev.dataPanel.openTabs.filter(t => t.id !== tabId);
      const wasActive = prev.dataPanel.activeTab === tabId;
      
      return {
        ...prev,
        dataPanel: {
          ...prev.dataPanel,
          openTabs: remainingTabs,
          activeTab: wasActive && remainingTabs.length > 0 ? remainingTabs[0].id : ''
        }
      };
    });
  }, []);

  const switchTab = useCallback((tabId: string) => {
    setLayoutState(prev => ({
      ...prev,
      dataPanel: {
        ...prev.dataPanel,
        openTabs: prev.dataPanel.openTabs.map(t => ({
          ...t,
          isActive: t.id === tabId
        })),
        activeTab: tabId
      }
    }));
  }, []);

  // 提供给子组件的上下文值
  const contextValue = {
    layoutState,
    switchLayoutMode,
    toggleIndexPanel,
    resizeIndexPanel,
    toggleResponsiveMode,
    addTab,
    closeTab,
    switchTab
  };

  // 计算容器样式
  const containerStyles = {
    display: 'flex',
    height: '100%',
    flexDirection: layoutState.mode === 'mobile' ? 'column' : 'row'
  } as React.CSSProperties;

  return (
    <div 
      ref={containerRef}
      className={`responsive-layout-controller ${className}`}
      style={containerStyles}
    >
      {React.Children.map(children, child => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, { 
            layoutContext: contextValue,
            ...child.props 
          });
        }
        return child;
      })}
    </div>
  );
};

// 导出类型和默认值
export type { 
  LayoutState, 
  LayoutPreferences, 
  IndexPanelState, 
  DataPanelState, 
  TabInfo 
};

export { DEFAULT_PREFERENCES, DEFAULT_LAYOUT_STATE };
