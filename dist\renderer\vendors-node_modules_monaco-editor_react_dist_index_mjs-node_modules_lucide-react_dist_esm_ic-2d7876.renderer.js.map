{"version": 3, "file": "vendors-node_modules_monaco-editor_react_dist_index_mjs-node_modules_lucide-react_dist_esm_ic-2d7876.renderer.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;;AAEA;AACA,kBAAkB,sBAAsB;AACxC;;AAEA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,cAAc,uBAAuB;AACrC;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA,gBAAgB,6BAA6B;AAC7C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,8CAA8C,+BAA+B;AAC7E;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,yCAAyC,SAAS;;AAElD;AACA;;AAEA;AACA;AACA;;AAE4b;;;;;;;;;;;;;;;AC7I5b;AACA;AACA;AACA;AACA;;AAEA,iEAAe,MAAM,EAAC;;;;;;;;;;;;;;;;ACNiB;AACK;;;;;;;;;;;;;;;;;;;;;;ACDoG;AAChH;AACU;AACM;AACN;AACA;AACc;;AAExD;;AAEA,oBAAoB,mDAAK;AACzB,UAAU,wDAAQ;AAClB;AACA;AACA;AACA;AACA,CAAC;AACD,qBAAqB,mFAAc;AACnC;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB;;;AAGA;AACA,2BAA2B,4DAAU;AACrC;AACA,eAAe,6FAAwB;;AAEvC;AACA;AACA,cAAc,+DAAK;AACnB;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,YAAY,SAAS;AACrB;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA,aAAa,oEAAc;AAC3B;;AAEA;AACA;AACA;AACA,aAAa,oEAAc;AAC3B;;AAEA,IAAI,6DAAO;AACX;;AAEA,SAAS,oEAAc;AACvB;AACA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY,QAAQ;AACpB;;;AAGA;AACA;AACA;AACA;AACA;AACA,WAAW,QAAQ;AACnB,YAAY,QAAQ;AACpB;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,QAAQ;AACpB;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,YAAY,aAAa;AACzB;;;AAGA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;;AAEA,iEAAe,MAAM,EAAC;;;;;;;;;;;;;;;AC3LtB;AACA,qEAAqE,aAAa;AAClF;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA,iEAAe,OAAO,EAAC;;;;;;;;;;;;;;;ACZvB;AACA;AACA;;AAEA,wEAAwE,aAAa;AACrF;AACA;;AAEA;AACA,iFAAiF,eAAe;AAChG;AACA;;AAEA;AACA;AACA;AACA;;AAEA,iEAAe,KAAK,EAAC;;;;;;;;;;;;;;;;AClBsE;;AAE3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,SAAS,mFAAc,CAAC,mFAAc,GAAG;AACzC;;AAEA,iEAAe,KAAK,EAAC;;;;;;;;;;;;;;;ACbrB;AACA,WAAW;AACX;;AAEA,iEAAe,QAAQ,EAAC;;;;;;;;;;;;;;;;ACJxB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;;AAEA,iEAAe,cAAc,EAAC;AACC;;;;;;;;;;;;;;;;;;;ACpBO;AACM;;AAE5C;AACA;AACA,WAAW,QAAQ;AACnB,YAAY,QAAQ;AACpB;;AAEA;AACA;AACA,OAAO,8DAAQ;;AAEf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,wIAAwI,QAAQ,qBAAqB,kCAAkC,SAAS,aAAa;AAC7N;AACA,mBAAmB,2DAAK;AACxB;AACA;AACA;;AAEA,iEAAe,UAAU,EAAC;AACa;;;;;;;;;;;;;;;;;;;;;ACjD0M,QAAQ,SAAS,uDAAuD,YAAY,aAAa,OAAO,gBAAgB,MAA4B,QAAQ,WAAW,uFAAuF,MAAM,aAAa,WAAW,EAAE,OAAO,gDAAgB,QAAQ,kBAAkB,IAAI,SAAS,QAAQ,aAAa,6EAA6E,EAAE,OAAO,gDAAe,YAAY,OAAO,8BAA8B,MAAM,KAAK,gDAAe,WAAW,gDAAe,QAAQ,aAAa,6BAA6B,aAAa,GAAG,UAAU,MAAM,2CAAE,KAAwC,eAAe,gDAAE,OAAO,SAAyD,sBAAsB,MAAM,6CAAE,KAAK,gDAAE,oBAAoB,aAAa,MAAM,SAAS,cAAc,oBAAoB,4BAA4B,iBAAiB,kCAAkC,qBAAqB,kDAAkD,iBAAiB,sBAAsB,aAAa,8NAA8N,6DAA6D,6BAA6B,EAAE,SAAS,+CAAE,WAAW,+CAAE,OAAO,6CAAC,SAAS,6CAAC,SAAS,6CAAC,SAAS,6CAAC,MAAM,6CAAC,MAAM,6CAAC,KAAK,OAAO,MAAM,6DAAE,QAAQ,wJAAwJ,SAAS,yBAAyB,4EAA4E,iCAAiC,eAAe,yBAAyB,4EAA4E,iCAAiC,eAAe,oCAAoC,6GAA6G,sEAAsE,qBAAqB,eAAe,gDAAgD,eAAe,IAAI,sBAAsB,sBAAsB,oGAAoG,mBAAmB,8BAA8B,eAAe,4BAA4B,QAAQ,MAAM,kDAAE,MAAM,qBAAqB,qBAAqB,oFAAoF,qBAAqB,sBAAsB,EAAE,oBAAoB,kDAAE,MAAM,+EAA+E,wBAAwB,wDAAwD,UAAU,gDAAE,MAAM,kCAAkC,MAAM,gDAAE,MAAM,YAAY,UAAU,aAAa,4BAA4B,yEAAyE,OAAO,gDAAgB,IAAI,6EAA6E,EAAE,UAAU,OAAO,2CAAE,KAA6E,cAAc,SAAS,+CAAE,CAAC,6DAAE,wBAAwB,cAAc,MAAM,aAAa,6DAAE,mBAAmB,KAAK,mBAAmB,IAAI,UAAiN,eAAe,MAAM,6CAAE,GAAG,OAAO,gDAAE,MAAM,YAAY,gBAAgB,UAAU,cAAc,aAAa,mIAAmI,sBAAsB,sGAAsG,uDAAuD,EAAE,SAAS,+CAAE,WAAW,+CAAE,OAAO,6CAAC,SAAS,6CAAC,SAAS,6CAAC,SAAS,6CAAC,MAAM,6CAAC,MAAM,6CAAC,KAAK,6CAAC,cAAc,6CAAC,OAAO,6CAAC,KAAK,OAAO,MAAM,6DAAE,QAAQ,yJAAyJ,SAAS,8CAA8C,oIAAoI,eAAe,4BAA4B,eAAe,+KAA+K,0EAA0E,2CAA2C,eAAe,4BAA4B,8CAA8C,eAAe,qCAAqC,eAAe,8BAA8B,QAAQ,MAAM,kDAAE,MAAM,0CAA0C,qBAAqB,kDAAkD,8CAA8C,gCAAgC,iIAAiI,0BAA0B,gDAAC,MAAM,kCAAkC,MAAM,gDAAC,MAAM,YAAY,sBAAsB,gDAAC,MAAM,6EAA6E,qCAAqC,GAAG,QAAQ,gDAAC,MAAM,MAAM,8CAA8C,gCAAgC,kCAAkC,wCAAwC,WAAW,EAAE,QAAQ,EAAE,WAAW,cAAc,aAAa,QAAQ,cAAc,iHAAiH,OAAO,gDAAgB,IAAI,6EAA6E,EAAE,UAAU,OAAO,2CAAE,KAAK,UAA2F;AAC71M,kC;;;;;;;;;;;;;;;ACDA;AACA;AACA;;AAEuD;;AAEvD,oBAAoB,iEAAgB;AACpC,aAAa,oCAAoC;AACjD,aAAa,8BAA8B;AAC3C,aAAa,kCAAkC;AAC/C,aAAa,6BAA6B;AAC1C;;AAEkC;AAClC;;;;;;;;;;;;;;;;ACdA;AACA;AACA;;AAEuD;;AAEvD,iBAAiB,iEAAgB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,qDAAqD;AAClE,aAAa,mDAAmD;AAChE,aAAa,sDAAsD;AACnE;;AAE+B;AAC/B;;;;;;;;;;;;;;;;ACzBA;AACA;AACA;;AAEuD;;AAEvD,sBAAsB,iEAAgB;AACtC,aAAa,mDAAmD;AAChE,aAAa,6CAA6C;AAC1D;;AAEoC;AACpC;;;;;;;;;;;;;;;;ACZA;AACA;AACA;;AAEuD;;AAEvD,aAAa,iEAAgB;AAC7B,aAAa,oDAAoD;AACjE,aAAa,sDAAsD;AACnE,aAAa,qDAAqD;AAClE,aAAa,sDAAsD;AACnE;;AAE2B;AAC3B;;;;;;;;;;;;;;;;ACdA;AACA;AACA;;AAEuD;;AAEvD,mBAAmB,iEAAgB;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,0CAA0C;AACzD;;AAEiC;AACjC;;;;;;;;;;;;;;;;ACvBA;AACA;AACA;;AAEuD;;AAEvD,aAAa,iEAAgB;AAC7B,iBAAiB,4CAA4C;AAC7D,aAAa,sDAAsD;AACnE,aAAa,sDAAsD;AACnE;;AAE2B;AAC3B;;;;;;;;;;;;;;;ACbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;;AAEA;AACA,kBAAkB,sBAAsB;AACxC;;AAEA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;;AAEA;AACA,qEAAqE,aAAa;AAClF;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;;AAEA,2EAA2E,eAAe;AAC1F;AACA;;AAEA;AACA,iFAAiF,eAAe;AAChG;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,WAAW;AACX;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,kDAAkD;AAClD;AACA;;AAEA;AACA;AACA;;AAEA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;;AAEA,iEAAe,KAAK,EAAC", "sources": ["webpack://es-client/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js", "webpack://es-client/./node_modules/@monaco-editor/loader/lib/es/config/index.js", "webpack://es-client/./node_modules/@monaco-editor/loader/lib/es/index.js", "webpack://es-client/./node_modules/@monaco-editor/loader/lib/es/loader/index.js", "webpack://es-client/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js", "webpack://es-client/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js", "webpack://es-client/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js", "webpack://es-client/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js", "webpack://es-client/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js", "webpack://es-client/./node_modules/@monaco-editor/loader/lib/es/validators/index.js", "webpack://es-client/./node_modules/@monaco-editor/react/dist/index.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/arrow-up-down.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/calendar.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/graduation-cap.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/hash.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/toggle-left.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/type.mjs", "webpack://es-client/./node_modules/state-local/lib/es/state-local.js"], "sourcesContent": ["function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nexport { _arrayLikeToArray as arrayLikeToArray, _arrayWithHoles as arrayWithHoles, _defineProperty as defineProperty, _iterableToArrayLimit as iterableToArrayLimit, _nonIterableRest as nonIterableRest, _objectSpread2 as objectSpread2, _objectWithoutProperties as objectWithoutProperties, _objectWithoutPropertiesLoose as objectWithoutPropertiesLoose, _slicedToArray as slicedToArray, _unsupportedIterableToArray as unsupportedIterableToArray };\n", "var config = {\n  paths: {\n    vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs'\n  }\n};\n\nexport default config;\n", "import loader from './loader/index.js';\nexport { default } from './loader/index.js';\n", "import { slicedToArray as _slicedToArray, objectWithoutProperties as _objectWithoutProperties } from '../_virtual/_rollupPluginBabelHelpers.js';\nimport state from 'state-local';\nimport config$1 from '../config/index.js';\nimport validators from '../validators/index.js';\nimport compose from '../utils/compose.js';\nimport merge from '../utils/deepMerge.js';\nimport makeCancelable from '../utils/makeCancelable.js';\n\n/** the local state of the module */\n\nvar _state$create = state.create({\n  config: config$1,\n  isInitialized: false,\n  resolve: null,\n  reject: null,\n  monaco: null\n}),\n    _state$create2 = _slicedToArray(_state$create, 2),\n    getState = _state$create2[0],\n    setState = _state$create2[1];\n/**\n * set the loader configuration\n * @param {Object} config - the configuration object\n */\n\n\nfunction config(globalConfig) {\n  var _validators$config = validators.config(globalConfig),\n      monaco = _validators$config.monaco,\n      config = _objectWithoutProperties(_validators$config, [\"monaco\"]);\n\n  setState(function (state) {\n    return {\n      config: merge(state.config, config),\n      monaco: monaco\n    };\n  });\n}\n/**\n * handles the initialization of the monaco-editor\n * @return {Promise} - returns an instance of monaco (with a cancelable promise)\n */\n\n\nfunction init() {\n  var state = getState(function (_ref) {\n    var monaco = _ref.monaco,\n        isInitialized = _ref.isInitialized,\n        resolve = _ref.resolve;\n    return {\n      monaco: monaco,\n      isInitialized: isInitialized,\n      resolve: resolve\n    };\n  });\n\n  if (!state.isInitialized) {\n    setState({\n      isInitialized: true\n    });\n\n    if (state.monaco) {\n      state.resolve(state.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n\n    if (window.monaco && window.monaco.editor) {\n      storeMonacoInstance(window.monaco);\n      state.resolve(window.monaco);\n      return makeCancelable(wrapperPromise);\n    }\n\n    compose(injectScripts, getMonacoLoaderScript)(configureLoader);\n  }\n\n  return makeCancelable(wrapperPromise);\n}\n/**\n * injects provided scripts into the document.body\n * @param {Object} script - an HTML script element\n * @return {Object} - the injected HTML script element\n */\n\n\nfunction injectScripts(script) {\n  return document.body.appendChild(script);\n}\n/**\n * creates an HTML script element with/without provided src\n * @param {string} [src] - the source path of the script\n * @return {Object} - the created HTML script element\n */\n\n\nfunction createScript(src) {\n  var script = document.createElement('script');\n  return src && (script.src = src), script;\n}\n/**\n * creates an HTML script element with the monaco loader src\n * @return {Object} - the created HTML script element\n */\n\n\nfunction getMonacoLoaderScript(configureLoader) {\n  var state = getState(function (_ref2) {\n    var config = _ref2.config,\n        reject = _ref2.reject;\n    return {\n      config: config,\n      reject: reject\n    };\n  });\n  var loaderScript = createScript(\"\".concat(state.config.paths.vs, \"/loader.js\"));\n\n  loaderScript.onload = function () {\n    return configureLoader();\n  };\n\n  loaderScript.onerror = state.reject;\n  return loaderScript;\n}\n/**\n * configures the monaco loader\n */\n\n\nfunction configureLoader() {\n  var state = getState(function (_ref3) {\n    var config = _ref3.config,\n        resolve = _ref3.resolve,\n        reject = _ref3.reject;\n    return {\n      config: config,\n      resolve: resolve,\n      reject: reject\n    };\n  });\n  var require = window.require;\n\n  require.config(state.config);\n\n  require(['vs/editor/editor.main'], function (monaco) {\n    storeMonacoInstance(monaco);\n    state.resolve(monaco);\n  }, function (error) {\n    state.reject(error);\n  });\n}\n/**\n * store monaco instance in local state\n */\n\n\nfunction storeMonacoInstance(monaco) {\n  if (!getState().monaco) {\n    setState({\n      monaco: monaco\n    });\n  }\n}\n/**\n * internal helper function\n * extracts stored monaco instance\n * @return {Object|null} - the monaco instance\n */\n\n\nfunction __getMonacoInstance() {\n  return getState(function (_ref4) {\n    var monaco = _ref4.monaco;\n    return monaco;\n  });\n}\n\nvar wrapperPromise = new Promise(function (resolve, reject) {\n  return setState({\n    resolve: resolve,\n    reject: reject\n  });\n});\nvar loader = {\n  config: config,\n  init: init,\n  __getMonacoInstance: __getMonacoInstance\n};\n\nexport default loader;\n", "var compose = function compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n};\n\nexport default compose;\n", "function curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len2 = arguments.length, nextArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        nextArgs[_key2] = arguments[_key2];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nexport default curry;\n", "import { objectSpread2 as _objectSpread2 } from '../_virtual/_rollupPluginBabelHelpers.js';\n\nfunction merge(target, source) {\n  Object.keys(source).forEach(function (key) {\n    if (source[key] instanceof Object) {\n      if (target[key]) {\n        Object.assign(source[key], merge(target[key], source[key]));\n      }\n    }\n  });\n  return _objectSpread2(_objectSpread2({}, target), source);\n}\n\nexport default merge;\n", "function isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nexport default isObject;\n", "// The source (has been changed) is https://github.com/facebook/react/issues/5465#issuecomment-157888325\nvar CANCELATION_MESSAGE = {\n  type: 'cancelation',\n  msg: 'operation is manually canceled'\n};\n\nfunction makeCancelable(promise) {\n  var hasCanceled_ = false;\n  var wrappedPromise = new Promise(function (resolve, reject) {\n    promise.then(function (val) {\n      return hasCanceled_ ? reject(CANCELATION_MESSAGE) : resolve(val);\n    });\n    promise[\"catch\"](reject);\n  });\n  return wrappedPromise.cancel = function () {\n    return hasCanceled_ = true;\n  }, wrappedPromise;\n}\n\nexport default makeCancelable;\nexport { CANCELATION_MESSAGE };\n", "import curry from '../utils/curry.js';\nimport isObject from '../utils/isObject.js';\n\n/**\n * validates the configuration object and informs about deprecation\n * @param {Object} config - the configuration object \n * @return {Object} config - the validated configuration object\n */\n\nfunction validateConfig(config) {\n  if (!config) errorHandler('configIsRequired');\n  if (!isObject(config)) errorHandler('configType');\n\n  if (config.urls) {\n    informAboutDeprecation();\n    return {\n      paths: {\n        vs: config.urls.monacoBase\n      }\n    };\n  }\n\n  return config;\n}\n/**\n * logs deprecation message\n */\n\n\nfunction informAboutDeprecation() {\n  console.warn(errorMessages.deprecation);\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  configIsRequired: 'the configuration object is required',\n  configType: 'the configuration object should be an object',\n  \"default\": 'an unknown error accured in `@monaco-editor/loader` package',\n  deprecation: \"Deprecation warning!\\n    You are using deprecated way of configuration.\\n\\n    Instead of using\\n      monaco.config({ urls: { monacoBase: '...' } })\\n    use\\n      monaco.config({ paths: { vs: '...' } })\\n\\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\\n  \"\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  config: validateConfig\n};\n\nexport default validators;\nexport { errorHandler, errorMessages };\n", "import _t from\"@monaco-editor/loader\";import{memo as Te}from\"react\";import ke,{useState as re,useRef as S,useCallback as oe,useEffect as ne}from\"react\";import Se from\"@monaco-editor/loader\";import{memo as ye}from\"react\";import K from\"react\";var le={wrapper:{display:\"flex\",position:\"relative\",textAlign:\"initial\"},fullWidth:{width:\"100%\"},hide:{display:\"none\"}},v=le;import me from\"react\";var ae={container:{display:\"flex\",height:\"100%\",width:\"100%\",justifyContent:\"center\",alignItems:\"center\"}},Y=ae;function Me({children:e}){return me.createElement(\"div\",{style:Y.container},e)}var Z=Me;var $=Z;function Ee({width:e,height:r,isEditorReady:n,loading:t,_ref:a,className:m,wrapperProps:E}){return K.createElement(\"section\",{style:{...v.wrapper,width:e,height:r},...E},!n&&K.createElement($,null,t),K.createElement(\"div\",{ref:a,style:{...v.fullWidth,...!n&&v.hide},className:m}))}var ee=Ee;var H=ye(ee);import{useEffect as xe}from\"react\";function Ce(e){xe(e,[])}var k=Ce;import{useEffect as ge,useRef as Re}from\"react\";function he(e,r,n=!0){let t=Re(!0);ge(t.current||!n?()=>{t.current=!1}:e,r)}var l=he;function D(){}function h(e,r,n,t){return De(e,t)||be(e,r,n,t)}function De(e,r){return e.editor.getModel(te(e,r))}function be(e,r,n,t){return e.editor.createModel(r,n,t?te(e,t):void 0)}function te(e,r){return e.Uri.parse(r)}function Oe({original:e,modified:r,language:n,originalLanguage:t,modifiedLanguage:a,originalModelPath:m,modifiedModelPath:E,keepCurrentOriginalModel:g=!1,keepCurrentModifiedModel:N=!1,theme:x=\"light\",loading:P=\"Loading...\",options:y={},height:V=\"100%\",width:z=\"100%\",className:F,wrapperProps:j={},beforeMount:A=D,onMount:q=D}){let[M,O]=re(!1),[T,s]=re(!0),u=S(null),c=S(null),w=S(null),d=S(q),o=S(A),b=S(!1);k(()=>{let i=Se.init();return i.then(f=>(c.current=f)&&s(!1)).catch(f=>f?.type!==\"cancelation\"&&console.error(\"Monaco initialization: error:\",f)),()=>u.current?I():i.cancel()}),l(()=>{if(u.current&&c.current){let i=u.current.getOriginalEditor(),f=h(c.current,e||\"\",t||n||\"text\",m||\"\");f!==i.getModel()&&i.setModel(f)}},[m],M),l(()=>{if(u.current&&c.current){let i=u.current.getModifiedEditor(),f=h(c.current,r||\"\",a||n||\"text\",E||\"\");f!==i.getModel()&&i.setModel(f)}},[E],M),l(()=>{let i=u.current.getModifiedEditor();i.getOption(c.current.editor.EditorOption.readOnly)?i.setValue(r||\"\"):r!==i.getValue()&&(i.executeEdits(\"\",[{range:i.getModel().getFullModelRange(),text:r||\"\",forceMoveMarkers:!0}]),i.pushUndoStop())},[r],M),l(()=>{u.current?.getModel()?.original.setValue(e||\"\")},[e],M),l(()=>{let{original:i,modified:f}=u.current.getModel();c.current.editor.setModelLanguage(i,t||n||\"text\"),c.current.editor.setModelLanguage(f,a||n||\"text\")},[n,t,a],M),l(()=>{c.current?.editor.setTheme(x)},[x],M),l(()=>{u.current?.updateOptions(y)},[y],M);let L=oe(()=>{if(!c.current)return;o.current(c.current);let i=h(c.current,e||\"\",t||n||\"text\",m||\"\"),f=h(c.current,r||\"\",a||n||\"text\",E||\"\");u.current?.setModel({original:i,modified:f})},[n,r,a,e,t,m,E]),U=oe(()=>{!b.current&&w.current&&(u.current=c.current.editor.createDiffEditor(w.current,{automaticLayout:!0,...y}),L(),c.current?.editor.setTheme(x),O(!0),b.current=!0)},[y,x,L]);ne(()=>{M&&d.current(u.current,c.current)},[M]),ne(()=>{!T&&!M&&U()},[T,M,U]);function I(){let i=u.current?.getModel();g||i?.original?.dispose(),N||i?.modified?.dispose(),u.current?.dispose()}return ke.createElement(H,{width:z,height:V,isEditorReady:M,loading:P,_ref:w,className:F,wrapperProps:j})}var ie=Oe;var we=Te(ie);import{useState as Ie}from\"react\";import ce from\"@monaco-editor/loader\";function Pe(){let[e,r]=Ie(ce.__getMonacoInstance());return k(()=>{let n;return e||(n=ce.init(),n.then(t=>{r(t)})),()=>n?.cancel()}),e}var Le=Pe;import{memo as ze}from\"react\";import We,{useState as ue,useEffect as W,useRef as C,useCallback as _e}from\"react\";import Ne from\"@monaco-editor/loader\";import{useEffect as Ue,useRef as ve}from\"react\";function He(e){let r=ve();return Ue(()=>{r.current=e},[e]),r.current}var se=He;var _=new Map;function Ve({defaultValue:e,defaultLanguage:r,defaultPath:n,value:t,language:a,path:m,theme:E=\"light\",line:g,loading:N=\"Loading...\",options:x={},overrideServices:P={},saveViewState:y=!0,keepCurrentModel:V=!1,width:z=\"100%\",height:F=\"100%\",className:j,wrapperProps:A={},beforeMount:q=D,onMount:M=D,onChange:O,onValidate:T=D}){let[s,u]=ue(!1),[c,w]=ue(!0),d=C(null),o=C(null),b=C(null),L=C(M),U=C(q),I=C(),i=C(t),f=se(m),Q=C(!1),B=C(!1);k(()=>{let p=Ne.init();return p.then(R=>(d.current=R)&&w(!1)).catch(R=>R?.type!==\"cancelation\"&&console.error(\"Monaco initialization: error:\",R)),()=>o.current?pe():p.cancel()}),l(()=>{let p=h(d.current,e||t||\"\",r||a||\"\",m||n||\"\");p!==o.current?.getModel()&&(y&&_.set(f,o.current?.saveViewState()),o.current?.setModel(p),y&&o.current?.restoreViewState(_.get(m)))},[m],s),l(()=>{o.current?.updateOptions(x)},[x],s),l(()=>{!o.current||t===void 0||(o.current.getOption(d.current.editor.EditorOption.readOnly)?o.current.setValue(t):t!==o.current.getValue()&&(B.current=!0,o.current.executeEdits(\"\",[{range:o.current.getModel().getFullModelRange(),text:t,forceMoveMarkers:!0}]),o.current.pushUndoStop(),B.current=!1))},[t],s),l(()=>{let p=o.current?.getModel();p&&a&&d.current?.editor.setModelLanguage(p,a)},[a],s),l(()=>{g!==void 0&&o.current?.revealLine(g)},[g],s),l(()=>{d.current?.editor.setTheme(E)},[E],s);let X=_e(()=>{if(!(!b.current||!d.current)&&!Q.current){U.current(d.current);let p=m||n,R=h(d.current,t||e||\"\",r||a||\"\",p||\"\");o.current=d.current?.editor.create(b.current,{model:R,automaticLayout:!0,...x},P),y&&o.current.restoreViewState(_.get(p)),d.current.editor.setTheme(E),g!==void 0&&o.current.revealLine(g),u(!0),Q.current=!0}},[e,r,n,t,a,m,x,P,y,E,g]);W(()=>{s&&L.current(o.current,d.current)},[s]),W(()=>{!c&&!s&&X()},[c,s,X]),i.current=t,W(()=>{s&&O&&(I.current?.dispose(),I.current=o.current?.onDidChangeModelContent(p=>{B.current||O(o.current.getValue(),p)}))},[s,O]),W(()=>{if(s){let p=d.current.editor.onDidChangeMarkers(R=>{let G=o.current.getModel()?.uri;if(G&&R.find(J=>J.path===G.path)){let J=d.current.editor.getModelMarkers({resource:G});T?.(J)}});return()=>{p?.dispose()}}return()=>{}},[s,T]);function pe(){I.current?.dispose(),V?y&&_.set(m,o.current.saveViewState()):o.current.getModel()?.dispose(),o.current.dispose()}return We.createElement(H,{width:z,height:F,isEditorReady:s,loading:N,_ref:b,className:j,wrapperProps:A})}var fe=Ve;var de=ze(fe);var Ft=de;export{we as DiffEditor,de as Editor,Ft as default,_t as loader,Le as useMonaco};\n//# sourceMappingURL=index.mjs.map", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst ArrowUpDown = createLucideIcon(\"ArrowUpDown\", [\n  [\"path\", { d: \"m21 16-4 4-4-4\", key: \"f6ql7i\" }],\n  [\"path\", { d: \"M17 20V4\", key: \"1ejh1v\" }],\n  [\"path\", { d: \"m3 8 4-4 4 4\", key: \"11wl7u\" }],\n  [\"path\", { d: \"M7 4v16\", key: \"1glfcx\" }]\n]);\n\nexport { ArrowUpDown as default };\n//# sourceMappingURL=arrow-up-down.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst Calendar = createLucideIcon(\"Calendar\", [\n  [\n    \"rect\",\n    {\n      width: \"18\",\n      height: \"18\",\n      x: \"3\",\n      y: \"4\",\n      rx: \"2\",\n      ry: \"2\",\n      key: \"eu3xkr\"\n    }\n  ],\n  [\"line\", { x1: \"16\", x2: \"16\", y1: \"2\", y2: \"6\", key: \"m3sa8f\" }],\n  [\"line\", { x1: \"8\", x2: \"8\", y1: \"2\", y2: \"6\", key: \"18kwsl\" }],\n  [\"line\", { x1: \"3\", x2: \"21\", y1: \"10\", y2: \"10\", key: \"xt86sb\" }]\n]);\n\nexport { Calendar as default };\n//# sourceMappingURL=calendar.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst GraduationCap = createLucideIcon(\"GraduationCap\", [\n  [\"path\", { d: \"M22 10v6M2 10l10-5 10 5-10 5z\", key: \"1ef52a\" }],\n  [\"path\", { d: \"M6 12v5c3 3 9 3 12 0v-5\", key: \"1f75yj\" }]\n]);\n\nexport { GraduationCap as default };\n//# sourceMappingURL=graduation-cap.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst Hash = createLucideIcon(\"Hash\", [\n  [\"line\", { x1: \"4\", x2: \"20\", y1: \"9\", y2: \"9\", key: \"4lhtct\" }],\n  [\"line\", { x1: \"4\", x2: \"20\", y1: \"15\", y2: \"15\", key: \"vyu0kd\" }],\n  [\"line\", { x1: \"10\", x2: \"8\", y1: \"3\", y2: \"21\", key: \"1ggp8o\" }],\n  [\"line\", { x1: \"16\", x2: \"14\", y1: \"3\", y2: \"21\", key: \"weycgp\" }]\n]);\n\nexport { Hash as default };\n//# sourceMappingURL=hash.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst ToggleLeft = createLucideIcon(\"ToggleLeft\", [\n  [\n    \"rect\",\n    {\n      width: \"20\",\n      height: \"12\",\n      x: \"2\",\n      y: \"6\",\n      rx: \"6\",\n      ry: \"6\",\n      key: \"f2vt7d\"\n    }\n  ],\n  [\"circle\", { cx: \"8\", cy: \"12\", r: \"2\", key: \"1nvbw3\" }]\n]);\n\nexport { ToggleLeft as default };\n//# sourceMappingURL=toggle-left.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst Type = createLucideIcon(\"Type\", [\n  [\"polyline\", { points: \"4 7 4 4 20 4 20 7\", key: \"1nosan\" }],\n  [\"line\", { x1: \"9\", x2: \"15\", y1: \"20\", y2: \"20\", key: \"swin9y\" }],\n  [\"line\", { x1: \"12\", x2: \"12\", y1: \"4\", y2: \"20\", key: \"1tx1rr\" }]\n]);\n\nexport { Type as default };\n//# sourceMappingURL=type.mjs.map\n", "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n}\n\nfunction curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len3 = arguments.length, nextArgs = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        nextArgs[_key3] = arguments[_key3];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nfunction isObject(value) {\n  return {}.toString.call(value).includes('Object');\n}\n\nfunction isEmpty(obj) {\n  return !Object.keys(obj).length;\n}\n\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n\nfunction hasOwnProperty(object, property) {\n  return Object.prototype.hasOwnProperty.call(object, property);\n}\n\nfunction validateChanges(initial, changes) {\n  if (!isObject(changes)) errorHandler('changeType');\n  if (Object.keys(changes).some(function (field) {\n    return !hasOwnProperty(initial, field);\n  })) errorHandler('changeField');\n  return changes;\n}\n\nfunction validateSelector(selector) {\n  if (!isFunction(selector)) errorHandler('selectorType');\n}\n\nfunction validateHandler(handler) {\n  if (!(isFunction(handler) || isObject(handler))) errorHandler('handlerType');\n  if (isObject(handler) && Object.values(handler).some(function (_handler) {\n    return !isFunction(_handler);\n  })) errorHandler('handlersType');\n}\n\nfunction validateInitial(initial) {\n  if (!initial) errorHandler('initialIsRequired');\n  if (!isObject(initial)) errorHandler('initialType');\n  if (isEmpty(initial)) errorHandler('initialContent');\n}\n\nfunction throwError(errorMessages, type) {\n  throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\n\nvar errorMessages = {\n  initialIsRequired: 'initial state is required',\n  initialType: 'initial state should be an object',\n  initialContent: 'initial state shouldn\\'t be an empty object',\n  handlerType: 'handler should be an object or a function',\n  handlersType: 'all handlers should be a functions',\n  selectorType: 'selector should be a function',\n  changeType: 'provided value of changes should be an object',\n  changeField: 'it seams you want to change a field in the state which is not specified in the \"initial\" state',\n  \"default\": 'an unknown error accured in `state-local` package'\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n  changes: validateChanges,\n  selector: validateSelector,\n  handler: validateHandler,\n  initial: validateInitial\n};\n\nfunction create(initial) {\n  var handler = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  validators.initial(initial);\n  validators.handler(handler);\n  var state = {\n    current: initial\n  };\n  var didUpdate = curry(didStateUpdate)(state, handler);\n  var update = curry(updateState)(state);\n  var validate = curry(validators.changes)(initial);\n  var getChanges = curry(extractChanges)(state);\n\n  function getState() {\n    var selector = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : function (state) {\n      return state;\n    };\n    validators.selector(selector);\n    return selector(state.current);\n  }\n\n  function setState(causedChanges) {\n    compose(didUpdate, update, validate, getChanges)(causedChanges);\n  }\n\n  return [getState, setState];\n}\n\nfunction extractChanges(state, causedChanges) {\n  return isFunction(causedChanges) ? causedChanges(state.current) : causedChanges;\n}\n\nfunction updateState(state, changes) {\n  state.current = _objectSpread2(_objectSpread2({}, state.current), changes);\n  return changes;\n}\n\nfunction didStateUpdate(state, handler, changes) {\n  isFunction(handler) ? handler(state.current) : Object.keys(changes).forEach(function (field) {\n    var _handler$field;\n\n    return (_handler$field = handler[field]) === null || _handler$field === void 0 ? void 0 : _handler$field.call(handler, state.current[field]);\n  });\n  return changes;\n}\n\nvar index = {\n  create: create\n};\n\nexport default index;\n"], "names": [], "sourceRoot": ""}