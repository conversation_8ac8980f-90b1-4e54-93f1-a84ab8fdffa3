import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ElasticsearchService } from '../services/elasticsearch';

export interface QueryCondition {
  id: string;
  field: string;
  operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'greater_than' | 'less_than' | 'exists' | 'not_exists';
  value: string;
  logicalOperator?: 'AND' | 'OR';
}

export interface VisualQuery {
  conditions: QueryCondition[];
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
  size?: number;
  from?: number;
}

export interface SavedQuery {
  id: string;
  name: string;
  description?: string;
  query: any;
  index: string;
  queryMode: 'visual' | 'dsl';
  createdAt: Date;
  updatedAt: Date;
  tags?: string[];
  isFavorite?: boolean;
}

export interface QueryHistoryItem {
  id: string;
  query: any;
  index: string;
  queryMode: 'visual' | 'dsl';
  executedAt: Date;
  resultCount?: number;
  executionTime?: number;
  isFavorite?: boolean;
}

export interface QueryState {
  // Current query state
  selectedIndex: string;
  queryMode: 'visual' | 'dsl';
  visualQuery: VisualQuery;
  dslQuery: string;
  
  // Results
  searchResults: any;
  isSearching: boolean;
  lastExecutionTime?: number;
  
  // History and saved queries
  queryHistory: QueryHistoryItem[];
  savedQueries: SavedQuery[];
  
  // Actions
  setSelectedIndex: (index: string) => void;
  setQueryMode: (mode: 'visual' | 'dsl') => void;
  setVisualQuery: (query: VisualQuery) => void;
  setDslQuery: (query: string) => void;
  updateVisualQuery: (updates: Partial<VisualQuery>) => void;
  
  // Condition management
  addCondition: () => void;
  removeCondition: (id: string) => void;
  updateCondition: (id: string, updates: Partial<QueryCondition>) => void;
  
  // Query execution
  executeQuery: () => Promise<void>;
  clearResults: () => void;
  
  // History management
  addToHistory: (item: Omit<QueryHistoryItem, 'id' | 'executedAt'>) => void;
  clearHistory: () => void;
  toggleHistoryFavorite: (id: string) => void;
  
  // Saved queries management
  saveQuery: (name: string, description?: string, tags?: string[]) => void;
  loadSavedQuery: (id: string) => void;
  deleteSavedQuery: (id: string) => void;
  updateSavedQuery: (id: string, updates: Partial<SavedQuery>) => void;
  toggleSavedQueryFavorite: (id: string) => void;
  
  // Import/Export
  exportQueries: () => string;
  importQueries: (data: string) => void;
  
  // Reset
  reset: () => void;
}

const generateId = () => Math.random().toString(36).substr(2, 9);

const defaultVisualQuery: VisualQuery = {
  conditions: [],
  size: 50,
  from: 0,
};

const initialState = {
  selectedIndex: '',
  queryMode: 'visual' as const,
  visualQuery: defaultVisualQuery,
  dslQuery: JSON.stringify({
    query: {
      match_all: {}
    }
  }, null, 2),
  searchResults: null,
  isSearching: false,
  queryHistory: [],
  savedQueries: [],
};

export const useQueryStore = create<QueryState>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      // Basic setters
      setSelectedIndex: (index: string) => set({ selectedIndex: index }),
      setQueryMode: (mode: 'visual' | 'dsl') => set({ queryMode: mode }),
      setVisualQuery: (query: VisualQuery) => set({ visualQuery: query }),
      setDslQuery: (query: string) => set({ dslQuery: query }),
      updateVisualQuery: (updates: Partial<VisualQuery>) => 
        set(state => ({ 
          visualQuery: { ...state.visualQuery, ...updates } 
        })),
      
      // Condition management
      addCondition: () => {
        const newCondition: QueryCondition = {
          id: generateId(),
          field: '',
          operator: 'equals',
          value: '',
          logicalOperator: get().visualQuery.conditions.length > 0 ? 'AND' : undefined,
        };
        
        set(state => ({
          visualQuery: {
            ...state.visualQuery,
            conditions: [...state.visualQuery.conditions, newCondition]
          }
        }));
      },
      
      removeCondition: (id: string) => {
        set(state => ({
          visualQuery: {
            ...state.visualQuery,
            conditions: state.visualQuery.conditions.filter(c => c.id !== id)
          }
        }));
      },
      
      updateCondition: (id: string, updates: Partial<QueryCondition>) => {
        set(state => ({
          visualQuery: {
            ...state.visualQuery,
            conditions: state.visualQuery.conditions.map(c => 
              c.id === id ? { ...c, ...updates } : c
            )
          }
        }));
      },
      
      // Query execution
      executeQuery: async () => {
        const state = get();
        if (!state.selectedIndex) return;
        
        set({ isSearching: true });
        
        try {
          const esService = ElasticsearchService.getInstance();
          const startTime = Date.now();
          
          let query: any;
          
          if (state.queryMode === 'visual') {
            // Convert visual query to Elasticsearch query
            query = convertVisualQueryToES(state.visualQuery);
          } else {
            // Parse DSL query
            try {
              query = JSON.parse(state.dslQuery);
            } catch (error) {
              throw new Error('Invalid JSON in DSL query');
            }
          }
          
          const result = await esService.search(state.selectedIndex, query);
          const executionTime = Date.now() - startTime;
          
          set({ 
            searchResults: result,
            isSearching: false,
            lastExecutionTime: executionTime
          });
          
          // Add to history
          get().addToHistory({
            query: state.queryMode === 'visual' ? state.visualQuery : query,
            index: state.selectedIndex,
            queryMode: state.queryMode,
            resultCount: result?.hits?.total?.value || 0,
            executionTime
          });
          
        } catch (error) {
          console.error('Query execution failed:', error);
          set({ 
            isSearching: false,
            searchResults: null 
          });
          throw error;
        }
      },
      
      clearResults: () => set({ searchResults: null }),
      
      // History management
      addToHistory: (item: Omit<QueryHistoryItem, 'id' | 'executedAt'>) => {
        const historyItem: QueryHistoryItem = {
          ...item,
          id: generateId(),
          executedAt: new Date(),
        };
        
        set(state => ({
          queryHistory: [historyItem, ...state.queryHistory.slice(0, 99)] // Keep last 100
        }));
      },
      
      clearHistory: () => set({ queryHistory: [] }),
      
      toggleHistoryFavorite: (id: string) => {
        set(state => ({
          queryHistory: state.queryHistory.map(item =>
            item.id === id ? { ...item, isFavorite: !item.isFavorite } : item
          )
        }));
      },
      
      // Saved queries management
      saveQuery: (name: string, description?: string, tags?: string[]) => {
        const state = get();
        const savedQuery: SavedQuery = {
          id: generateId(),
          name,
          description,
          query: state.queryMode === 'visual' ? state.visualQuery : JSON.parse(state.dslQuery),
          index: state.selectedIndex,
          queryMode: state.queryMode,
          createdAt: new Date(),
          updatedAt: new Date(),
          tags,
          isFavorite: false,
        };
        
        set(state => ({
          savedQueries: [...state.savedQueries, savedQuery]
        }));
      },
      
      loadSavedQuery: (id: string) => {
        const state = get();
        const savedQuery = state.savedQueries.find(q => q.id === id);
        if (!savedQuery) return;
        
        set({
          selectedIndex: savedQuery.index,
          queryMode: savedQuery.queryMode,
          visualQuery: savedQuery.queryMode === 'visual' ? savedQuery.query : defaultVisualQuery,
          dslQuery: savedQuery.queryMode === 'dsl' ? JSON.stringify(savedQuery.query, null, 2) : state.dslQuery,
        });
      },
      
      deleteSavedQuery: (id: string) => {
        set(state => ({
          savedQueries: state.savedQueries.filter(q => q.id !== id)
        }));
      },
      
      updateSavedQuery: (id: string, updates: Partial<SavedQuery>) => {
        set(state => ({
          savedQueries: state.savedQueries.map(q =>
            q.id === id ? { ...q, ...updates, updatedAt: new Date() } : q
          )
        }));
      },
      
      toggleSavedQueryFavorite: (id: string) => {
        set(state => ({
          savedQueries: state.savedQueries.map(q =>
            q.id === id ? { ...q, isFavorite: !q.isFavorite } : q
          )
        }));
      },
      
      // Import/Export
      exportQueries: () => {
        const state = get();
        return JSON.stringify({
          savedQueries: state.savedQueries,
          queryHistory: state.queryHistory,
          exportedAt: new Date().toISOString(),
        }, null, 2);
      },
      
      importQueries: (data: string) => {
        try {
          const imported = JSON.parse(data);
          if (imported.savedQueries) {
            set(state => ({
              savedQueries: [...state.savedQueries, ...imported.savedQueries]
            }));
          }
          if (imported.queryHistory) {
            set(state => ({
              queryHistory: [...imported.queryHistory, ...state.queryHistory].slice(0, 100)
            }));
          }
        } catch (error) {
          throw new Error('Invalid import data format');
        }
      },
      
      // Reset
      reset: () => set(initialState),
    }),
    {
      name: 'query-store',
      partialize: (state) => ({
        savedQueries: state.savedQueries,
        queryHistory: state.queryHistory,
      }),
    }
  )
);

// Helper function to convert visual query to Elasticsearch query
function convertVisualQueryToES(visualQuery: VisualQuery): any {
  if (visualQuery.conditions.length === 0) {
    return {
      query: { match_all: {} },
      size: visualQuery.size || 50,
      from: visualQuery.from || 0,
      ...(visualQuery.sortField && {
        sort: [{ [visualQuery.sortField]: { order: visualQuery.sortOrder || 'asc' } }]
      })
    };
  }
  
  const boolQuery: any = {
    must: [],
    should: [],
    must_not: []
  };
  
  let currentGroup = 'must';
  
  visualQuery.conditions.forEach((condition, index) => {
    if (index > 0 && condition.logicalOperator === 'OR') {
      currentGroup = 'should';
    }
    
    const esCondition = convertConditionToES(condition);
    if (esCondition) {
      boolQuery[currentGroup].push(esCondition);
    }
  });
  
  // Clean up empty arrays
  Object.keys(boolQuery).forEach(key => {
    if (boolQuery[key].length === 0) {
      delete boolQuery[key];
    }
  });
  
  return {
    query: { bool: boolQuery },
    size: visualQuery.size || 50,
    from: visualQuery.from || 0,
    ...(visualQuery.sortField && {
      sort: [{ [visualQuery.sortField]: { order: visualQuery.sortOrder || 'asc' } }]
    })
  };
}

function convertConditionToES(condition: QueryCondition): any {
  if (!condition.field) return null;
  
  switch (condition.operator) {
    case 'equals':
      return { term: { [condition.field]: condition.value } };
    case 'contains':
      return { match: { [condition.field]: condition.value } };
    case 'starts_with':
      return { prefix: { [condition.field]: condition.value } };
    case 'ends_with':
      return { wildcard: { [condition.field]: `*${condition.value}` } };
    case 'greater_than':
      return { range: { [condition.field]: { gt: condition.value } } };
    case 'less_than':
      return { range: { [condition.field]: { lt: condition.value } } };
    case 'exists':
      return { exists: { field: condition.field } };
    case 'not_exists':
      return { bool: { must_not: { exists: { field: condition.field } } } };
    default:
      return null;
  }
}