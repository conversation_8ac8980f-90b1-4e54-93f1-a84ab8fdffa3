"use strict";
(global["webpackChunkes_client"] = global["webpackChunkes_client"] || []).push([["src_renderer_components_Query_QueryBuilder_tsx"],{

/***/ "./src/renderer/components/Data/FieldAutocomplete.tsx":
/*!************************************************************!*\
  !*** ./src/renderer/components/Data/FieldAutocomplete.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FieldAutocomplete: () => (/* binding */ FieldAutocomplete),
/* harmony export */   useFieldMapping: () => (/* binding */ useFieldMapping)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/calendar.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/hash.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/list.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/search.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/star.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/toggle-left.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/trending-up.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/type.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/zap.mjs");
/* harmony import */ var _services_FieldMappingService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../services/FieldMappingService */ "./src/renderer/services/FieldMappingService.ts");
/* harmony import */ var _services_IntelligentQueryAssistant__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../services/IntelligentQueryAssistant */ "./src/renderer/services/IntelligentQueryAssistant.ts");
/* harmony import */ var _hooks_useDebounce__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../hooks/useDebounce */ "./src/renderer/hooks/useDebounce.ts");






const FieldAutocomplete = ({ index, value, onChange, placeholder = '输入字段名...', className = '', showValueSuggestions = false, type = 'field', fieldName, onFieldSelect }) => {
    const [suggestions, setSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(-1);
    const [showAdvanced, setShowAdvanced] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
    const service = _services_FieldMappingService__WEBPACK_IMPORTED_MODULE_11__.FieldMappingService.getInstance();
    const intelligentAssistant = _services_IntelligentQueryAssistant__WEBPACK_IMPORTED_MODULE_12__.IntelligentQueryAssistant.getInstance();
    const debouncedValue = (0,_hooks_useDebounce__WEBPACK_IMPORTED_MODULE_13__.useDebounce)(value, 200); // Reduced debounce for faster response
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
        if (debouncedValue.trim() && index) {
            loadSuggestions(debouncedValue);
        }
        else {
            setSuggestions([]);
            setIsOpen(false);
        }
    }, [debouncedValue, index, type, fieldName]);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current &&
                !dropdownRef.current.contains(event.target) &&
                inputRef.current &&
                !inputRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);
    const loadSuggestions = async (query) => {
        if (!index)
            return;
        setIsLoading(true);
        try {
            let newSuggestions = [];
            if (type === 'field') {
                // Use intelligent assistant for enhanced field suggestions
                const intelligentSuggestions = await intelligentAssistant.getFieldSuggestions(index, query);
                // Convert to FieldSuggestion format and add enhanced metadata
                newSuggestions = intelligentSuggestions.map(suggestion => ({
                    label: suggestion.name,
                    type: suggestion.type,
                    description: suggestion.description || `${suggestion.type} field`,
                    score: suggestion.popularity,
                    popularity: suggestion.popularity,
                    isNested: suggestion.isNested,
                    parentPath: suggestion.parentPath,
                    examples: suggestion.examples
                }));
                // Also get traditional suggestions as fallback
                const fallbackSuggestions = await service.getFieldSuggestions(index, query);
                // Merge and deduplicate suggestions
                const mergedSuggestions = new Map();
                [...newSuggestions, ...fallbackSuggestions].forEach(suggestion => {
                    const key = 'label' in suggestion ? suggestion.label : suggestion.value;
                    if (!mergedSuggestions.has(key) ||
                        (mergedSuggestions.get(key).score || 0) < (suggestion.score || 0)) {
                        mergedSuggestions.set(key, suggestion);
                    }
                });
                newSuggestions = Array.from(mergedSuggestions.values());
            }
            else if (type === 'value' && fieldName) {
                // Use intelligent assistant for enhanced value suggestions
                const intelligentValueSuggestions = await intelligentAssistant.getValueSuggestions(index, fieldName, query);
                // Convert to ValueSuggestion format
                newSuggestions = intelligentValueSuggestions.map(suggestion => ({
                    value: suggestion.value,
                    count: Math.round(suggestion.frequency * 1000), // Convert frequency to count
                    score: suggestion.frequency,
                    type: suggestion.type,
                    isExact: suggestion.isExact,
                    description: suggestion.description
                }));
                // Also get traditional suggestions as fallback
                const fallbackSuggestions = await service.getValueSuggestions(index, fieldName, query);
                // Merge suggestions
                const mergedSuggestions = new Map();
                [...newSuggestions, ...fallbackSuggestions].forEach(suggestion => {
                    const key = String(suggestion.value);
                    if (!mergedSuggestions.has(key) ||
                        (mergedSuggestions.get(key).score || 0) < (suggestion.score || 0)) {
                        mergedSuggestions.set(key, suggestion);
                    }
                });
                newSuggestions = Array.from(mergedSuggestions.values());
            }
            // Sort suggestions by score/relevance
            newSuggestions.sort((a, b) => (b.score || 0) - (a.score || 0));
            setSuggestions(newSuggestions.slice(0, 15)); // Limit to top 15 suggestions
            setIsOpen(newSuggestions.length > 0);
            setSelectedIndex(-1);
        }
        catch (error) {
            console.error('Failed to load suggestions:', error);
            setSuggestions([]);
            setIsOpen(false);
        }
        finally {
            setIsLoading(false);
        }
    };
    const handleInputChange = (e) => {
        onChange(e.target.value);
        setSelectedIndex(-1);
    };
    const handleKeyDown = (e) => {
        if (!isOpen || suggestions.length === 0)
            return;
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                setSelectedIndex(prev => (prev + 1) % suggestions.length);
                break;
            case 'ArrowUp':
                e.preventDefault();
                setSelectedIndex(prev => (prev - 1 + suggestions.length) % suggestions.length);
                break;
            case 'Enter':
                e.preventDefault();
                if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
                    selectSuggestion(suggestions[selectedIndex]);
                }
                break;
            case 'Escape':
                setIsOpen(false);
                break;
        }
    };
    const selectSuggestion = (suggestion) => {
        if (type === 'field') {
            const fieldSuggestion = suggestion;
            onChange(fieldSuggestion.label);
            onFieldSelect?.(fieldSuggestion.label);
        }
        else {
            const valueSuggestion = suggestion;
            onChange(valueSuggestion.value);
        }
        setIsOpen(false);
        setSelectedIndex(-1);
    };
    const getFieldIcon = (type) => {
        switch (type) {
            case 'text':
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_9__["default"], { className: "w-4 h-4" });
            case 'keyword':
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_3__["default"], { className: "w-4 h-4" });
            case 'date':
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_2__["default"], { className: "w-4 h-4" });
            case 'boolean':
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_7__["default"], { className: "w-4 h-4" });
            case 'long':
            case 'integer':
            case 'double':
            case 'float':
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_3__["default"], { className: "w-4 h-4" });
            default:
                return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], { className: "w-4 h-4" });
        }
    };
    const getTypeColor = (type) => {
        switch (type) {
            case 'text':
                return 'text-blue-600 bg-blue-50';
            case 'keyword':
                return 'text-green-600 bg-green-50';
            case 'date':
                return 'text-purple-600 bg-purple-50';
            case 'boolean':
                return 'text-orange-600 bg-orange-50';
            case 'long':
            case 'integer':
            case 'double':
            case 'float':
                return 'text-red-600 bg-red-50';
            default:
                return 'text-gray-600 bg-gray-50';
        }
    };
    const renderFieldSuggestion = (suggestion) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3 flex-1 min-w-0", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-shrink-0", children: getFieldIcon(suggestion.type) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1 min-w-0", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "font-medium text-sm text-gray-900 dark:text-white truncate", children: suggestion.label }), suggestion.popularity > 0.7 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_6__["default"], { className: "w-3 h-3 text-yellow-500 flex-shrink-0", title: "Popular field" })), suggestion.isNested && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-xs bg-blue-100 text-blue-700 px-1 rounded", children: "nested" }))] }), suggestion.description && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-gray-500 dark:text-gray-400 truncate mt-0.5", children: suggestion.description })), suggestion.parentPath && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-xs text-gray-400 dark:text-gray-500 truncate", children: ["in ", suggestion.parentPath] }))] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2 flex-shrink-0", children: [suggestion.popularity > 0.5 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_8__["default"], { className: "w-3 h-3 text-green-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "text-xs text-green-600", children: [Math.round(suggestion.popularity * 100), "%"] })] })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: `text-xs px-2 py-1 rounded-full ${getTypeColor(suggestion.type)}`, children: suggestion.type })] })] }));
    const renderValueSuggestion = (suggestion) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center space-x-2 flex-1 min-w-0", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1 min-w-0", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-900 dark:text-white font-medium truncate", children: String(suggestion.value) }), suggestion.isExact && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-xs bg-green-100 text-green-700 px-1 rounded", children: "exact" }))] }), suggestion.description && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-gray-500 dark:text-gray-400 truncate mt-0.5", children: suggestion.description }))] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex items-center space-x-2 flex-shrink-0", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "text-xs text-gray-500 dark:text-gray-400", children: [suggestion.count.toLocaleString(), " \u6761"] }), suggestion.count > 100 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_10__["default"], { className: "w-3 h-3 text-orange-500", title: "High frequency value" }))] }) })] }));
    const renderSuggestion = (suggestion, index) => {
        const isSelected = index === selectedIndex;
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: `${isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : ''} border-b border-gray-100 dark:border-gray-700 last:border-b-0`, onClick: () => selectSuggestion(suggestion), children: type === 'field'
                ? renderFieldSuggestion(suggestion)
                : renderValueSuggestion(suggestion) }, index));
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "relative", ref: dropdownRef, children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "relative", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { ref: inputRef, type: "text", value: value, onChange: handleInputChange, onKeyDown: handleKeyDown, onFocus: () => {
                            if (suggestions.length > 0) {
                                setIsOpen(true);
                            }
                        }, placeholder: placeholder, className: `w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white ${className}` }), isLoading && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute right-3 top-1/2 transform -translate-y-1/2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full" }) })), !isLoading && !value && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "absolute right-3 top-1/2 transform -translate-y-1/2", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], { className: "w-4 h-4 text-gray-400" }) }))] }), isOpen && suggestions.length > 0 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-80 overflow-y-auto", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "px-3 py-2 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { className: "text-xs font-medium text-gray-600 dark:text-gray-300", children: [type === 'field' ? '字段建议' : '值建议', " (", suggestions.length, ")"] }), type === 'field' && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button", { onClick: () => setShowAdvanced(!showAdvanced), className: "text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400", children: showAdvanced ? '简化视图' : '详细视图' }))] }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "py-1", children: suggestions.map((suggestion, index) => renderSuggestion(suggestion, index)) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "px-3 py-2 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-gray-500 dark:text-gray-400", children: type === 'field'
                                ? '使用 ↑↓ 键导航，Enter 选择，Esc 关闭'
                                : '显示最常用的值，按使用频率排序' }) })] }))] }));
};
// 自定义Hook用于字段映射
const useFieldMapping = (index) => {
    const [mapping, setMapping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const service = _services_FieldMappingService__WEBPACK_IMPORTED_MODULE_11__.FieldMappingService.getInstance();
    const loadMapping = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async () => {
        if (!index) {
            setMapping(null);
            return;
        }
        setIsLoading(true);
        try {
            const result = await service.getIndexMapping(index);
            setMapping(result);
        }
        catch (error) {
            console.error('Failed to load field mapping:', error);
            setMapping(null);
        }
        finally {
            setIsLoading(false);
        }
    }, [index, service]);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
        loadMapping();
    }, [loadMapping]);
    const refreshMapping = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(() => {
        if (index) {
            service.refreshMapping(index);
            loadMapping();
        }
    }, [index, service, loadMapping]);
    return {
        mapping,
        isLoading,
        refreshMapping
    };
};


/***/ }),

/***/ "./src/renderer/components/Query/DSLQueryEditor.tsx":
/*!**********************************************************!*\
  !*** ./src/renderer/components/Query/DSLQueryEditor.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ DSLQueryEditor)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ "./node_modules/@monaco-editor/react/dist/index.mjs");
/* harmony import */ var _stores_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../stores/query */ "./src/renderer/stores/query.ts");
/* harmony import */ var _stores_theme__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../stores/theme */ "./src/renderer/stores/theme.ts");
/* harmony import */ var _Data_FieldAutocomplete__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../Data/FieldAutocomplete */ "./src/renderer/components/Data/FieldAutocomplete.tsx");
/* harmony import */ var _services_FieldMappingService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../services/FieldMappingService */ "./src/renderer/services/FieldMappingService.ts");
/* harmony import */ var _services_IntelligentQueryAssistant__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../services/IntelligentQueryAssistant */ "./src/renderer/services/IntelligentQueryAssistant.ts");
/* harmony import */ var _UI_Card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../UI/Card */ "./src/renderer/components/UI/Card.tsx");
/* harmony import */ var _UI_Button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../UI/Button */ "./src/renderer/components/UI/Button.tsx");
/* harmony import */ var _UI_Badge__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../UI/Badge */ "./src/renderer/components/UI/Badge.tsx");
/* harmony import */ var _Learning_DSLCodeDisplay__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../Learning/DSLCodeDisplay */ "./src/renderer/components/Learning/DSLCodeDisplay.tsx");
/* harmony import */ var _Learning_LearningSupport__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../Learning/LearningSupport */ "./src/renderer/components/Learning/LearningSupport.tsx");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/alert-circle.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/book-open.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/check.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/graduation-cap.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/lightbulb.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/sparkles.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/zap.mjs");














function DSLQueryEditor() {
    const { dslQuery, setDslQuery, selectedIndex } = (0,_stores_query__WEBPACK_IMPORTED_MODULE_3__.useQueryStore)();
    const { theme } = (0,_stores_theme__WEBPACK_IMPORTED_MODULE_4__.useThemeStore)();
    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
    const { mapping } = (0,_Data_FieldAutocomplete__WEBPACK_IMPORTED_MODULE_5__.useFieldMapping)(selectedIndex || '');
    const fieldService = _services_FieldMappingService__WEBPACK_IMPORTED_MODULE_6__.FieldMappingService.getInstance();
    const intelligentAssistant = _services_IntelligentQueryAssistant__WEBPACK_IMPORTED_MODULE_7__.IntelligentQueryAssistant.getInstance();
    const [optimizations, setOptimizations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [showOptimizations, setShowOptimizations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);
    const [queryComplexity, setQueryComplexity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('simple');
    const [showLearningSupport, setShowLearningSupport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
    const [showDSLDisplay, setShowDSLDisplay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);
    const [parsedQuery, setParsedQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
    // Analyze query when it changes
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
        const analyzeQuery = async () => {
            if (!dslQuery.trim() || !selectedIndex) {
                setParsedQuery(null);
                return;
            }
            try {
                const parsed = JSON.parse(dslQuery);
                setParsedQuery(parsed);
                setIsAnalyzing(true);
                const analysis = await intelligentAssistant.analyzeQuery(parsed, selectedIndex);
                if (analysis) {
                    setOptimizations(analysis.optimizations);
                    setQueryComplexity(analysis.complexity);
                }
            }
            catch (error) {
                // Invalid JSON, skip analysis
                setOptimizations([]);
                setParsedQuery(null);
            }
            finally {
                setIsAnalyzing(false);
            }
        };
        const timeoutId = setTimeout(analyzeQuery, 1000); // Debounce analysis
        return () => clearTimeout(timeoutId);
    }, [dslQuery, selectedIndex]);
    const handleEditorDidMount = (editor, monaco) => {
        editorRef.current = editor;
        // Configure enhanced JSON schema for Elasticsearch query DSL
        monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
            validate: true,
            schemas: [
                {
                    uri: 'http://elasticsearch.org/query-dsl.json',
                    fileMatch: ['*'],
                    schema: {
                        type: 'object',
                        properties: {
                            query: {
                                type: 'object',
                                properties: {
                                    match_all: {
                                        type: 'object',
                                        properties: {
                                            boost: { type: 'number' }
                                        }
                                    },
                                    match: {
                                        type: 'object',
                                        patternProperties: {
                                            '.*': {
                                                oneOf: [
                                                    { type: 'string' },
                                                    {
                                                        type: 'object',
                                                        properties: {
                                                            query: { type: 'string' },
                                                            operator: { enum: ['and', 'or'] },
                                                            fuzziness: { type: 'string' },
                                                            boost: { type: 'number' }
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    },
                                    term: { type: 'object' },
                                    terms: { type: 'object' },
                                    range: {
                                        type: 'object',
                                        patternProperties: {
                                            '.*': {
                                                type: 'object',
                                                properties: {
                                                    gte: {},
                                                    gt: {},
                                                    lte: {},
                                                    lt: {},
                                                    boost: { type: 'number' }
                                                }
                                            }
                                        }
                                    },
                                    exists: {
                                        type: 'object',
                                        properties: {
                                            field: { type: 'string' }
                                        }
                                    },
                                    bool: {
                                        type: 'object',
                                        properties: {
                                            must: { type: 'array' },
                                            must_not: { type: 'array' },
                                            should: { type: 'array' },
                                            filter: { type: 'array' },
                                            minimum_should_match: { type: 'number' },
                                            boost: { type: 'number' }
                                        },
                                    },
                                    wildcard: { type: 'object' },
                                    regexp: { type: 'object' },
                                    fuzzy: { type: 'object' },
                                    prefix: { type: 'object' },
                                    query_string: {
                                        type: 'object',
                                        properties: {
                                            query: { type: 'string' },
                                            default_field: { type: 'string' },
                                            fields: { type: 'array', items: { type: 'string' } },
                                            default_operator: { enum: ['AND', 'OR'] }
                                        }
                                    },
                                    simple_query_string: { type: 'object' },
                                    multi_match: {
                                        type: 'object',
                                        properties: {
                                            query: { type: 'string' },
                                            fields: { type: 'array', items: { type: 'string' } },
                                            type: { enum: ['best_fields', 'most_fields', 'cross_fields', 'phrase', 'phrase_prefix'] }
                                        }
                                    }
                                },
                            },
                            sort: {
                                type: 'array',
                                items: {
                                    oneOf: [
                                        { type: 'string' },
                                        {
                                            type: 'object',
                                            patternProperties: {
                                                '.*': {
                                                    oneOf: [
                                                        { enum: ['asc', 'desc'] },
                                                        {
                                                            type: 'object',
                                                            properties: {
                                                                order: { enum: ['asc', 'desc'] },
                                                                missing: { enum: ['_first', '_last'] }
                                                            }
                                                        }
                                                    ]
                                                }
                                            }
                                        }
                                    ]
                                },
                            },
                            size: { type: 'number', minimum: 0, maximum: 10000 },
                            from: { type: 'number', minimum: 0 },
                            _source: {
                                oneOf: [
                                    { type: 'boolean' },
                                    { type: 'string' },
                                    { type: 'array', items: { type: 'string' } },
                                    {
                                        type: 'object',
                                        properties: {
                                            includes: { type: 'array', items: { type: 'string' } },
                                            excludes: { type: 'array', items: { type: 'string' } }
                                        }
                                    }
                                ],
                            },
                            highlight: {
                                type: 'object',
                                properties: {
                                    fields: { type: 'object' },
                                    pre_tags: { type: 'array', items: { type: 'string' } },
                                    post_tags: { type: 'array', items: { type: 'string' } }
                                }
                            },
                            aggs: { type: 'object' },
                            aggregations: { type: 'object' },
                            timeout: { type: 'string' },
                            track_total_hits: { type: 'boolean' }
                        },
                    },
                },
            ],
        });
        // Add enhanced custom completions for Elasticsearch queries
        monaco.languages.registerCompletionItemProvider('json', {
            provideCompletionItems: async (model, position) => {
                const lineContent = model.getLineContent(position.lineNumber);
                const lineUntilPosition = lineContent.substring(0, position.column - 1);
                const suggestions = [
                    // Basic query types
                    {
                        label: 'match_all',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '"match_all": {\n  "boost": 1.0\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Matches all documents with optional boost',
                        detail: 'Basic query - matches everything'
                    },
                    {
                        label: 'match',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '"match": {\n  "${1:field}": {\n    "query": "${2:value}",\n    "operator": "and"\n  }\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Full-text search with advanced options',
                        detail: 'Text search query'
                    },
                    {
                        label: 'multi_match',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '"multi_match": {\n  "query": "${1:search_text}",\n  "fields": ["${2:field1}", "${3:field2}"],\n  "type": "best_fields"\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Search across multiple fields',
                        detail: 'Multi-field text search'
                    },
                    {
                        label: 'term',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '"term": {\n  "${1:field}": {\n    "value": "${2:exact_value}",\n    "boost": 1.0\n  }\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Exact term match with boost',
                        detail: 'Exact match query'
                    },
                    {
                        label: 'terms',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '"terms": {\n  "${1:field}": ["${2:value1}", "${3:value2}"],\n  "boost": 1.0\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Match any of the provided terms',
                        detail: 'Multiple exact values'
                    },
                    {
                        label: 'range',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '"range": {\n  "${1:field}": {\n    "gte": "${2:from}",\n    "lte": "${3:to}",\n    "boost": 1.0\n  }\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Range query with boost',
                        detail: 'Numeric/date range'
                    },
                    {
                        label: 'bool',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '"bool": {\n  "must": [\n    ${1:// Required conditions}\n  ],\n  "should": [\n    ${2:// Optional conditions}\n  ],\n  "must_not": [\n    ${3:// Excluded conditions}\n  ],\n  "filter": [\n    ${4:// Filter conditions}\n  ]\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Boolean query with all clauses',
                        detail: 'Complex boolean logic'
                    },
                    {
                        label: 'exists',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '"exists": {\n  "field": "${1:field}"\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Check if field exists and has a value',
                        detail: 'Field existence check'
                    },
                    {
                        label: 'wildcard',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '"wildcard": {\n  "${1:field}": {\n    "value": "${2:pattern*}",\n    "boost": 1.0\n  }\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Wildcard pattern matching',
                        detail: 'Pattern matching with * and ?'
                    },
                    {
                        label: 'fuzzy',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '"fuzzy": {\n  "${1:field}": {\n    "value": "${2:term}",\n    "fuzziness": "AUTO"\n  }\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Fuzzy matching for typos',
                        detail: 'Typo-tolerant search'
                    },
                    // Aggregations
                    {
                        label: 'aggs_terms',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '"aggs": {\n  "${1:aggregation_name}": {\n    "terms": {\n      "field": "${2:field}",\n      "size": 10\n    }\n  }\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Terms aggregation',
                        detail: 'Group by field values'
                    },
                    {
                        label: 'aggs_date_histogram',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '"aggs": {\n  "${1:aggregation_name}": {\n    "date_histogram": {\n      "field": "${2:date_field}",\n      "calendar_interval": "day"\n    }\n  }\n}',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Date histogram aggregation',
                        detail: 'Time-based grouping'
                    },
                    // Sorting
                    {
                        label: 'sort_field',
                        kind: monaco.languages.CompletionItemKind.Snippet,
                        insertText: '"sort": [\n  {\n    "${1:field}": {\n      "order": "${2|asc,desc|}"\n    }\n  }\n]',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: 'Sort by field',
                        detail: 'Field-based sorting'
                    }
                ];
                // Add intelligent field-specific completions based on index mapping
                if (selectedIndex && mapping) {
                    // Check if we're in a field context
                    const fieldContext = lineUntilPosition.includes('"field"') ||
                        lineUntilPosition.match(/"(match|term|range|wildcard|fuzzy|exists)"\s*:\s*{\s*"?$/);
                    if (fieldContext) {
                        // Get intelligent field suggestions
                        try {
                            const partialField = lineUntilPosition.match(/"([^"]*)"?\s*$/)?.[1] || '';
                            const intelligentFields = await intelligentAssistant.getFieldSuggestions(selectedIndex, partialField);
                            const fieldSuggestions = intelligentFields.map(field => ({
                                label: field.name,
                                kind: monaco.languages.CompletionItemKind.Property,
                                insertText: `"${field.name}"`,
                                documentation: `${field.type} field - ${field.description || 'No description'}`,
                                detail: `Popularity: ${Math.round(field.popularity * 100)}%`,
                                sortText: `${1000 - Math.round(field.popularity * 1000)}_${field.name}` // Sort by popularity
                            }));
                            suggestions.push(...fieldSuggestions);
                        }
                        catch (error) {
                            // Fallback to basic field suggestions
                            const fieldSuggestions = mapping.fields.map(field => ({
                                label: field.name,
                                kind: monaco.languages.CompletionItemKind.Property,
                                insertText: `"${field.name}"`,
                                documentation: `${field.type} field - ${field.searchable ? 'searchable' : 'not searchable'}, ${field.aggregatable ? 'aggregatable' : 'not aggregatable'}`,
                            }));
                            suggestions.push(...fieldSuggestions);
                        }
                    }
                    // Add value suggestions for specific fields
                    const valueContext = lineUntilPosition.match(/"(match|term|wildcard)"\s*:\s*{\s*"([^"]+)"\s*:\s*"?$/);
                    if (valueContext) {
                        const fieldName = valueContext[2];
                        try {
                            const partialValue = lineUntilPosition.match(/"([^"]*)"?\s*$/)?.[1] || '';
                            const intelligentValues = await intelligentAssistant.getValueSuggestions(selectedIndex, fieldName, partialValue);
                            const valueSuggestions = intelligentValues.slice(0, 10).map(value => ({
                                label: String(value.value),
                                kind: monaco.languages.CompletionItemKind.Value,
                                insertText: `"${value.value}"`,
                                documentation: `Frequency: ${Math.round(value.frequency * 100)}%`,
                                detail: value.description || `${value.type} value`,
                                sortText: `${1000 - Math.round(value.frequency * 1000)}_${value.value}`
                            }));
                            suggestions.push(...valueSuggestions);
                        }
                        catch (error) {
                            console.error('Failed to get value suggestions:', error);
                        }
                    }
                    // Add sort field completions
                    const sortContext = lineUntilPosition.includes('"sort"') ||
                        lineUntilPosition.match(/"sort"\s*:\s*\[\s*{\s*"?$/);
                    if (sortContext) {
                        const sortFields = mapping.fields
                            .filter(field => field.aggregatable)
                            .map(field => ({
                            label: field.name,
                            kind: monaco.languages.CompletionItemKind.Property,
                            insertText: `"${field.name}"`,
                            documentation: `${field.type} field - sortable`,
                            detail: 'Sortable field'
                        }));
                        suggestions.push(...sortFields);
                    }
                    // Add _source field completions
                    const sourceContext = lineUntilPosition.includes('"_source"') ||
                        lineUntilPosition.match(/"_source"\s*:\s*\[?\s*"?$/);
                    if (sourceContext) {
                        const sourceFields = mapping.fields.map(field => ({
                            label: field.name,
                            kind: monaco.languages.CompletionItemKind.Property,
                            insertText: `"${field.name}"`,
                            documentation: `Include ${field.name} in results`,
                            detail: `${field.type} field`
                        }));
                        suggestions.push(...sourceFields);
                    }
                }
                return { suggestions };
            },
        });
    };
    const formatQuery = () => {
        if (editorRef.current) {
            editorRef.current.getAction('editor.action.formatDocument').run();
        }
    };
    const validateQuery = () => {
        try {
            JSON.parse(dslQuery);
            return { isValid: true, error: null };
        }
        catch (error) {
            return {
                isValid: false,
                error: error instanceof Error ? error.message : 'Invalid JSON',
            };
        }
    };
    const validation = validateQuery();
    const insertTemplate = (template) => {
        if (editorRef.current) {
            const selection = editorRef.current.getSelection();
            editorRef.current.executeEdits('', [
                {
                    range: selection,
                    text: template,
                },
            ]);
            editorRef.current.focus();
        }
    };
    const handleDSLInsert = (dsl) => {
        setDslQuery(dsl);
        if (editorRef.current) {
            editorRef.current.focus();
        }
    };
    const applyOptimization = (optimization) => {
        const optimizedQueryString = JSON.stringify(optimization.optimizedQuery, null, 2);
        setDslQuery(optimizedQueryString);
        // Remove applied optimization from list
        setOptimizations(opts => opts.filter(opt => opt !== optimization));
    };
    const getComplexityColor = (complexity) => {
        switch (complexity) {
            case 'simple': return 'text-green-600 bg-green-50';
            case 'moderate': return 'text-yellow-600 bg-yellow-50';
            case 'complex': return 'text-orange-600 bg-orange-50';
            case 'very_complex': return 'text-red-600 bg-red-50';
            default: return 'text-gray-600 bg-gray-50';
        }
    };
    const queryTemplates = [
        {
            name: 'Match All',
            category: 'basic',
            template: '{\n  "query": {\n    "match_all": {\n      "boost": 1.0\n    }\n  },\n  "size": 10\n}',
            description: '匹配所有文档'
        },
        {
            name: 'Simple Match',
            category: 'basic',
            template: '{\n  "query": {\n    "match": {\n      "field_name": {\n        "query": "search_term",\n        "operator": "and"\n      }\n    }\n  }\n}',
            description: '简单文本搜索'
        },
        {
            name: 'Multi Match',
            category: 'text',
            template: '{\n  "query": {\n    "multi_match": {\n      "query": "search_term",\n      "fields": ["field1", "field2^2"],\n      "type": "best_fields"\n    }\n  }\n}',
            description: '多字段文本搜索'
        },
        {
            name: 'Boolean Query',
            category: 'complex',
            template: '{\n  "query": {\n    "bool": {\n      "must": [\n        { "match": { "field1": "value1" } }\n      ],\n      "filter": [\n        { "term": { "field2": "value2" } }\n      ],\n      "should": [\n        { "match": { "field3": "value3" } }\n      ],\n      "minimum_should_match": 1\n    }\n  }\n}',
            description: '复杂布尔查询'
        },
        {
            name: 'Range Query',
            category: 'filter',
            template: '{\n  "query": {\n    "range": {\n      "date_field": {\n        "gte": "2023-01-01",\n        "lte": "2023-12-31",\n        "format": "yyyy-MM-dd"\n      }\n    }\n  }\n}',
            description: '范围查询'
        },
        {
            name: 'Aggregation Query',
            category: 'analytics',
            template: '{\n  "size": 0,\n  "aggs": {\n    "group_by_field": {\n      "terms": {\n        "field": "category.keyword",\n        "size": 10\n      },\n      "aggs": {\n        "avg_value": {\n          "avg": {\n            "field": "price"\n          }\n        }\n      }\n    }\n  }\n}',
            description: '聚合分析查询'
        },
        {
            name: 'Fuzzy Search',
            category: 'text',
            template: '{\n  "query": {\n    "fuzzy": {\n      "field_name": {\n        "value": "search_term",\n        "fuzziness": "AUTO",\n        "max_expansions": 50\n      }\n    }\n  }\n}',
            description: '模糊搜索'
        },
        {
            name: 'Wildcard Search',
            category: 'text',
            template: '{\n  "query": {\n    "wildcard": {\n      "field_name": {\n        "value": "*search*",\n        "boost": 1.0\n      }\n    }\n  }\n}',
            description: '通配符搜索'
        }
    ];
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_8__.Card, { className: "p-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_18__["default"], { className: "w-5 h-5 text-blue-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "font-medium text-gray-900 dark:text-white", children: "\u667A\u80FD DSL \u7F16\u8F91\u5668" }), isAnalyzing && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full" }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3", children: [validation.isValid ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1 text-green-600", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_15__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm", children: "\u8BED\u6CD5\u6B63\u786E" })] })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-1 text-red-600", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_13__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm", children: "\u8BED\u6CD5\u9519\u8BEF" })] })), validation.isValid && dslQuery.trim() && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, { className: `text-xs ${getComplexityColor(queryComplexity)}`, children: ["\u590D\u6742\u5EA6: ", queryComplexity] }))] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_9__.Button, { variant: "outline", size: "sm", onClick: formatQuery, children: "\u683C\u5F0F\u5316" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_9__.Button, { variant: "outline", size: "sm", onClick: () => setShowOptimizations(!showOptimizations), children: [showOptimizations ? '隐藏' : '显示', "\u4F18\u5316"] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_9__.Button, { variant: "outline", size: "sm", onClick: () => setShowLearningSupport(!showLearningSupport), className: "flex items-center space-x-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_16__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span", { children: [showLearningSupport ? '隐藏' : '显示', "\u5B66\u4E60"] })] })] })] }), !validation.isValid && validation.error && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_13__["default"], { className: "w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-red-700 dark:text-red-400", children: "JSON \u8BED\u6CD5\u9519\u8BEF" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm text-red-600 dark:text-red-300 mt-1", children: validation.error })] })] }) }))] }), showOptimizations && optimizations.length > 0 && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_8__.Card, { className: "p-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2 mb-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_19__["default"], { className: "w-5 h-5 text-orange-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "font-semibold text-gray-900 dark:text-white", children: "\u67E5\u8BE2\u4F18\u5316\u5EFA\u8BAE" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, { variant: "secondary", children: optimizations.length })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-3", children: optimizations.slice(0, 3).map((optimization, index) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2 mb-1", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h5", { className: "font-medium text-sm text-gray-900 dark:text-white", children: optimization.title }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, { variant: optimization.severity === 'error' ? 'destructive' :
                                                            optimization.severity === 'warning' ? 'secondary' : 'outline', className: "text-xs", children: optimization.severity })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-gray-600 dark:text-gray-400 mb-2", children: optimization.description }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-xs text-blue-600 dark:text-blue-400", children: ["\u5EFA\u8BAE: ", optimization.suggestion] }), optimization.estimatedImprovement?.performance && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p", { className: "text-xs text-green-600 dark:text-green-400 mt-1", children: ["\u9884\u671F\u63D0\u5347: ", optimization.estimatedImprovement.performance] }))] }), optimization.autoApplicable && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_9__.Button, { variant: "outline", size: "sm", onClick: () => applyOptimization(optimization), className: "ml-3 flex-shrink-0", children: "\u5E94\u7528" }))] }) }, index))) })] })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_8__.Card, { className: "p-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2 mb-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_14__["default"], { className: "w-5 h-5 text-gray-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4", { className: "font-medium text-gray-900 dark:text-white", children: "\u667A\u80FD\u67E5\u8BE2\u6A21\u677F" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3", children: queryTemplates.map(template => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-300 dark:hover:border-blue-600 cursor-pointer transition-colors", onClick: () => insertTemplate(template.template), children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between mb-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h5", { className: "font-medium text-sm text-gray-900 dark:text-white", children: template.name }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Badge__WEBPACK_IMPORTED_MODULE_10__.Badge, { variant: "outline", className: "text-xs", children: template.category })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-gray-600 dark:text-gray-400", children: template.description })] }, template.name))) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-start space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_17__["default"], { className: "w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm font-medium text-blue-800 dark:text-blue-200", children: "\u667A\u80FD\u63D0\u793A" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-xs text-blue-700 dark:text-blue-300 mt-1", children: "\u8F93\u5165\u65F6\u4F1A\u81EA\u52A8\u63D0\u4F9B\u5B57\u6BB5\u540D\u548C\u503C\u7684\u667A\u80FD\u5EFA\u8BAE\u3002\u4F7F\u7528 Ctrl+Space \u624B\u52A8\u89E6\u53D1\u81EA\u52A8\u5B8C\u6210\u3002" })] })] }) })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_8__.Card, { className: "overflow-hidden", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "h-96", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__["default"], { height: "100%", defaultLanguage: "json", theme: theme === 'dark' ? 'vs-dark' : 'vs-light', value: dslQuery, onChange: value => setDslQuery(value || ''), onMount: handleEditorDidMount, options: {
                            minimap: { enabled: false },
                            scrollBeyondLastLine: false,
                            fontSize: 14,
                            lineNumbers: 'on',
                            roundedSelection: false,
                            scrollbar: {
                                vertical: 'auto',
                                horizontal: 'auto',
                            },
                            automaticLayout: true,
                            tabSize: 2,
                            insertSpaces: true,
                            wordWrap: 'on',
                            bracketPairColorization: { enabled: true },
                            suggest: {
                                showKeywords: true,
                                showSnippets: true,
                            },
                        } }) }) }), showDSLDisplay && parsedQuery && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Learning_DSLCodeDisplay__WEBPACK_IMPORTED_MODULE_11__.DSLCodeDisplay, { query: parsedQuery, isVisible: showDSLDisplay, onToggleVisibility: () => setShowDSLDisplay(!showDSLDisplay), showExplanations: true, className: "mt-4" })), showLearningSupport && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Learning_LearningSupport__WEBPACK_IMPORTED_MODULE_12__.LearningSupport, { currentQuery: parsedQuery, userLevel: "intermediate", onDSLInsert: handleDSLInsert, className: "mt-4" }))] }));
}


/***/ }),

/***/ "./src/renderer/components/Query/QueryBuilder.tsx":
/*!********************************************************!*\
  !*** ./src/renderer/components/Query/QueryBuilder.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ QueryBuilder)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/history.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/play.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/save.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/search.mjs");
/* harmony import */ var _stores_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../stores/query */ "./src/renderer/stores/query.ts");
/* harmony import */ var _stores_connection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../stores/connection */ "./src/renderer/stores/connection.ts");
/* harmony import */ var _UI_Button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../UI/Button */ "./src/renderer/components/UI/Button.tsx");
/* harmony import */ var _UI_Card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../UI/Card */ "./src/renderer/components/UI/Card.tsx");
/* harmony import */ var _UI_Dropdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../UI/Dropdown */ "./src/renderer/components/UI/Dropdown.tsx");
/* harmony import */ var _UI_Spinner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../UI/Spinner */ "./src/renderer/components/UI/Spinner.tsx");
/* harmony import */ var _VisualQueryBuilder__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./VisualQueryBuilder */ "./src/renderer/components/Query/VisualQueryBuilder.tsx");
/* harmony import */ var _DSLQueryEditor__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./DSLQueryEditor */ "./src/renderer/components/Query/DSLQueryEditor.tsx");
/* harmony import */ var _services_elasticsearch__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../services/elasticsearch */ "./src/renderer/services/elasticsearch.ts");











function QueryBuilder() {
    const { selectedIndex, queryMode, isSearching, searchError, pageSize, setSelectedIndex, setQueryMode, executeQuery, setPageSize, clearResults, reset, } = (0,_stores_query__WEBPACK_IMPORTED_MODULE_6__.useQueryStore)();
    const { clusterInfo } = (0,_stores_connection__WEBPACK_IMPORTED_MODULE_7__.useConnectionStore)();
    const elasticsearchService = _services_elasticsearch__WEBPACK_IMPORTED_MODULE_14__.ElasticsearchService.getInstance();
    // Load real indices from Elasticsearch
    const [availableIndices, setAvailableIndices] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);
    const [isLoadingIndices, setIsLoadingIndices] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
        loadAvailableIndices();
    }, [clusterInfo]);
    const loadAvailableIndices = async () => {
        if (!clusterInfo)
            return;
        setIsLoadingIndices(true);
        try {
            const indices = await elasticsearchService.listIndices();
            setAvailableIndices(indices.map(index => index.name));
        }
        catch (error) {
            console.error('Failed to load indices:', error);
            setAvailableIndices([]);
        }
        finally {
            setIsLoadingIndices(false);
        }
    };
    const indexOptions = availableIndices.map(index => ({
        value: index,
        label: index,
    }));
    const pageSizeOptions = [
        { value: '10', label: '10 per page' },
        { value: '20', label: '20 per page' },
        { value: '50', label: '50 per page' },
        { value: '100', label: '100 per page' },
    ];
    const handleExecuteQuery = async () => {
        if (!selectedIndex) {
            return;
        }
        try {
            await executeQuery();
        }
        catch (error) {
            console.error('Query execution failed:', error);
        }
    };
    const handleSaveQuery = () => {
        // This would open a modal to save the query
        // For now, we'll just show an alert
        alert('Save query functionality will be implemented in the saved queries component');
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_9__.Card, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], { className: "w-6 h-6 text-blue-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2", { className: "text-xl font-semibold text-gray-900 dark:text-white", children: "Query Builder" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_8__.Button, { variant: "outline", size: "sm", onClick: handleSaveQuery, disabled: !selectedIndex, className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_4__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "Save Query" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_8__.Button, { variant: "outline", size: "sm", onClick: () => {
                                            /* Open history */
                                        }, className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_2__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "History" })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4 mb-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2", children: "Select Index" }), isLoadingIndices ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-2 p-2 border border-gray-300 dark:border-gray-600 rounded-md", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Spinner__WEBPACK_IMPORTED_MODULE_11__.Spinner, { size: "sm" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm text-gray-500", children: "Loading indices..." })] })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Dropdown__WEBPACK_IMPORTED_MODULE_10__.Dropdown, { options: indexOptions, value: selectedIndex, onChange: setSelectedIndex, placeholder: "Choose an index", disabled: availableIndices.length === 0 }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2", children: "Query Mode" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-4 p-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("label", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "radio", name: "queryMode", value: "visual", checked: queryMode === 'visual', onChange: () => setQueryMode('visual'), className: "text-blue-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm", children: "Visual" })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("label", { className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("input", { type: "radio", name: "queryMode", value: "dsl", checked: queryMode === 'dsl', onChange: () => setQueryMode('dsl'), className: "text-blue-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "text-sm", children: "DSL" })] })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label", { className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2", children: "Results per page" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Dropdown__WEBPACK_IMPORTED_MODULE_10__.Dropdown, { options: pageSizeOptions, value: pageSize.toString(), onChange: value => setPageSize(parseInt(value)) })] })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_8__.Button, { onClick: handleExecuteQuery, disabled: !selectedIndex || isSearching, className: "flex items-center space-x-2", children: [isSearching ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Spinner__WEBPACK_IMPORTED_MODULE_11__.Spinner, { size: "sm" })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_3__["default"], { className: "w-4 h-4" })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: isSearching ? 'Searching...' : 'Execute Query' })] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_8__.Button, { variant: "outline", onClick: clearResults, disabled: isSearching, children: "Clear Results" })] }), searchError && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-red-600 text-sm", children: ["Error: ", searchError] }))] })] }), selectedIndex ? (queryMode === 'visual' ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_VisualQueryBuilder__WEBPACK_IMPORTED_MODULE_12__["default"], {})) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_DSLQueryEditor__WEBPACK_IMPORTED_MODULE_13__["default"], {}))) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_9__.Card, { className: "p-12", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center text-gray-500 dark:text-gray-400", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], { className: "w-12 h-12 mx-auto mb-4 opacity-50" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-medium mb-2", children: "Select an Index to Start" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { children: "Choose an Elasticsearch index from the dropdown above to begin building your query." })] }) }))] }));
}
// Import the service at the top level to avoid circular dependencies



/***/ }),

/***/ "./src/renderer/components/Query/QueryCondition.tsx":
/*!**********************************************************!*\
  !*** ./src/renderer/components/Query/QueryCondition.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ QueryCondition)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/x.mjs");
/* harmony import */ var _UI_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../UI/Button */ "./src/renderer/components/UI/Button.tsx");
/* harmony import */ var _UI_Input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../UI/Input */ "./src/renderer/components/UI/Input.tsx");
/* harmony import */ var _UI_Dropdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../UI/Dropdown */ "./src/renderer/components/UI/Dropdown.tsx");
/* harmony import */ var _Data_FieldAutocomplete__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../Data/FieldAutocomplete */ "./src/renderer/components/Data/FieldAutocomplete.tsx");







const OPERATORS = [
    { value: 'equals', label: 'Equals' },
    { value: 'not_equals', label: 'Not Equals' },
    { value: 'contains', label: 'Contains' },
    { value: 'not_contains', label: 'Not Contains' },
    { value: 'exists', label: 'Exists' },
    { value: 'not_exists', label: 'Not Exists' },
    { value: 'range', label: 'Range' },
    { value: 'wildcard', label: 'Wildcard' },
    { value: 'regexp', label: 'Regex' },
];
const LOGICAL_OPERATORS = [
    { value: 'AND', label: 'AND' },
    { value: 'OR', label: 'OR' },
];
function QueryCondition({ condition, availableFields, isFirst, onUpdate, onRemove, }) {
    const renderValueInput = () => {
        if (condition.operator === 'exists' ||
            condition.operator === 'not_exists') {
            return null;
        }
        if (condition.operator === 'range') {
            const rangeValue = typeof condition.value === 'object' ? condition.value : {};
            return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Input__WEBPACK_IMPORTED_MODULE_4__.Input, { placeholder: "\u4ECE", value: rangeValue.gte || rangeValue.gt || '', onChange: e => onUpdate({
                            value: {
                                ...rangeValue,
                                gte: e.target.value || undefined,
                                gt: undefined,
                            },
                        }), className: "flex-1" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Input__WEBPACK_IMPORTED_MODULE_4__.Input, { placeholder: "\u5230", value: rangeValue.lte || rangeValue.lt || '', onChange: e => onUpdate({
                            value: {
                                ...rangeValue,
                                lte: e.target.value || undefined,
                                lt: undefined,
                            },
                        }), className: "flex-1" })] }));
        }
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Data_FieldAutocomplete__WEBPACK_IMPORTED_MODULE_6__.FieldAutocomplete, { index: index, value: condition.value || '', onChange: value => onUpdate({ value }), placeholder: "\u8F93\u5165\u503C", type: "value", fieldName: condition.field, showValueSuggestions: true, className: "flex-1" }));
    };
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700", children: [!isFirst && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-shrink-0", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Dropdown__WEBPACK_IMPORTED_MODULE_5__.Dropdown, { options: LOGICAL_OPERATORS, value: condition.logicalOperator || 'AND', onChange: value => onUpdate({ logicalOperator: value }), className: "w-20" }) })), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-shrink-0 w-48", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Data_FieldAutocomplete__WEBPACK_IMPORTED_MODULE_6__.FieldAutocomplete, { index: index, value: condition.field, onChange: value => onUpdate({ field: value }), placeholder: "\u9009\u62E9\u5B57\u6BB5", type: "field" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-shrink-0 w-32", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Dropdown__WEBPACK_IMPORTED_MODULE_5__.Dropdown, { options: OPERATORS, value: condition.operator, onChange: value => onUpdate({ operator: value }) }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-1", children: renderValueInput() }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-shrink-0", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Button__WEBPACK_IMPORTED_MODULE_3__.Button, { variant: "ghost", size: "sm", onClick: onRemove, className: "text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_2__["default"], { className: "w-4 h-4" }) }) })] }));
}


/***/ }),

/***/ "./src/renderer/components/Query/VisualQueryBuilder.tsx":
/*!**************************************************************!*\
  !*** ./src/renderer/components/Query/VisualQueryBuilder.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ VisualQueryBuilder)
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/jsx-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/arrow-up-down.mjs");
/* harmony import */ var lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lucide-react */ "./node_modules/lucide-react/dist/esm/icons/plus.mjs");
/* harmony import */ var _stores_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../stores/query */ "./src/renderer/stores/query.ts");
/* harmony import */ var _Data_FieldAutocomplete__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../Data/FieldAutocomplete */ "./src/renderer/components/Data/FieldAutocomplete.tsx");
/* harmony import */ var _UI_Button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../UI/Button */ "./src/renderer/components/UI/Button.tsx");
/* harmony import */ var _UI_Card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../UI/Card */ "./src/renderer/components/UI/Card.tsx");
/* harmony import */ var _UI_Dropdown__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../UI/Dropdown */ "./src/renderer/components/UI/Dropdown.tsx");
/* harmony import */ var _QueryCondition__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./QueryCondition */ "./src/renderer/components/Query/QueryCondition.tsx");










function VisualQueryBuilder() {
    const { selectedIndex, visualQuery, addCondition, removeCondition, updateCondition, updateVisualQuery, } = (0,_stores_query__WEBPACK_IMPORTED_MODULE_4__.useQueryStore)();
    const { mapping, isLoading } = (0,_Data_FieldAutocomplete__WEBPACK_IMPORTED_MODULE_5__.useFieldMapping)(selectedIndex || '');
    const handleSetSort = (field, order) => {
        updateVisualQuery({
            ...visualQuery,
            sortField: field,
            sortOrder: order,
        });
    };
    const handleClearSort = () => {
        updateVisualQuery({
            ...visualQuery,
            sortField: undefined,
            sortOrder: undefined,
        });
    };
    const availableFields = mapping?.fields || [];
    const fieldOptions = availableFields.map(field => ({
        value: field.name,
        label: `${field.name} (${field.type})`,
    }));
    const sortOptions = [
        { value: 'asc', label: 'Ascending' },
        { value: 'desc', label: 'Descending' },
    ];
    if (isLoading) {
        return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Card__WEBPACK_IMPORTED_MODULE_7__.Card, { className: "p-6", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-center", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { className: "ml-3 text-gray-600 dark:text-gray-400", children: "Loading index fields..." })] }) }));
    }
    return ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "space-y-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_7__.Card, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between mb-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3", { className: "text-lg font-semibold text-gray-900 dark:text-white", children: "Query Conditions" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_6__.Button, { onClick: addCondition, size: "sm", className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_3__["default"], { className: "w-4 h-4" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "Add Condition" })] })] }), visualQuery.conditions.length === 0 ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "text-center py-8 text-gray-500 dark:text-gray-400", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { children: "No conditions added. Click \"Add Condition\" to start building your query." }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { className: "text-sm mt-2", children: "Without conditions, this will return all documents." })] })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "space-y-3", children: visualQuery.conditions.map((condition, index) => ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_QueryCondition__WEBPACK_IMPORTED_MODULE_9__["default"], { condition: condition, availableFields: availableFields, isFirst: index === 0, index: selectedIndex || '', onUpdate: updates => updateCondition(condition.id, updates), onRemove: () => removeCondition(condition.id) }, condition.id))) }))] }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Card__WEBPACK_IMPORTED_MODULE_7__.Card, { className: "p-6", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center justify-between mb-4", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("h3", { className: "text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_2__["default"], { className: "w-5 h-5" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "Sort Order" })] }), visualQuery.sortField && ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_UI_Button__WEBPACK_IMPORTED_MODULE_6__.Button, { onClick: handleClearSort, size: "sm", variant: "outline", className: "flex items-center space-x-2", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(lucide_react__WEBPACK_IMPORTED_MODULE_3__["default"], { className: "w-4 h-4 rotate-45" }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span", { children: "Clear Sort" })] }))] }), !visualQuery.sortField ? ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "text-center py-6 text-gray-500 dark:text-gray-400", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p", { children: "No sorting configured. Results will be sorted by relevance score." }) })) : ((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", { className: "flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg", children: [(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "flex-1", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_Data_FieldAutocomplete__WEBPACK_IMPORTED_MODULE_5__.FieldAutocomplete, { index: selectedIndex || '', value: visualQuery.sortField, onChange: value => handleSetSort(value, visualQuery.sortOrder || 'asc'), placeholder: "\u9009\u62E9\u6392\u5E8F\u5B57\u6BB5", type: "field" }) }), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", { className: "w-32", children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_UI_Dropdown__WEBPACK_IMPORTED_MODULE_8__.Dropdown, { options: sortOptions, value: visualQuery.sortOrder || 'asc', onChange: value => handleSetSort(visualQuery.sortField || '', value) }) })] }))] })] }));
}


/***/ }),

/***/ "./src/renderer/hooks/useDebounce.ts":
/*!*******************************************!*\
  !*** ./src/renderer/hooks/useDebounce.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   useDebounce: () => (/* binding */ useDebounce)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

function useDebounce(value, delay) {
    const [debouncedValue, setDebouncedValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(value);
    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);
        return () => {
            clearTimeout(handler);
        };
    }, [value, delay]);
    return debouncedValue;
}


/***/ }),

/***/ "./src/renderer/services/FieldMappingService.ts":
/*!******************************************************!*\
  !*** ./src/renderer/services/FieldMappingService.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FieldMappingService: () => (/* binding */ FieldMappingService)
/* harmony export */ });
/* harmony import */ var _elasticsearch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./elasticsearch */ "./src/renderer/services/elasticsearch.ts");

class FieldMappingService {
    constructor() {
        this.fieldCache = new Map();
        this.suggestionCache = new Map();
        this.valueCache = new Map();
    }
    static getInstance() {
        if (!FieldMappingService.instance) {
            FieldMappingService.instance = new FieldMappingService();
        }
        return FieldMappingService.instance;
    }
    async getIndexMapping(index) {
        if (this.fieldCache.has(index)) {
            return this.fieldCache.get(index);
        }
        try {
            const elasticsearchService = _elasticsearch__WEBPACK_IMPORTED_MODULE_0__.ElasticsearchService.getInstance();
            const response = await elasticsearchService.getClient().indices.getMapping({
                index: index
            });
            const mapping = response[index]?.mappings || {};
            const properties = mapping.properties || {};
            const fields = this.extractFieldsFromProperties(properties);
            const indexMapping = {
                index,
                fields,
                timestampField: this.findTimestampField(fields),
                idField: this.findIdField(fields),
                nestedFields: this.findNestedFields(properties),
                dynamic: mapping.dynamic !== false
            };
            this.fieldCache.set(index, indexMapping);
            return indexMapping;
        }
        catch (error) {
            console.error('Failed to get index mapping:', error);
            return null;
        }
    }
    extractFieldsFromProperties(properties, prefix = '', path = '') {
        const fields = [];
        for (const [fieldName, fieldMapping] of Object.entries(properties)) {
            const fullName = prefix ? `${prefix}.${fieldName}` : fieldName;
            const fullPath = path ? `${path}.${fieldName}` : fieldName;
            if (fieldMapping && typeof fieldMapping === 'object') {
                const mapping = fieldMapping;
                // 处理多字段映射
                if (mapping.fields) {
                    // 添加主字段
                    fields.push(this.createFieldInfo(fieldName, fullName, mapping, fullPath));
                    // 添加子字段
                    for (const [subFieldName, subFieldMapping] of Object.entries(mapping.fields)) {
                        const subFullName = `${fullName}.${subFieldName}`;
                        const subFullPath = `${fullPath}.${subFieldName}`;
                        fields.push(this.createFieldInfo(subFieldName, subFullName, subFieldMapping, subFullPath));
                    }
                }
                else if (mapping.type === 'nested' || mapping.type === 'object') {
                    // 处理嵌套对象
                    fields.push({
                        name: fullName,
                        type: mapping.type,
                        searchable: false,
                        aggregatable: false,
                        isKeyword: false,
                        isText: false,
                        isNumeric: false,
                        isDate: false,
                        isBoolean: false,
                        isObject: true,
                        isArray: mapping.type === 'nested',
                        path: fullPath
                    });
                    if (mapping.properties) {
                        const nestedFields = this.extractFieldsFromProperties(mapping.properties, fullName, fullPath);
                        fields.push(...nestedFields);
                    }
                }
                else {
                    // 普通字段
                    fields.push(this.createFieldInfo(fieldName, fullName, mapping, fullPath));
                }
            }
        }
        return fields;
    }
    createFieldInfo(fieldName, fullName, mapping, path) {
        const type = mapping.type || 'text';
        return {
            name: fullName,
            type,
            format: mapping.format,
            analyzer: mapping.analyzer,
            searchable: this.isSearchable(type, mapping),
            aggregatable: this.isAggregatable(type, mapping),
            isKeyword: type === 'keyword',
            isText: type === 'text',
            isNumeric: ['long', 'integer', 'short', 'byte', 'double', 'float', 'half_float', 'scaled_float'].includes(type),
            isDate: type === 'date',
            isBoolean: type === 'boolean',
            isObject: false,
            isArray: false,
            path
        };
    }
    isSearchable(type, mapping) {
        if (type === 'text')
            return true;
        if (type === 'keyword')
            return true;
        if (['long', 'integer', 'short', 'byte', 'double', 'float', 'half_float', 'scaled_float'].includes(type))
            return true;
        if (type === 'date')
            return true;
        if (type === 'boolean')
            return true;
        return false;
    }
    isAggregatable(type, mapping) {
        if (type === 'keyword')
            return true;
        if (['long', 'integer', 'short', 'byte', 'double', 'float', 'half_float', 'scaled_float'].includes(type))
            return true;
        if (type === 'date')
            return true;
        if (type === 'boolean')
            return true;
        return false;
    }
    findTimestampField(fields) {
        const timestampFields = ['@timestamp', 'timestamp', 'created_at', 'updated_at', 'date'];
        return fields.find(field => timestampFields.includes(field.name))?.name;
    }
    findIdField(fields) {
        const idFields = ['id', '_id', 'uuid', 'identifier'];
        return fields.find(field => idFields.includes(field.name))?.name;
    }
    findNestedFields(properties) {
        const nestedFields = [];
        for (const [fieldName, fieldMapping] of Object.entries(properties)) {
            if (fieldMapping && typeof fieldMapping === 'object' && fieldMapping.type === 'nested') {
                nestedFields.push(fieldName);
            }
        }
        return nestedFields;
    }
    async getFieldSuggestions(index, query) {
        const cacheKey = `${index}:${query}`;
        if (this.suggestionCache.has(cacheKey)) {
            return this.suggestionCache.get(cacheKey);
        }
        const mapping = await this.getIndexMapping(index);
        if (!mapping)
            return [];
        const suggestions = mapping.fields
            .filter(field => {
            const matchesQuery = field.name.toLowerCase().includes(query.toLowerCase());
            const isRelevant = field.searchable || field.aggregatable;
            return matchesQuery && isRelevant;
        })
            .map(field => ({
            label: field.name,
            type: field.type,
            description: this.getFieldDescription(field),
            score: this.calculateRelevanceScore(field, query)
        }))
            .sort((a, b) => b.score - a.score)
            .slice(0, 10);
        this.suggestionCache.set(cacheKey, suggestions);
        return suggestions;
    }
    async getValueSuggestions(index, field, query, maxResults = 10) {
        const cacheKey = `${index}:${field}:${query}`;
        if (this.valueCache.has(cacheKey)) {
            return this.valueCache.get(cacheKey);
        }
        try {
            const elasticsearchService = _elasticsearch__WEBPACK_IMPORTED_MODULE_0__.ElasticsearchService.getInstance();
            const searchRequest = {
                index: index,
                size: 0,
                aggs: {
                    suggestions: {
                        terms: {
                            field: field,
                            size: maxResults * 2,
                            include: query ? `.*${query}.*` : undefined
                        }
                    }
                }
            };
            const response = await elasticsearchService.getClient().search(searchRequest);
            const buckets = response.aggregations?.suggestions?.buckets || [];
            const suggestions = buckets
                .map((bucket) => ({
                value: bucket.key,
                count: bucket.doc_count,
                score: bucket.doc_count
            }))
                .sort((a, b) => b.score - a.score)
                .slice(0, maxResults);
            this.valueCache.set(cacheKey, suggestions);
            return suggestions;
        }
        catch (error) {
            console.error('Failed to get value suggestions:', error);
            return [];
        }
    }
    async getFieldValues(index, field) {
        try {
            const elasticsearchService = _elasticsearch__WEBPACK_IMPORTED_MODULE_0__.ElasticsearchService.getInstance();
            const response = await elasticsearchService.getClient().search({
                index: index,
                size: 0,
                aggs: {
                    unique_values: {
                        terms: {
                            field: field,
                            size: 100
                        }
                    }
                }
            });
            const buckets = response.aggregations?.unique_values?.buckets || [];
            return buckets.map((bucket) => bucket.key);
        }
        catch (error) {
            console.error('Failed to get field values:', error);
            return [];
        }
    }
    getFieldDescription(field) {
        const typeDescriptions = {
            text: '全文搜索字段',
            keyword: '精确匹配字段',
            long: '长整数字段',
            integer: '整数字段',
            double: '双精度浮点数字段',
            float: '单精度浮点数字段',
            date: '日期时间字段',
            boolean: '布尔字段',
            object: '嵌套对象字段',
            nested: '嵌套数组字段'
        };
        const description = typeDescriptions[field.type] || `${field.type}类型字段`;
        const capabilities = [];
        if (field.searchable)
            capabilities.push('可搜索');
        if (field.aggregatable)
            capabilities.push('可聚合');
        return `${description}${capabilities.length > 0 ? ` (${capabilities.join(', ')})` : ''}`;
    }
    calculateRelevanceScore(field, query) {
        let score = 0;
        // 名称匹配度
        const nameMatch = field.name.toLowerCase().includes(query.toLowerCase());
        if (nameMatch)
            score += 10;
        // 前缀匹配
        if (field.name.toLowerCase().startsWith(query.toLowerCase()))
            score += 5;
        // 字段类型优先级
        if (field.isText || field.isKeyword)
            score += 3;
        if (field.isNumeric || field.isDate)
            score += 2;
        // 可搜索性和可聚合性
        if (field.searchable)
            score += 1;
        if (field.aggregatable)
            score += 1;
        return score;
    }
    clearCache(index) {
        if (index) {
            this.fieldCache.delete(index);
            // 清除相关缓存
            for (const key of this.suggestionCache.keys()) {
                if (key.startsWith(`${index}:`)) {
                    this.suggestionCache.delete(key);
                }
            }
            for (const key of this.valueCache.keys()) {
                if (key.startsWith(`${index}:`)) {
                    this.valueCache.delete(key);
                }
            }
        }
        else {
            this.fieldCache.clear();
            this.suggestionCache.clear();
            this.valueCache.clear();
        }
    }
    async refreshMapping(index) {
        this.clearCache(index);
        await this.getIndexMapping(index);
    }
}


/***/ })

}]);
//# sourceMappingURL=src_renderer_components_Query_QueryBuilder_tsx.renderer.js.map