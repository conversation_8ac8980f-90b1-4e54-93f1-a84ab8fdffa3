import React, { useMemo, useCallback } from 'react';
import { FixedSizeList as List } from 'react-window';
import { Database, Star, Pin, Clock, MoreVertical, CheckCircle, AlertCircle } from 'lucide-react';
import { Card } from '../UI/Card';
import { Button } from '../UI/Button';
import { Badge } from '../UI/Badge';

interface IndexInfo {
  name: string;
  docCount?: number;
  size?: string;
  health?: 'green' | 'yellow' | 'red';
  status?: 'open' | 'close';
  lastAccessed?: Date;
  isFavorite?: boolean;
  isPinned?: boolean;
  shards?: number;
  replicas?: number;
  createdAt?: Date;
  updatedAt?: Date;
  aliases?: string[];
  settings?: Record<string, any>;
  mapping?: Record<string, any>;
}

interface VirtualizedIndexListProps {
  indices: IndexInfo[];
  selectedIndex?: string;
  favoriteIndices: string[];
  pinnedIndices: string[];
  recentIndices: string[];
  onIndexSelect: (indexName: string) => void;
  onIndexFavorite: (indexName: string, isFavorite: boolean) => void;
  onIndexPin: (indexName: string, isPinned: boolean) => void;
  viewMode: 'list' | 'grid' | 'compact';
  height: number;
  itemHeight?: number;
  className?: string;
}

interface ItemData {
  indices: IndexInfo[];
  selectedIndex?: string;
  favoriteIndices: string[];
  pinnedIndices: string[];
  recentIndices: string[];
  onIndexSelect: (indexName: string) => void;
  onIndexFavorite: (indexName: string, isFavorite: boolean) => void;
  onIndexPin: (indexName: string, isPinned: boolean) => void;
  viewMode: 'list' | 'grid' | 'compact';
}

// 列表项渲染组件
const IndexListItem: React.FC<{ index: number; style: any; data: ItemData }> = ({
  index,
  style,
  data
}) => {
  const {
    indices,
    selectedIndex,
    favoriteIndices,
    pinnedIndices,
    recentIndices,
    onIndexSelect,
    onIndexFavorite,
    onIndexPin,
    viewMode
  } = data;

  const indexInfo = indices[index];
  if (!indexInfo) return null;

  const isSelected = selectedIndex === indexInfo.name;
  const isFavorite = favoriteIndices.includes(indexInfo.name);
  const isPinned = pinnedIndices.includes(indexInfo.name);
  const isRecent = recentIndices.includes(indexInfo.name);

  const healthColor = {
    green: 'text-green-500',
    yellow: 'text-yellow-500',
    red: 'text-red-500'
  }[indexInfo.health || 'green'];

  const healthIcon = {
    green: CheckCircle,
    yellow: AlertCircle,
    red: AlertCircle
  }[indexInfo.health || 'green'];

  const HealthIcon = healthIcon;

  const handleToggleFavorite = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onIndexFavorite(indexInfo.name, !isFavorite);
  }, [indexInfo.name, isFavorite, onIndexFavorite]);

  const handleTogglePin = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onIndexPin(indexInfo.name, !isPinned);
  }, [indexInfo.name, isPinned, onIndexPin]);

  const handleSelect = useCallback(() => {
    onIndexSelect(indexInfo.name);
  }, [indexInfo.name, onIndexSelect]);

  if (viewMode === 'compact') {
    return (
      <div style={style}>
        <div
          className={`
            flex items-center justify-between p-2 mx-2 rounded-lg cursor-pointer
            hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors
            ${isSelected ? 'bg-blue-100 dark:bg-blue-900/30' : ''}
          `}
          onClick={handleSelect}
        >
          <div className="flex items-center space-x-2 min-w-0">
            <HealthIcon className={`w-4 h-4 ${healthColor} flex-shrink-0`} />
            <div className="font-medium text-gray-900 dark:text-white truncate text-sm">
              {indexInfo.name}
            </div>
            {isPinned && <Pin className="w-3 h-3 text-blue-500 flex-shrink-0" />}
            {isFavorite && <Star className="w-3 h-3 text-yellow-500 fill-current flex-shrink-0" />}
          </div>
          
          <div className="flex items-center space-x-1 flex-shrink-0">
            <span className="text-xs text-gray-500 dark:text-gray-400 mr-2">
              {indexInfo.docCount?.toLocaleString() || '0'}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleToggleFavorite}
              className="p-1 h-5 w-5"
            >
              <Star className={`w-2.5 h-2.5 ${isFavorite ? 'text-yellow-500 fill-current' : 'text-gray-400'}`} />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={style}>
      <Card
        className={`
          mx-2 p-4 cursor-pointer transition-all duration-200
          hover:shadow-md hover:border-blue-300 dark:hover:border-blue-600
          ${isSelected ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : ''}
        `}
        onClick={handleSelect}
      >
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3 min-w-0">
            <HealthIcon className={`w-5 h-5 ${healthColor} flex-shrink-0`} />
            <div>
              <div className="font-medium text-gray-900 dark:text-white truncate">
                {indexInfo.name}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {indexInfo.docCount?.toLocaleString() || '0'} 文档
                {indexInfo.size && ` • ${indexInfo.size}`}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2 flex-shrink-0">
            {isPinned && <Pin className="w-4 h-4 text-blue-500" />}
            {isFavorite && <Star className="w-4 h-4 text-yellow-500 fill-current" />}
            {isRecent && <Clock className="w-4 h-4 text-gray-400" />}
            <Badge variant={indexInfo.status === 'open' ? 'default' : 'secondary'}>
              {indexInfo.status === 'open' ? '开启' : '关闭'}
            </Badge>
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
            {indexInfo.shards && (
              <span>分片: {indexInfo.shards}</span>
            )}
            {indexInfo.replicas !== undefined && (
              <span>副本: {indexInfo.replicas}</span>
            )}
          </div>
          
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleToggleFavorite}
              className="p-1"
            >
              <Star className={`w-4 h-4 ${isFavorite ? 'text-yellow-500 fill-current' : 'text-gray-400'}`} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleTogglePin}
              className="p-1"
            >
              <Pin className={`w-4 h-4 ${isPinned ? 'text-blue-500' : 'text-gray-400'}`} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="p-1"
            >
              <MoreVertical className="w-4 h-4 text-gray-400" />
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export const VirtualizedIndexList: React.FC<VirtualizedIndexListProps> = ({
  indices,
  selectedIndex,
  favoriteIndices,
  pinnedIndices,
  recentIndices,
  onIndexSelect,
  onIndexFavorite,
  onIndexPin,
  viewMode,
  height,
  itemHeight = viewMode === 'compact' ? 48 : 120,
  className = ''
}) => {
  const itemData: ItemData = useMemo(() => ({
    indices,
    selectedIndex,
    favoriteIndices,
    pinnedIndices,
    recentIndices,
    onIndexSelect,
    onIndexFavorite,
    onIndexPin,
    viewMode
  }), [
    indices,
    selectedIndex,
    favoriteIndices,
    pinnedIndices,
    recentIndices,
    onIndexSelect,
    onIndexFavorite,
    onIndexPin,
    viewMode
  ]);

  if (indices.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Database className="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-600 dark:text-gray-400">暂无索引</p>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <List
        height={height}
        itemCount={indices.length}
        itemSize={itemHeight}
        itemData={itemData}
        overscanCount={5}
      >
        {IndexListItem}
      </List>
    </div>
  );
};

export default VirtualizedIndexList;
