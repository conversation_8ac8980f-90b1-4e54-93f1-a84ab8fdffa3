import { create } from 'zustand';
import { persist, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// 索引信息接口
export interface IndexInfo {
  name: string;
  docCount?: number;
  size?: string;
  health?: 'green' | 'yellow' | 'red';
  status?: 'open' | 'close';
  shards?: number;
  replicas?: number;
  createdAt?: Date;
  updatedAt?: Date;
  aliases?: string[];
  settings?: Record<string, any>;
  mapping?: Record<string, any>;
}

// 标签页信息接口
export interface TabInfo {
  id: string;
  indexName: string;
  title: string;
  isActive: boolean;
  hasUnsavedChanges?: boolean;
  searchQuery?: string;
  searchResults?: any;
  lastUpdated?: Date;
  viewMode?: 'browser' | 'editor' | 'batch' | 'query';
  metadata?: Partial<IndexInfo>;
}

// 数据操作历史记录
export interface DataOperationHistory {
  id: string;
  indexName: string;
  operation: 'create' | 'update' | 'delete' | 'bulk' | 'query';
  timestamp: Date;
  details: {
    documentId?: string;
    query?: any;
    affectedCount?: number;
    success: boolean;
    errorMessage?: string;
  };
}

// 统一数据状态接口
export interface UnifiedDataState {
  // 索引管理状态
  indices: IndexInfo[];
  selectedIndex: string | null;
  favoriteIndices: string[];
  pinnedIndices: string[];
  recentIndices: string[];
  
  // 标签页管理状态
  tabs: TabInfo[];
  activeTabId: string | null;
  maxTabs: number;
  
  // 数据操作状态
  isLoading: boolean;
  error: string | null;
  lastRefresh: Date | null;
  
  // 操作历史
  operationHistory: DataOperationHistory[];
  maxHistorySize: number;
  
  // 用户偏好
  preferences: {
    autoRefresh: boolean;
    autoRefreshInterval: number;
    defaultViewMode: 'browser' | 'editor' | 'batch' | 'query';
    enableMultiTab: boolean;
    showOperationHistory: boolean;
    cacheEnabled: boolean;
    preloadEnabled: boolean;
  };
  
  // 同步状态
  syncState: {
    lastSyncTime: Date | null;
    syncInProgress: boolean;
    syncError: string | null;
    pendingChanges: string[];
  };
}

// 状态操作接口
export interface UnifiedDataActions {
  // 索引管理操作
  setIndices: (indices: IndexInfo[]) => void;
  updateIndex: (indexName: string, updates: Partial<IndexInfo>) => void;
  setSelectedIndex: (indexName: string | null) => void;
  addToFavorites: (indexName: string) => void;
  removeFromFavorites: (indexName: string) => void;
  toggleFavorite: (indexName: string) => void;
  addToPinned: (indexName: string) => void;
  removeFromPinned: (indexName: string) => void;
  togglePinned: (indexName: string) => void;
  addToRecent: (indexName: string) => void;
  clearRecent: () => void;
  
  // 标签页管理操作
  createTab: (tab: Omit<TabInfo, 'id'>) => string;
  updateTab: (tabId: string, updates: Partial<TabInfo>) => void;
  closeTab: (tabId: string) => void;
  setActiveTab: (tabId: string) => void;
  closeAllTabs: () => void;
  duplicateTab: (tabId: string) => void;
  
  // 数据操作状态
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setLastRefresh: (date: Date) => void;
  
  // 操作历史
  addOperationHistory: (operation: Omit<DataOperationHistory, 'id'>) => void;
  clearOperationHistory: () => void;
  removeOperationHistory: (id: string) => void;
  
  // 用户偏好
  updatePreferences: (preferences: Partial<UnifiedDataState['preferences']>) => void;
  
  // 同步操作
  setSyncState: (syncState: Partial<UnifiedDataState['syncState']>) => void;
  addPendingChange: (change: string) => void;
  removePendingChange: (change: string) => void;
  clearPendingChanges: () => void;
  
  // 批量操作
  batchUpdate: (updates: Partial<UnifiedDataState>) => void;
  reset: () => void;
}

// 默认状态
const defaultState: UnifiedDataState = {
  indices: [],
  selectedIndex: null,
  favoriteIndices: [],
  pinnedIndices: [],
  recentIndices: [],
  
  tabs: [],
  activeTabId: null,
  maxTabs: 5,
  
  isLoading: false,
  error: null,
  lastRefresh: null,
  
  operationHistory: [],
  maxHistorySize: 100,
  
  preferences: {
    autoRefresh: false,
    autoRefreshInterval: 30000,
    defaultViewMode: 'browser',
    enableMultiTab: true,
    showOperationHistory: true,
    cacheEnabled: true,
    preloadEnabled: true,
  },
  
  syncState: {
    lastSyncTime: null,
    syncInProgress: false,
    syncError: null,
    pendingChanges: [],
  },
};

// 生成唯一ID
const generateId = () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

// 创建Zustand store
export const useUnifiedDataStore = create<UnifiedDataState & UnifiedDataActions>()(
  persist(
    subscribeWithSelector(
      immer((set, get) => ({
        ...defaultState,
        
        // 索引管理操作
        setIndices: (indices) => set((state) => {
          state.indices = indices;
          state.lastRefresh = new Date();
        }),
        
        updateIndex: (indexName, updates) => set((state) => {
          const index = state.indices.find(i => i.name === indexName);
          if (index) {
            Object.assign(index, updates);
          }
        }),
        
        setSelectedIndex: (indexName) => set((state) => {
          state.selectedIndex = indexName;
          if (indexName) {
            // 添加到最近访问
            const recent = state.recentIndices.filter(name => name !== indexName);
            state.recentIndices = [indexName, ...recent].slice(0, 10);
          }
        }),
        
        addToFavorites: (indexName) => set((state) => {
          if (!state.favoriteIndices.includes(indexName)) {
            state.favoriteIndices.push(indexName);
          }
        }),
        
        removeFromFavorites: (indexName) => set((state) => {
          state.favoriteIndices = state.favoriteIndices.filter(name => name !== indexName);
        }),
        
        toggleFavorite: (indexName) => set((state) => {
          const isFavorite = state.favoriteIndices.includes(indexName);
          if (isFavorite) {
            state.favoriteIndices = state.favoriteIndices.filter(name => name !== indexName);
          } else {
            state.favoriteIndices.push(indexName);
          }
        }),
        
        addToPinned: (indexName) => set((state) => {
          if (!state.pinnedIndices.includes(indexName)) {
            state.pinnedIndices.push(indexName);
          }
        }),
        
        removeFromPinned: (indexName) => set((state) => {
          state.pinnedIndices = state.pinnedIndices.filter(name => name !== indexName);
        }),
        
        togglePinned: (indexName) => set((state) => {
          const isPinned = state.pinnedIndices.includes(indexName);
          if (isPinned) {
            state.pinnedIndices = state.pinnedIndices.filter(name => name !== indexName);
          } else {
            state.pinnedIndices.push(indexName);
          }
        }),
        
        addToRecent: (indexName) => set((state) => {
          const recent = state.recentIndices.filter(name => name !== indexName);
          state.recentIndices = [indexName, ...recent].slice(0, 10);
        }),
        
        clearRecent: () => set((state) => {
          state.recentIndices = [];
        }),
        
        // 标签页管理操作
        createTab: (tab) => {
          const tabId = generateId();
          const newTab: TabInfo = { ...tab, id: tabId };
          
          set((state) => {
            // 检查标签页数量限制
            if (state.tabs.length >= state.maxTabs) {
              // 关闭最旧的非活动标签页
              const oldestTab = state.tabs.find(t => !t.isActive);
              if (oldestTab) {
                state.tabs = state.tabs.filter(t => t.id !== oldestTab.id);
              }
            }
            
            // 设置其他标签页为非活动
            state.tabs.forEach(t => t.isActive = false);
            
            // 添加新标签页
            state.tabs.push(newTab);
            state.activeTabId = tabId;
          });
          
          return tabId;
        },
        
        updateTab: (tabId, updates) => set((state) => {
          const tab = state.tabs.find(t => t.id === tabId);
          if (tab) {
            Object.assign(tab, updates);
          }
        }),
        
        closeTab: (tabId) => set((state) => {
          state.tabs = state.tabs.filter(t => t.id !== tabId);
          
          // 如果关闭的是活动标签页，切换到其他标签页
          if (state.activeTabId === tabId) {
            const remainingTabs = state.tabs;
            if (remainingTabs.length > 0) {
              const newActiveTab = remainingTabs[remainingTabs.length - 1];
              state.activeTabId = newActiveTab.id;
              newActiveTab.isActive = true;
            } else {
              state.activeTabId = null;
            }
          }
        }),
        
        setActiveTab: (tabId) => set((state) => {
          state.tabs.forEach(t => t.isActive = t.id === tabId);
          state.activeTabId = tabId;
        }),
        
        closeAllTabs: () => set((state) => {
          state.tabs = [];
          state.activeTabId = null;
        }),
        
        duplicateTab: (tabId) => set((state) => {
          const originalTab = state.tabs.find(t => t.id === tabId);
          if (originalTab) {
            const newTabId = generateId();
            const duplicatedTab: TabInfo = {
              ...originalTab,
              id: newTabId,
              title: `${originalTab.title} (副本)`,
              isActive: true,
              hasUnsavedChanges: false,
            };
            
            // 设置其他标签页为非活动
            state.tabs.forEach(t => t.isActive = false);
            
            // 添加复制的标签页
            state.tabs.push(duplicatedTab);
            state.activeTabId = newTabId;
          }
        }),
        
        // 数据操作状态
        setLoading: (loading) => set((state) => {
          state.isLoading = loading;
        }),
        
        setError: (error) => set((state) => {
          state.error = error;
        }),
        
        setLastRefresh: (date) => set((state) => {
          state.lastRefresh = date;
        }),
        
        // 操作历史
        addOperationHistory: (operation) => set((state) => {
          const historyItem: DataOperationHistory = {
            ...operation,
            id: generateId(),
          };
          
          state.operationHistory.unshift(historyItem);
          
          // 限制历史记录数量
          if (state.operationHistory.length > state.maxHistorySize) {
            state.operationHistory = state.operationHistory.slice(0, state.maxHistorySize);
          }
        }),
        
        clearOperationHistory: () => set((state) => {
          state.operationHistory = [];
        }),
        
        removeOperationHistory: (id) => set((state) => {
          state.operationHistory = state.operationHistory.filter(h => h.id !== id);
        }),
        
        // 用户偏好
        updatePreferences: (preferences) => set((state) => {
          Object.assign(state.preferences, preferences);
        }),
        
        // 同步操作
        setSyncState: (syncState) => set((state) => {
          Object.assign(state.syncState, syncState);
        }),
        
        addPendingChange: (change) => set((state) => {
          if (!state.syncState.pendingChanges.includes(change)) {
            state.syncState.pendingChanges.push(change);
          }
        }),
        
        removePendingChange: (change) => set((state) => {
          state.syncState.pendingChanges = state.syncState.pendingChanges.filter(c => c !== change);
        }),
        
        clearPendingChanges: () => set((state) => {
          state.syncState.pendingChanges = [];
        }),
        
        // 批量操作
        batchUpdate: (updates) => set((state) => {
          Object.assign(state, updates);
        }),
        
        reset: () => set(() => ({ ...defaultState })),
      }))
    ),
    {
      name: 'unified-data-state',
      partialize: (state) => ({
        favoriteIndices: state.favoriteIndices,
        pinnedIndices: state.pinnedIndices,
        recentIndices: state.recentIndices,
        preferences: state.preferences,
        operationHistory: state.operationHistory.slice(0, 20), // 只持久化最近20条历史
      }),
    }
  )
);

export default useUnifiedDataStore;
