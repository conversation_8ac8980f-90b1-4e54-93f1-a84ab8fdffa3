import React, { useState, useCallback, useRef, useEffect } from 'react';
import { GripVertical } from 'lucide-react';

// 调整器属性接口
interface PanelResizerProps {
  direction: 'horizontal' | 'vertical';
  initialSize: number;
  minSize?: number;
  maxSize?: number;
  onResize?: (size: number) => void;
  onResizeStart?: () => void;
  onResizeEnd?: (size: number) => void;
  disabled?: boolean;
  className?: string;
  children?: React.ReactNode;
}

// 调整状态接口
interface ResizeState {
  isResizing: boolean;
  startPosition: number;
  startSize: number;
  currentSize: number;
}

export const PanelResizer: React.FC<PanelResizerProps> = ({
  direction = 'horizontal',
  initialSize,
  minSize = 200,
  maxSize = 600,
  onResize,
  onResizeStart,
  onResizeEnd,
  disabled = false,
  className = '',
  children
}) => {
  // 状态管理
  const [resizeState, setResizeState] = useState<ResizeState>({
    isResizing: false,
    startPosition: 0,
    startSize: initialSize,
    currentSize: initialSize
  });

  // Refs
  const resizerRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 限制尺寸在有效范围内
  const clampSize = useCallback((size: number) => {
    return Math.max(minSize, Math.min(maxSize, size));
  }, [minSize, maxSize]);

  // 开始调整大小
  const handleResizeStart = useCallback((event: React.MouseEvent | React.TouchEvent) => {
    if (disabled) return;

    event.preventDefault();
    event.stopPropagation();

    const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX;
    const clientY = 'touches' in event ? event.touches[0].clientY : event.clientY;
    const startPosition = direction === 'horizontal' ? clientX : clientY;

    setResizeState(prev => ({
      ...prev,
      isResizing: true,
      startPosition,
      startSize: prev.currentSize
    }));

    onResizeStart?.();

    // 添加全局事件监听器
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', handleTouchEnd);

    // 防止文本选择
    document.body.style.userSelect = 'none';
    document.body.style.cursor = direction === 'horizontal' ? 'col-resize' : 'row-resize';
  }, [disabled, direction, onResizeStart]);

  // 处理鼠标移动
  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!resizeState.isResizing) return;

    const clientX = event.clientX;
    const clientY = event.clientY;
    const currentPosition = direction === 'horizontal' ? clientX : clientY;
    const delta = currentPosition - resizeState.startPosition;
    const newSize = clampSize(resizeState.startSize + delta);

    setResizeState(prev => ({
      ...prev,
      currentSize: newSize
    }));

    onResize?.(newSize);
  }, [resizeState.isResizing, resizeState.startPosition, resizeState.startSize, direction, clampSize, onResize]);

  // 处理触摸移动
  const handleTouchMove = useCallback((event: TouchEvent) => {
    if (!resizeState.isResizing) return;

    event.preventDefault();
    
    const touch = event.touches[0];
    const clientX = touch.clientX;
    const clientY = touch.clientY;
    const currentPosition = direction === 'horizontal' ? clientX : clientY;
    const delta = currentPosition - resizeState.startPosition;
    const newSize = clampSize(resizeState.startSize + delta);

    setResizeState(prev => ({
      ...prev,
      currentSize: newSize
    }));

    onResize?.(newSize);
  }, [resizeState.isResizing, resizeState.startPosition, resizeState.startSize, direction, clampSize, onResize]);

  // 结束调整大小
  const handleResizeEnd = useCallback(() => {
    if (!resizeState.isResizing) return;

    setResizeState(prev => ({
      ...prev,
      isResizing: false
    }));

    // 移除全局事件监听器
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);

    // 恢复默认样式
    document.body.style.userSelect = '';
    document.body.style.cursor = '';

    onResizeEnd?.(resizeState.currentSize);
  }, [resizeState.isResizing, resizeState.currentSize, handleMouseMove, handleTouchMove, onResizeEnd]);

  // 鼠标抬起处理
  const handleMouseUp = useCallback(() => {
    handleResizeEnd();
  }, [handleResizeEnd]);

  // 触摸结束处理
  const handleTouchEnd = useCallback(() => {
    handleResizeEnd();
  }, [handleResizeEnd]);

  // 双击重置大小
  const handleDoubleClick = useCallback(() => {
    if (disabled) return;

    const resetSize = initialSize;
    setResizeState(prev => ({
      ...prev,
      currentSize: resetSize
    }));

    onResize?.(resetSize);
    onResizeEnd?.(resetSize);
  }, [disabled, initialSize, onResize, onResizeEnd]);

  // 键盘调整大小
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (disabled) return;

    const step = event.shiftKey ? 50 : 10;
    let newSize = resizeState.currentSize;

    switch (event.key) {
      case 'ArrowLeft':
      case 'ArrowUp':
        newSize = clampSize(resizeState.currentSize - step);
        break;
      case 'ArrowRight':
      case 'ArrowDown':
        newSize = clampSize(resizeState.currentSize + step);
        break;
      case 'Home':
        newSize = minSize;
        break;
      case 'End':
        newSize = maxSize;
        break;
      case 'Enter':
      case ' ':
        newSize = initialSize;
        break;
      default:
        return;
    }

    event.preventDefault();
    
    setResizeState(prev => ({
      ...prev,
      currentSize: newSize
    }));

    onResize?.(newSize);
  }, [disabled, resizeState.currentSize, clampSize, minSize, maxSize, initialSize, onResize]);

  // 清理事件监听器
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
      
      // 恢复默认样式
      document.body.style.userSelect = '';
      document.body.style.cursor = '';
    };
  }, [handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);

  // 更新当前大小
  useEffect(() => {
    setResizeState(prev => ({
      ...prev,
      currentSize: initialSize
    }));
  }, [initialSize]);

  // 计算调整器样式
  const resizerStyles: React.CSSProperties = {
    position: 'relative',
    flexShrink: 0,
    backgroundColor: resizeState.isResizing ? 'rgba(59, 130, 246, 0.3)' : 'transparent',
    cursor: disabled ? 'default' : (direction === 'horizontal' ? 'col-resize' : 'row-resize'),
    transition: resizeState.isResizing ? 'none' : 'background-color 0.2s ease',
    ...(direction === 'horizontal' ? {
      width: '4px',
      height: '100%',
      borderLeft: '1px solid transparent',
      borderRight: '1px solid transparent'
    } : {
      width: '100%',
      height: '4px',
      borderTop: '1px solid transparent',
      borderBottom: '1px solid transparent'
    })
  };

  // 计算握柄样式
  const gripStyles: React.CSSProperties = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    color: disabled ? '#d1d5db' : (resizeState.isResizing ? '#3b82f6' : '#6b7280'),
    transition: 'color 0.2s ease',
    pointerEvents: 'none',
    ...(direction === 'vertical' && {
      transform: 'translate(-50%, -50%) rotate(90deg)'
    })
  };

  return (
    <div
      ref={containerRef}
      className={`panel-resizer ${className} ${resizeState.isResizing ? 'is-resizing' : ''}`}
      style={{ display: 'flex', flexDirection: direction === 'horizontal' ? 'row' : 'column' }}
    >
      {children}
      
      <div
        ref={resizerRef}
        className={`resizer-handle ${disabled ? 'disabled' : ''}`}
        style={resizerStyles}
        onMouseDown={handleResizeStart}
        onTouchStart={handleResizeStart}
        onDoubleClick={handleDoubleClick}
        onKeyDown={handleKeyDown}
        tabIndex={disabled ? -1 : 0}
        role="separator"
        aria-orientation={direction}
        aria-valuenow={resizeState.currentSize}
        aria-valuemin={minSize}
        aria-valuemax={maxSize}
        aria-label={`调整${direction === 'horizontal' ? '宽度' : '高度'}`}
      >
        <GripVertical 
          size={12} 
          style={gripStyles}
          className="grip-icon"
        />
        
        {/* 悬停提示区域 */}
        <div
          className="hover-area"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            ...(direction === 'horizontal' ? {
              left: '-2px',
              right: '-2px'
            } : {
              top: '-2px',
              bottom: '-2px'
            })
          }}
        />
      </div>
    </div>
  );
};
