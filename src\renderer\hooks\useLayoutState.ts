import { useState, useEffect, useCallback, useRef } from 'react';
import { LayoutMode } from '../components/Data/UnifiedDataManagementPage';

// 布局状态接口
interface LayoutState {
  mode: LayoutMode;
  breakpoint: 'mobile' | 'tablet' | 'desktop';
  indexPanelWidth: number;
  isIndexPanelCollapsed: boolean;
  isResponsive: boolean;
  isResizing: boolean;
}

// 布局偏好接口
interface LayoutPreferences {
  defaultMode: LayoutMode;
  indexPanelWidth: number;
  showIndexPanel: boolean;
  enableAutoCollapse: boolean;
  mobileBreakpoint: number;
  tabletBreakpoint: number;
}

// Hook 选项接口
interface UseLayoutStateOptions {
  initialMode?: LayoutMode;
  preferences?: Partial<LayoutPreferences>;
  onLayoutChange?: (state: LayoutState) => void;
  enablePersistence?: boolean;
}

// 默认偏好设置
const DEFAULT_PREFERENCES: LayoutPreferences = {
  defaultMode: 'sidebar',
  indexPanelWidth: 320,
  showIndexPanel: true,
  enableAutoCollapse: true,
  mobileBreakpoint: 768,
  tabletBreakpoint: 1024
};

// 默认布局状态
const DEFAULT_LAYOUT_STATE: LayoutState = {
  mode: 'sidebar',
  breakpoint: 'desktop',
  indexPanelWidth: 320,
  isIndexPanelCollapsed: false,
  isResponsive: true,
  isResizing: false
};

// 本地存储键
const STORAGE_KEY = 'unified-data-management-layout';

export const useLayoutState = (options: UseLayoutStateOptions = {}) => {
  const {
    initialMode = 'sidebar',
    preferences: userPreferences = {},
    onLayoutChange,
    enablePersistence = true
  } = options;

  // 合并偏好设置
  const preferences = { ...DEFAULT_PREFERENCES, ...userPreferences };

  // 状态管理
  const [layoutState, setLayoutState] = useState<LayoutState>(() => {
    if (enablePersistence) {
      try {
        const stored = localStorage.getItem(STORAGE_KEY);
        if (stored) {
          const parsedState = JSON.parse(stored);
          return { ...DEFAULT_LAYOUT_STATE, ...parsedState, mode: initialMode };
        }
      } catch (error) {
        console.warn('Failed to load layout state from localStorage:', error);
      }
    }
    
    return {
      ...DEFAULT_LAYOUT_STATE,
      mode: initialMode,
      indexPanelWidth: preferences.indexPanelWidth,
      isIndexPanelCollapsed: !preferences.showIndexPanel
    };
  });

  // Refs
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const layoutChangeCallbackRef = useRef(onLayoutChange);

  // 更新回调引用
  useEffect(() => {
    layoutChangeCallbackRef.current = onLayoutChange;
  }, [onLayoutChange]);

  // 持久化状态
  const persistState = useCallback((state: LayoutState) => {
    if (!enablePersistence) return;

    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
    } catch (error) {
      console.warn('Failed to persist layout state:', error);
    }
  }, [enablePersistence]);

  // 检测断点
  const detectBreakpoint = useCallback((width: number): 'mobile' | 'tablet' | 'desktop' => {
    if (width < preferences.mobileBreakpoint) return 'mobile';
    if (width < preferences.tabletBreakpoint) return 'tablet';
    return 'desktop';
  }, [preferences.mobileBreakpoint, preferences.tabletBreakpoint]);

  // 根据断点自动调整布局
  const adjustLayoutForBreakpoint = useCallback((breakpoint: 'mobile' | 'tablet' | 'desktop') => {
    setLayoutState(prev => {
      if (!prev.isResponsive) return prev;

      let newMode: LayoutMode = prev.mode;
      let isCollapsed = prev.isIndexPanelCollapsed;

      switch (breakpoint) {
        case 'mobile':
          newMode = 'mobile';
          isCollapsed = preferences.enableAutoCollapse;
          break;
        
        case 'tablet':
          newMode = 'tabs';
          isCollapsed = false;
          break;
        
        case 'desktop':
          newMode = preferences.defaultMode;
          isCollapsed = !preferences.showIndexPanel;
          break;
      }

      const newState = {
        ...prev,
        mode: newMode,
        breakpoint,
        isIndexPanelCollapsed: isCollapsed
      };

      return newState;
    });
  }, [preferences]);

  // 处理窗口大小变化
  const handleResize = useCallback(() => {
    const width = window.innerWidth;
    const newBreakpoint = detectBreakpoint(width);
    
    setLayoutState(prev => {
      if (newBreakpoint === prev.breakpoint) return prev;
      
      const newState = { ...prev, breakpoint: newBreakpoint };
      adjustLayoutForBreakpoint(newBreakpoint);
      return newState;
    });
  }, [detectBreakpoint, adjustLayoutForBreakpoint]);

  // 监听窗口大小变化
  useEffect(() => {
    // 初始检测
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
    };
  }, [handleResize]);

  // 监听状态变化并触发回调
  useEffect(() => {
    layoutChangeCallbackRef.current?.(layoutState);
    persistState(layoutState);
  }, [layoutState, persistState]);

  // 切换布局模式
  const switchLayoutMode = useCallback((mode: LayoutMode) => {
    setLayoutState(prev => ({
      ...prev,
      mode,
      isResponsive: false // 手动切换后禁用自动响应
    }));
  }, []);

  // 切换索引面板可见性
  const toggleIndexPanel = useCallback(() => {
    setLayoutState(prev => ({
      ...prev,
      isIndexPanelCollapsed: !prev.isIndexPanelCollapsed
    }));
  }, []);

  // 调整索引面板宽度
  const resizeIndexPanel = useCallback((width: number) => {
    const clampedWidth = Math.max(200, Math.min(600, width));
    
    setLayoutState(prev => ({
      ...prev,
      indexPanelWidth: clampedWidth,
      isResizing: true
    }));

    // 延迟重置 resizing 状态
    if (resizeTimeoutRef.current) {
      clearTimeout(resizeTimeoutRef.current);
    }
    
    resizeTimeoutRef.current = setTimeout(() => {
      setLayoutState(prev => ({
        ...prev,
        isResizing: false
      }));
    }, 150);
  }, []);

  // 启用/禁用响应式模式
  const toggleResponsiveMode = useCallback(() => {
    setLayoutState(prev => {
      const newResponsive = !prev.isResponsive;
      
      if (newResponsive) {
        // 重新应用断点逻辑
        adjustLayoutForBreakpoint(prev.breakpoint);
      }
      
      return {
        ...prev,
        isResponsive: newResponsive
      };
    });
  }, [adjustLayoutForBreakpoint]);

  // 重置布局到默认状态
  const resetLayout = useCallback(() => {
    const resetState = {
      ...DEFAULT_LAYOUT_STATE,
      mode: preferences.defaultMode,
      indexPanelWidth: preferences.indexPanelWidth,
      isIndexPanelCollapsed: !preferences.showIndexPanel
    };
    
    setLayoutState(resetState);
  }, [preferences]);

  // 获取当前布局的 CSS 类名
  const getLayoutClasses = useCallback(() => {
    const classes = ['unified-data-management'];
    
    classes.push(`layout-${layoutState.mode}`);
    classes.push(`breakpoint-${layoutState.breakpoint}`);
    
    if (layoutState.isIndexPanelCollapsed) {
      classes.push('index-panel-collapsed');
    }
    
    if (layoutState.isResponsive) {
      classes.push('responsive-enabled');
    }
    
    if (layoutState.isResizing) {
      classes.push('is-resizing');
    }
    
    return classes.join(' ');
  }, [layoutState]);

  // 获取索引面板样式
  const getIndexPanelStyles = useCallback((): React.CSSProperties => {
    const baseStyles: React.CSSProperties = {
      transition: layoutState.isResizing ? 'none' : 'width 0.3s ease, transform 0.3s ease'
    };

    if (layoutState.mode === 'sidebar') {
      return {
        ...baseStyles,
        width: layoutState.isIndexPanelCollapsed ? 0 : layoutState.indexPanelWidth,
        transform: layoutState.isIndexPanelCollapsed ? 'translateX(-100%)' : 'translateX(0)'
      };
    }

    if (layoutState.mode === 'mobile') {
      return {
        ...baseStyles,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 10,
        transform: layoutState.isIndexPanelCollapsed ? 'translateY(-100%)' : 'translateY(0)'
      };
    }

    return baseStyles;
  }, [layoutState]);

  // 检查是否为移动端布局
  const isMobile = layoutState.breakpoint === 'mobile' || layoutState.mode === 'mobile';

  // 检查是否为平板端布局
  const isTablet = layoutState.breakpoint === 'tablet';

  // 检查是否为桌面端布局
  const isDesktop = layoutState.breakpoint === 'desktop';

  return {
    // 状态
    layoutState,
    isMobile,
    isTablet,
    isDesktop,
    
    // 操作方法
    switchLayoutMode,
    toggleIndexPanel,
    resizeIndexPanel,
    toggleResponsiveMode,
    resetLayout,
    
    // 样式辅助方法
    getLayoutClasses,
    getIndexPanelStyles,
    
    // 偏好设置
    preferences
  };
};
