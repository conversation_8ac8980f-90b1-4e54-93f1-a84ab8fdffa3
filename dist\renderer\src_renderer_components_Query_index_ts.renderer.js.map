{"version": 3, "file": "src_renderer_components_Query_index_ts.renderer.js", "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;;AAEuD;;AAEvD,4BAA4B,iEAAgB;AAC5C,aAAa,mCAAmC;AAChD,aAAa,6BAA6B;AAC1C,aAAa,8BAA8B;AAC3C,aAAa,6BAA6B;AAC1C,aAAa,8BAA8B;AAC3C;;AAE0C;AAC1C;;;;;;;;;;;;;;;;ACfA;AACA;AACA;;AAEuD;;AAEvD,0BAA0B,iEAAgB;AAC1C,aAAa,kCAAkC;AAC/C,aAAa,6BAA6B;AAC1C,aAAa,8BAA8B;AAC3C,aAAa,8BAA8B;AAC3C,aAAa,+BAA+B;AAC5C;;AAEwC;AACxC;;;;;;;;;;;;;;;;ACfA;AACA;AACA;;AAEuD;;AAEvD,aAAa,iEAAgB;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAE2B;AAC3B;;;;;;;;;;;;;;;;ACxBA;AACA;AACA;;AAEuD;;AAEvD,eAAe,iEAAgB;AAC/B,eAAe,0CAA0C;AACzD,eAAe,0CAA0C;AACzD,eAAe,2CAA2C;AAC1D;AACA;AACA,MAAM;AACN;AACA,aAAa,iEAAiE;AAC9E;;AAE6B;AAC7B;;;;;;;;;;;;;;;;AClBA;AACA;AACA;;AAEuD;;AAEvD,gBAAgB,iEAAgB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,aAAa,qDAAqD;AAClE;;AAE8B;AAC9B;;;;;;;;;;;;;;;;ACtBA;AACA;AACA;;AAEuD;;AAEvD,YAAY,iEAAgB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,8BAA8B;AAC3C;;AAE0B;AAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClB+D;AACd;AACgK;AAC9J;AACb;AACJ;AACE;AACA;AACrB;AACf,YAAY,sKAAsK,EAAE,6DAAa;AACjM,wCAAwC,+CAAQ;AAChD,4CAA4C,+CAAQ;AACpD,gCAAgC,+CAAQ;AACxC,sCAAsC,+CAAQ;AAC9C,4CAA4C,+CAAQ;AACpD,4CAA4C,+CAAQ;AACpD,gCAAgC,+CAAQ;AACxC,kDAAkD,+CAAQ;AAC1D,gDAAgD,+CAAQ;AACxD,8CAA8C,+CAAQ;AACtD,4DAA4D,+CAAQ;AACpE,8CAA8C,+CAAQ;AACtD;AACA,uBAAuB,8CAAO;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA,0BAA0B,8CAAO;AACjC;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA,4BAA4B,8CAAO;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,yDAAa;AACxC,uBAAuB,yDAAa;AACpC,yBAAyB,yDAAa;AACtC,sBAAsB,yDAAa;AACnC;AACA;AACA,QAAQ,yDAAa;AACrB;AACA;AACA,kFAAkF,yDAAa;AAC/F,qGAAqG,yDAAa;AAClH,SAAS;AACT;AACA;AACA;AACA,QAAQ,yDAAa;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,mBAAmB,IAAI,sDAAsD;AACvH;AACA;AACA,+BAA+B,yDAAa;AAC5C,2BAA2B,yDAAa;AACxC,6BAA6B,yDAAa;AAC1C,0BAA0B,yDAAa;AACvC;AACA,YAAY,yDAAa;AACzB;AACA;AACA,sFAAsF,yDAAa;AACnG,yGAAyG,yDAAa;AACtH,aAAa;AACb;AACA;AACA,YAAY,yDAAa;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,uBAAuB,qBAAqB,oDAAoD;AAC5H;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,0BAA0B;AACxE;AACA;AACA;AACA,8CAA8C,uCAAuC;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,gBAAgB,yBAAyB,sBAAsB,WAAW,yBAAyB;AAClJ;AACA;AACA,+CAA+C,gBAAgB;AAC/D;AACA;AACA;AACA,+CAA+C,MAAM;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,KAAK,SAAS,yBAAyB;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,iBAAiB,EAAE,oBAAoB,EAAE,gBAAgB;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,uDAAK,UAAU,mCAAmC,uDAAK,CAAC,2CAAI,IAAI,6BAA6B,uDAAK,UAAU,gEAAgE,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,qDAAO,IAAI,oCAAoC,GAAG,sDAAI,SAAS,yGAAyG,GAAG,uDAAK,CAAC,6CAAK,IAAI,uFAAuF,GAAG,uDAAK,CAAC,6CAAK,IAAI,qHAAqH,IAAI,GAAG,uDAAK,UAAU,qDAAqD,sDAAI,YAAY,yGAAyG,GAAG,uDAAK,CAAC,+CAAM,IAAI,4IAA4I,sDAAI,CAAC,qDAAM,IAAI,sBAAsB,GAAG,sDAAI,WAAW,oBAAoB,IAAI,GAAG,uDAAK,CAAC,+CAAM,IAAI,4IAA4I,sDAAI,CAAC,oDAAQ,IAAI,sBAAsB,GAAG,sDAAI,WAAW,oBAAoB,IAAI,GAAG,uDAAK,CAAC,+CAAM,IAAI,2KAA2K,sDAAI,CAAC,qDAAM,IAAI,sBAAsB,GAAG,sDAAI,WAAW,uBAAuB,IAAI,IAAI,IAAI,GAAG,uDAAK,UAAU,mCAAmC,uDAAK,UAAU,yDAAyD,uDAAK,UAAU,yCAAyC,sDAAI,CAAC,qDAAM,IAAI,uFAAuF,GAAG,sDAAI,CAAC,6CAAK,IAAI,iJAAiJ,IAAI,GAAG,uDAAK,UAAU,wCAAwC,uDAAK,aAAa,iMAAiM,sDAAI,aAAa,sCAAsC,GAAG,sDAAI,aAAa,4CAA4C,GAAG,sDAAI,aAAa,yCAAyC,GAAG,sDAAI,aAAa,gDAAgD,IAAI,GAAG,uDAAK,UAAU,2CAA2C,uDAAK,aAAa,kMAAkM,sDAAI,aAAa,yCAAyC,GAAG,sDAAI,aAAa,6DAA6D,GAAG,sDAAI,aAAa,+DAA+D,GAAG,sDAAI,aAAa,yCAAyC,IAAI,GAAG,sDAAI,CAAC,+CAAM,IAAI,wIAAwI,sDAAI,CAAC,oDAAQ,IAAI,sBAAsB,IAAI,sDAAI,CAAC,oDAAO,IAAI,sBAAsB,GAAG,IAAI,IAAI,IAAI,gCAAgC,uDAAK,UAAU,8CAA8C,uDAAK,WAAW,oFAAoF,sDAAI,CAAC,qDAAG,IAAI,2BAA2B,uBAAuB,6BAA6B,uDAAK,CAAC,6CAAK,IAAI;AAClnH;AACA;AACA;AACA,yCAAyC,iDAAiD,sDAAI,CAAC,qDAAC,IAAI,2BAA2B,KAAK,sCAAsC,sDAAI,CAAC,+CAAM,IAAI,uHAAuH,KAAK,KAAK,IAAI,GAAG,sDAAI,UAAU,kEAAkE,sDAAI,CAAC,2CAAI,IAAI,6BAA6B,uDAAK,UAAU,sEAAsE,sDAAI,CAAC,qDAAO,IAAI,gDAAgD,GAAG,sDAAI,SAAS;AAChmB;AACA,6DAA6D,GAAG,sDAAI,QAAQ;AAC5E;AACA,qFAAqF,IAAI,GAAG;AAC5F;AACA;AACA;AACA,4BAA4B,uDAAK,CAAC,2CAAI,IAAI,+DAA+D,uDAAK,UAAU,0DAA0D,uDAAK,UAAU,gCAAgC,sDAAI,UAAU,yDAAyD,uDAAK,UAAU,qEAAqE,sDAAI,CAAC,oDAAW,IAAI,qCAAqC,MAAM,sDAAI,CAAC,qDAAO,IAAI,mCAAmC,IAAI,sDAAI,CAAC,6CAAK,IAAI,gEAAgE,GAAG,sDAAI,CAAC,6CAAK,IAAI,8HAA8H,+CAA+C,uDAAK,CAAC,6CAAK,IAAI,iFAAiF,sDAAI,CAAC,qDAAU,IAAI,sBAAsB,GAAG,uDAAK,WAAW,6CAA6C,IAAI,mBAAmB,uDAAK,CAAC,6CAAK,IAAI,2HAA2H,sDAAI,CAAC,qDAAI,IAAI,2BAA2B,gBAAgB,KAAK,GAAG,kBAAkB,uDAAK,UAAU,8BAA8B,sDAAI,SAAS,2FAA2F,8BAA8B,sDAAI,QAAQ,yFAAyF,KAAK,IAAI,sDAAI,UAAU,6BAA6B,sDAAI,QAAQ,qGAAqG,GAAG,gDAAgD,uDAAK,UAAU,+EAA+E,uDAAK,CAAC,6CAAK,IAAI,uDAAuD,sDAAI,CAAC,oDAAI,IAAI,2BAA2B,SAAS,sCAAsC,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,6CAAK,IAAI;AACv/D;AACA;AACA;AACA,mEAAmE,GAAG,sDAAI,CAAC,+CAAM,IAAI,yFAAyF,sDAAI,CAAC,qDAAI,IAAI,sBAAsB,GAAG,IAAI,MAAM,uDAAK,CAAC,+CAAM,IAAI,iHAAiH,sDAAI,CAAC,qDAAI,IAAI,2BAA2B,eAAe,KAAK,IAAI,uDAAK,UAAU,8FAA8F,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,oDAAQ,IAAI,sBAAsB,GAAG,sDAAI,WAAW,sDAAsD,IAAI,GAAG,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,oDAAK,IAAI,sBAAsB,GAAG,sDAAI,WAAW,mDAAmD,IAAI,sDAAsD,sDAAI,UAAU,oDAAoD,uDAAK,WAAW,2DAA2D,GAAG,sDAAsD,sDAAI,UAAU,oDAAoD,uDAAK,WAAW,uEAAuE,GAAG,KAAK,mCAAmC,sDAAI,UAAU,yEAAyE,sDAAI,QAAQ,2EAA2E,GAAG,KAAK,GAAG,uDAAK,UAAU,gEAAgE,uDAAK,UAAU,qDAAqD,uDAAK,CAAC,+CAAM,IAAI,8HAA8H,sDAAI,CAAC,qDAAS,IAAI,sBAAsB,GAAG,sDAAI,WAAW,mBAAmB,IAAI,GAAG,sDAAI,CAAC,+CAAM,IAAI,uFAAuF,6FAA6F,0BAA0B,sDAAI,CAAC,qDAAI,IAAI,mCAAmC,IAAI,sDAAI,CAAC,qDAAO,IAAI,sBAAsB,GAAG,GAAG,sDAAI,CAAC,+CAAM,IAAI,oIAAoI,sDAAI,CAAC,qDAAM,IAAI,sBAAsB,GAAG,GAAG,sDAAI,CAAC,+CAAM,IAAI,yKAAyK,sDAAI,CAAC,qDAAM,IAAI,sBAAsB,GAAG,IAAI,mBAAmB,sDAAI,CAAC,+CAAM,IAAI,yHAAyH,KAAK,IAAI,mCAAmC,uDAAK,UAAU,4EAA4E,sDAAI,SAAS,gEAAgE,GAAG,uDAAK,UAAU,wCAAwC,uDAAK,CAAC,+CAAM,IAAI,+GAA+G,sDAAI,CAAC,qDAAI,IAAI,sBAAsB,GAAG,sDAAI,WAAW,2BAA2B,IAAI,GAAG,uDAAK,CAAC,+CAAM,IAAI;AACntG;AACA;AACA,iDAAiD,uDAAuD,sDAAI,CAAC,oDAAI,IAAI,sBAAsB,GAAG,sDAAI,WAAW,wBAAwB,IAAI,GAAG,sDAAI,CAAC,+CAAM,IAAI,2FAA2F,IAAI,IAAI,mCAAmC,uDAAK,UAAU,8EAA8E,sDAAI,SAAS,+DAA+D,GAAG,uDAAK,UAAU,mCAAmC,sDAAI,CAAC,6CAAK,IAAI,2HAA2H,GAAG,sDAAI,CAAC,6CAAK,IAAI,qJAAqJ,GAAG,uDAAK,UAAU,wCAAwC,sDAAI,CAAC,+CAAM,IAAI,qGAAqG,GAAG,sDAAI,CAAC,+CAAM,IAAI;AAC9hC;AACA;AACA;AACA,yDAAyD,sBAAsB,IAAI,IAAI,IAAI,KAAK;AAChG,iBAAiB,IAAI,IAAI;AACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9R+D;AACvB;AACiC;AACtB;AACb;AACJ;AACE;AACA;AACA;AACrB;AACf,YAAY,8GAA8G,EAAE,4DAAa;AACzI,0CAA0C,+CAAQ;AAClD,wCAAwC,+CAAQ;AAChD,oCAAoC,+CAAQ;AAC5C;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,qCAAqC;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,cAAc,qBAAqB,KAAK,GAAG;AACjF,YAAY,uDAAK,UAAU,mCAAmC,uDAAK,CAAC,2CAAI,IAAI,6BAA6B,uDAAK,UAAU,gEAAgE,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,oDAAI,IAAI,oCAAoC,GAAG,sDAAI,SAAS,6FAA6F,IAAI,GAAG,uDAAK,CAAC,8CAAM,IAAI,mHAAmH,sDAAI,CAAC,oDAAI,IAAI,sBAAsB,GAAG,sDAAI,WAAW,gCAAgC,IAAI,IAAI,GAAG,uDAAK,UAAU,kCAAkC,sDAAI,CAAC,oDAAM,IAAI,uFAAuF,GAAG,sDAAI,CAAC,6CAAK,IAAI,6HAA6H,IAAI,IAAI,GAAG,sDAAI,UAAU,kEAAkE,sDAAI,CAAC,2CAAI,IAAI,6BAA6B,uDAAK,UAAU,sEAAsE,sDAAI,CAAC,oDAAI,IAAI,gDAAgD,GAAG,sDAAI,SAAS;AAChtC;AACA,6DAA6D,GAAG,sDAAI,QAAQ;AAC5E;AACA,0EAA0E,IAAI,GAAG,oCAAoC,sDAAI,CAAC,2CAAI,IAAI,4BAA4B,uDAAK,UAAU,0DAA0D,uDAAK,UAAU,gCAAgC,uDAAK,UAAU,0DAA0D,sDAAI,SAAS,sFAAsF,GAAG,sDAAI,CAAC,6CAAK,IAAI,iEAAiE,IAAI,yBAAyB,sDAAI,QAAQ,iFAAiF,IAAI,uDAAK,UAAU,8FAA8F,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,oDAAQ,IAAI,sBAAsB,GAAG,sDAAI,WAAW,0DAA0D,IAAI,6BAA6B,uDAAK,UAAU,qDAAqD,sDAAI,CAAC,oDAAG,IAAI,sBAAsB,GAAG,sDAAI,UAAU,8DAA8D,sDAAI,CAAC,6CAAK,IAAI,2DAA2D,UAAU,IAAI,KAAK,IAAI,GAAG,uDAAK,UAAU,0DAA0D,uDAAK,CAAC,8CAAM,IAAI,+HAA+H,sDAAI,CAAC,oDAAI,IAAI,sBAAsB,GAAG,sDAAI,WAAW,kBAAkB,IAAI,GAAG,sDAAI,CAAC,8CAAM,IAAI,2KAA2K,sDAAI,CAAC,oDAAM,IAAI,sBAAsB,GAAG,IAAI,IAAI,GAAG,gBAAgB,GAAG,sDAAI,CAAC,6CAAK,IAAI,0FAA0F,uDAAK,UAAU,mCAAmC,uDAAK,UAAU,WAAW,sDAAI,YAAY,wGAAwG,GAAG,sDAAI,CAAC,6CAAK,IAAI,+FAA+F,mCAAmC,GAAG,IAAI,GAAG,uDAAK,UAAU,WAAW,sDAAI,YAAY,uGAAuG,GAAG,sDAAI,CAAC,6CAAK,IAAI,+FAA+F,0CAA0C,GAAG,IAAI,GAAG,uDAAK,UAAU,WAAW,sDAAI,YAAY,gGAAgG,GAAG,sDAAI,CAAC,6CAAK,IAAI,kGAAkG,mCAAmC,GAAG,GAAG,sDAAI,QAAQ,kGAAkG,IAAI,GAAG,uDAAK,UAAU,yDAAyD,sDAAI,CAAC,8CAAM,IAAI,8EAA8E,GAAG,sDAAI,CAAC,8CAAM,IAAI,mFAAmF,IAAI,IAAI,GAAG,IAAI;AACvsG;;;;;;;;;;;;;;;;;;;;;;;;;;ACjDyD;AACY;AACR;AACA;AACJ;AACA", "sources": ["webpack://es-client/./node_modules/lucide-react/dist/esm/icons/arrow-down-wide-narrow.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/link.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/share-2.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/star-off.mjs", "webpack://es-client/./node_modules/lucide-react/dist/esm/icons/tag.mjs", "webpack://es-client/./src/renderer/components/Query/QueryHistory.tsx", "webpack://es-client/./src/renderer/components/Query/SavedQueries.tsx", "webpack://es-client/./src/renderer/components/Query/index.ts"], "sourcesContent": ["/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst ArrowDownWideNarrow = createLucideIcon(\"ArrowDownWideNarrow\", [\n  [\"path\", { d: \"m3 16 4 4 4-4\", key: \"1co6wj\" }],\n  [\"path\", { d: \"M7 20V4\", key: \"1yoxec\" }],\n  [\"path\", { d: \"M11 4h10\", key: \"1w87gc\" }],\n  [\"path\", { d: \"M11 8h7\", key: \"djye34\" }],\n  [\"path\", { d: \"M11 12h4\", key: \"q8tih4\" }]\n]);\n\nexport { ArrowDownWideNarrow as default };\n//# sourceMappingURL=arrow-down-wide-narrow.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst ArrowUpNarrowWide = createLucideIcon(\"ArrowUpNarrowWide\", [\n  [\"path\", { d: \"m3 8 4-4 4 4\", key: \"11wl7u\" }],\n  [\"path\", { d: \"M7 4v16\", key: \"1glfcx\" }],\n  [\"path\", { d: \"M11 12h4\", key: \"q8tih4\" }],\n  [\"path\", { d: \"M11 16h7\", key: \"uosisv\" }],\n  [\"path\", { d: \"M11 20h10\", key: \"jvxblo\" }]\n]);\n\nexport { ArrowUpNarrowWide as default };\n//# sourceMappingURL=arrow-up-narrow-wide.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst Link = createLucideIcon(\"Link\", [\n  [\n    \"path\",\n    {\n      d: \"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\",\n      key: \"1cjeqo\"\n    }\n  ],\n  [\n    \"path\",\n    {\n      d: \"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\",\n      key: \"19qd67\"\n    }\n  ]\n]);\n\nexport { Link as default };\n//# sourceMappingURL=link.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst Share2 = createLucideIcon(\"Share2\", [\n  [\"circle\", { cx: \"18\", cy: \"5\", r: \"3\", key: \"gq8acd\" }],\n  [\"circle\", { cx: \"6\", cy: \"12\", r: \"3\", key: \"w7nqdw\" }],\n  [\"circle\", { cx: \"18\", cy: \"19\", r: \"3\", key: \"1xt0gg\" }],\n  [\n    \"line\",\n    { x1: \"8.59\", x2: \"15.42\", y1: \"13.51\", y2: \"17.49\", key: \"47mynk\" }\n  ],\n  [\"line\", { x1: \"15.41\", x2: \"8.59\", y1: \"6.51\", y2: \"10.49\", key: \"1n3mei\" }]\n]);\n\nexport { Share2 as default };\n//# sourceMappingURL=share-2.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst StarOff = createLucideIcon(\"StarOff\", [\n  [\n    \"path\",\n    {\n      d: \"M8.34 8.34 2 9.27l5 4.87L5.82 21 12 17.77 18.18 21l-.59-3.43\",\n      key: \"16m0ql\"\n    }\n  ],\n  [\n    \"path\",\n    { d: \"M18.42 12.76 22 9.27l-6.91-1L12 2l-1.44 2.91\", key: \"1vt8nq\" }\n  ],\n  [\"line\", { x1: \"2\", x2: \"22\", y1: \"2\", y2: \"22\", key: \"a6p6uj\" }]\n]);\n\nexport { StarOff as default };\n//# sourceMappingURL=star-off.mjs.map\n", "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\n\nconst Tag = createLucideIcon(\"Tag\", [\n  [\n    \"path\",\n    {\n      d: \"M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z\",\n      key: \"14b2ls\"\n    }\n  ],\n  [\"path\", { d: \"M7 7h.01\", key: \"7u93v4\" }]\n]);\n\nexport { Tag as default };\n//# sourceMappingURL=tag.mjs.map\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { useState, useMemo } from 'react';\nimport { History, Trash2, Search, Clock, CheckCircle, XCircle, RotateCcw, Calendar, Star, StarOff, Tag, Share2, Download, Upload, Copy, Link, TrendingUp, SortAsc, SortDesc, Hash, Plus, X } from 'lucide-react';\nimport { useQueryStore } from '../../stores/query';\nimport { Button } from '../UI/Button';\nimport { Card } from '../UI/Card';\nimport { Input } from '../UI/Input';\nimport { Badge } from '../UI/Badge';\nexport default function QueryHistory() {\n    const { queryHistory, savedQueries, loadHistoryQuery, clearQueryHistory, deleteHistoryItem, saveQuery, toggleQueryFavorite, updateSavedQuery, exportQueries, importQueries, } = useQueryStore();\n    const [searchTerm, setSearchTerm] = useState('');\n    const [filterStatus, setFilterStatus] = useState('all');\n    const [sortBy, setSortBy] = useState('date');\n    const [sortOrder, setSortOrder] = useState('desc');\n    const [selectedTags, setSelectedTags] = useState([]);\n    const [showTagInput, setShowTagInput] = useState(null);\n    const [newTag, setNewTag] = useState('');\n    const [showShareDialog, setShowShareDialog] = useState(null);\n    const [showSaveDialog, setShowSaveDialog] = useState(null);\n    const [saveQueryName, setSaveQueryName] = useState('');\n    const [saveQueryDescription, setSaveQueryDescription] = useState('');\n    const [saveQueryTags, setSaveQueryTags] = useState([]);\n    // Calculate usage statistics for intelligent sorting\n    const usageStats = useMemo(() => {\n        const stats = new Map();\n        queryHistory.forEach(item => {\n            const queryKey = JSON.stringify(item.query);\n            const existing = stats.get(queryKey);\n            if (existing) {\n                existing.usageCount++;\n                existing.lastUsed = new Date(Math.max(existing.lastUsed.getTime(), item.executedAt.getTime()));\n                if (item.executionTime) {\n                    existing.avgExecutionTime = (existing.avgExecutionTime + item.executionTime) / 2;\n                }\n            }\n            else {\n                stats.set(queryKey, {\n                    queryId: queryKey,\n                    usageCount: 1,\n                    lastUsed: item.executedAt,\n                    avgExecutionTime: item.executionTime || 0,\n                });\n            }\n        });\n        return stats;\n    }, [queryHistory]);\n    // Get all available tags from saved queries\n    const availableTags = useMemo(() => {\n        const tags = new Set();\n        savedQueries.forEach(query => {\n            query.tags.forEach(tag => tags.add(tag));\n        });\n        return Array.from(tags).sort();\n    }, [savedQueries]);\n    // Enhanced filtering and sorting\n    const filteredHistory = useMemo(() => {\n        let filtered = queryHistory.filter(item => {\n            // Search filter\n            const matchesSearch = item.index.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                (item.error && item.error.toLowerCase().includes(searchTerm.toLowerCase())) ||\n                JSON.stringify(item.query).toLowerCase().includes(searchTerm.toLowerCase());\n            // Status filter\n            const matchesStatus = filterStatus === 'all' ||\n                (filterStatus === 'success' && item.success) ||\n                (filterStatus === 'error' && !item.success) ||\n                (filterStatus === 'favorites' && savedQueries.some(sq => sq.isFavorite && JSON.stringify(sq.query) === JSON.stringify(item.query)));\n            // Tag filter\n            const matchesTags = selectedTags.length === 0 ||\n                savedQueries.some(sq => JSON.stringify(sq.query) === JSON.stringify(item.query) &&\n                    selectedTags.every(tag => sq.tags.includes(tag)));\n            return matchesSearch && matchesStatus && matchesTags;\n        });\n        // Sort the filtered results\n        return filtered.sort((a, b) => {\n            let comparison = 0;\n            switch (sortBy) {\n                case 'date':\n                    comparison = new Date(b.executedAt).getTime() - new Date(a.executedAt).getTime();\n                    break;\n                case 'execution_time':\n                    comparison = (b.executionTime || 0) - (a.executionTime || 0);\n                    break;\n                case 'usage_frequency':\n                    const aStats = usageStats.get(JSON.stringify(a.query));\n                    const bStats = usageStats.get(JSON.stringify(b.query));\n                    comparison = (bStats?.usageCount || 0) - (aStats?.usageCount || 0);\n                    break;\n                case 'name':\n                    const aName = savedQueries.find(sq => JSON.stringify(sq.query) === JSON.stringify(a.query))?.name || 'Unnamed';\n                    const bName = savedQueries.find(sq => JSON.stringify(sq.query) === JSON.stringify(b.query))?.name || 'Unnamed';\n                    comparison = aName.localeCompare(bName);\n                    break;\n            }\n            return sortOrder === 'desc' ? comparison : -comparison;\n        });\n    }, [queryHistory, searchTerm, filterStatus, selectedTags, sortBy, sortOrder, usageStats, savedQueries]);\n    const handleLoadQuery = (historyId) => {\n        loadHistoryQuery(historyId);\n    };\n    const handleDeleteItem = (historyId) => {\n        if (confirm('Are you sure you want to delete this history item?')) {\n            deleteHistoryItem(historyId);\n        }\n    };\n    const handleClearHistory = () => {\n        if (confirm('Are you sure you want to clear all query history?')) {\n            clearQueryHistory();\n        }\n    };\n    const handleSaveQuery = (historyItem) => {\n        if (!saveQueryName.trim())\n            return;\n        // Temporarily set the selected index and query to match the history item\n        const currentState = {\n            selectedIndex: useQueryStore.getState().selectedIndex,\n            queryMode: useQueryStore.getState().queryMode,\n            visualQuery: useQueryStore.getState().visualQuery,\n            dslQuery: useQueryStore.getState().dslQuery,\n        };\n        // Set the state to match the history item\n        useQueryStore.setState({\n            selectedIndex: historyItem.index,\n            queryMode: historyItem.queryMode,\n            visualQuery: historyItem.queryMode === 'visual' ? historyItem.query : useQueryStore.getState().visualQuery,\n            dslQuery: historyItem.queryMode === 'dsl' ? JSON.stringify(historyItem.query, null, 2) : useQueryStore.getState().dslQuery,\n        });\n        // Save the query\n        const queryId = saveQuery(saveQueryName, saveQueryDescription, saveQueryTags);\n        // Restore the previous state\n        useQueryStore.setState(currentState);\n        setSaveQueryName('');\n        setSaveQueryDescription('');\n        setSaveQueryTags([]);\n        setShowSaveDialog(null);\n    };\n    const handleToggleFavorite = (historyItem) => {\n        const existingSaved = savedQueries.find(sq => JSON.stringify(sq.query) === JSON.stringify(historyItem.query) &&\n            sq.index === historyItem.index);\n        if (existingSaved) {\n            toggleQueryFavorite(existingSaved.id);\n        }\n        else {\n            // Save as favorite if not already saved\n            const queryName = `Query on ${historyItem.index} - ${new Date(historyItem.executedAt).toLocaleDateString()}`;\n            // Temporarily set the state to match the history item\n            const currentState = {\n                selectedIndex: useQueryStore.getState().selectedIndex,\n                queryMode: useQueryStore.getState().queryMode,\n                visualQuery: useQueryStore.getState().visualQuery,\n                dslQuery: useQueryStore.getState().dslQuery,\n            };\n            useQueryStore.setState({\n                selectedIndex: historyItem.index,\n                queryMode: historyItem.queryMode,\n                visualQuery: historyItem.queryMode === 'visual' ? historyItem.query : useQueryStore.getState().visualQuery,\n                dslQuery: historyItem.queryMode === 'dsl' ? JSON.stringify(historyItem.query, null, 2) : useQueryStore.getState().dslQuery,\n            });\n            const queryId = saveQuery(queryName, '', [], undefined);\n            // Restore the previous state\n            useQueryStore.setState(currentState);\n            // Mark as favorite\n            if (queryId) {\n                toggleQueryFavorite(queryId);\n            }\n        }\n    };\n    const handleAddTag = (queryId) => {\n        if (!newTag.trim())\n            return;\n        const savedQuery = savedQueries.find(sq => sq.id === queryId);\n        if (savedQuery && !savedQuery.tags.includes(newTag)) {\n            updateSavedQuery(queryId, {\n                tags: [...savedQuery.tags, newTag]\n            });\n        }\n        setNewTag('');\n        setShowTagInput(null);\n    };\n    const handleRemoveTag = (queryId, tagToRemove) => {\n        const savedQuery = savedQueries.find(sq => sq.id === queryId);\n        if (savedQuery) {\n            updateSavedQuery(queryId, {\n                tags: savedQuery.tags.filter(tag => tag !== tagToRemove)\n            });\n        }\n    };\n    const handleGenerateShareLink = (historyItem) => {\n        const shareData = {\n            index: historyItem.index,\n            query: historyItem.query,\n            queryMode: historyItem.queryMode,\n            timestamp: historyItem.executedAt.toISOString(),\n        };\n        const shareUrl = `${window.location.origin}/shared-query?data=${encodeURIComponent(btoa(JSON.stringify(shareData)))}`;\n        navigator.clipboard.writeText(shareUrl);\n        // Show success feedback\n        alert('Share link copied to clipboard!');\n        setShowShareDialog(null);\n    };\n    const handleExportQueries = () => {\n        const exportData = exportQueries();\n        const blob = new Blob([exportData], { type: 'application/json' });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `elasticsearch-queries-${new Date().toISOString().split('T')[0]}.json`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const handleImportQueries = async (event) => {\n        const file = event.target.files?.[0];\n        if (!file)\n            return;\n        try {\n            const content = await file.text();\n            const result = await importQueries(content);\n            if (result.errors.length > 0) {\n                alert(`Import completed with ${result.success} successful imports and ${result.errors.length} errors:\\n${result.errors.join('\\n')}`);\n            }\n            else {\n                alert(`Successfully imported ${result.success} queries!`);\n            }\n        }\n        catch (error) {\n            alert(`Failed to import queries: ${error}`);\n        }\n        // Reset file input\n        event.target.value = '';\n    };\n    const toggleSortOrder = () => {\n        setSortOrder(current => current === 'desc' ? 'asc' : 'desc');\n    };\n    const getSavedQueryForHistory = (historyItem) => {\n        return savedQueries.find(sq => JSON.stringify(sq.query) === JSON.stringify(historyItem.query) &&\n            sq.index === historyItem.index);\n    };\n    const getUsageStatsForHistory = (historyItem) => {\n        return usageStats.get(JSON.stringify(historyItem.query));\n    };\n    const formatExecutionTime = (time) => {\n        if (!time)\n            return 'N/A';\n        return time < 1000 ? `${time}ms` : `${(time / 1000).toFixed(2)}s`;\n    };\n    const formatQueryPreview = (item) => {\n        if (item.queryMode === 'visual') {\n            const visualQuery = item.query;\n            if (visualQuery.conditions && visualQuery.conditions.length > 0) {\n                const condition = visualQuery.conditions[0];\n                return `${condition.field} ${condition.operator} ${condition.value}`;\n            }\n            return 'match_all';\n        }\n        else {\n            // DSL query preview\n            const queryStr = JSON.stringify(item.query);\n            return queryStr.length > 50 ? queryStr.substring(0, 50) + '...' : queryStr;\n        }\n    };\n    return (_jsxs(\"div\", { className: \"space-y-6\", children: [_jsxs(Card, { className: \"p-6\", children: [_jsxs(\"div\", { className: \"flex items-center justify-between mb-4\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-3\", children: [_jsx(History, { className: \"w-6 h-6 text-blue-500\" }), _jsx(\"h2\", { className: \"text-xl font-semibold text-gray-900 dark:text-white\", children: \"Query History & Favorites\" }), _jsxs(Badge, { variant: \"secondary\", className: \"text-xs\", children: [queryHistory.length, \" items\"] }), _jsxs(Badge, { variant: \"outline\", className: \"text-xs\", children: [savedQueries.filter(sq => sq.isFavorite).length, \" favorites\"] })] }), _jsxs(\"div\", { className: \"flex items-center space-x-2\", children: [_jsx(\"input\", { type: \"file\", accept: \".json\", onChange: handleImportQueries, className: \"hidden\", id: \"import-queries\" }), _jsxs(Button, { variant: \"outline\", onClick: () => document.getElementById('import-queries')?.click(), className: \"flex items-center space-x-2\", children: [_jsx(Upload, { className: \"w-4 h-4\" }), _jsx(\"span\", { children: \"Import\" })] }), _jsxs(Button, { variant: \"outline\", onClick: handleExportQueries, disabled: savedQueries.length === 0, className: \"flex items-center space-x-2\", children: [_jsx(Download, { className: \"w-4 h-4\" }), _jsx(\"span\", { children: \"Export\" })] }), _jsxs(Button, { variant: \"outline\", onClick: handleClearHistory, disabled: queryHistory.length === 0, className: \"flex items-center space-x-2 text-red-600 hover:text-red-700\", children: [_jsx(Trash2, { className: \"w-4 h-4\" }), _jsx(\"span\", { children: \"Clear All\" })] })] })] }), _jsxs(\"div\", { className: \"space-y-4\", children: [_jsxs(\"div\", { className: \"flex flex-col sm:flex-row gap-4\", children: [_jsxs(\"div\", { className: \"relative flex-1\", children: [_jsx(Search, { className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" }), _jsx(Input, { placeholder: \"Search by index, query content, or error...\", value: searchTerm, onChange: e => setSearchTerm(e.target.value), className: \"pl-10\" })] }), _jsxs(\"div\", { className: \"flex space-x-2\", children: [_jsxs(\"select\", { value: filterStatus, onChange: e => setFilterStatus(e.target.value), className: \"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm\", children: [_jsx(\"option\", { value: \"all\", children: \"All Status\" }), _jsx(\"option\", { value: \"success\", children: \"Success Only\" }), _jsx(\"option\", { value: \"error\", children: \"Errors Only\" }), _jsx(\"option\", { value: \"favorites\", children: \"Favorites Only\" })] }), _jsxs(\"div\", { className: \"flex items-center\", children: [_jsxs(\"select\", { value: sortBy, onChange: e => setSortBy(e.target.value), className: \"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md bg-white dark:bg-gray-800 text-sm border-r-0\", children: [_jsx(\"option\", { value: \"date\", children: \"Sort by Date\" }), _jsx(\"option\", { value: \"execution_time\", children: \"Sort by Execution Time\" }), _jsx(\"option\", { value: \"usage_frequency\", children: \"Sort by Usage Frequency\" }), _jsx(\"option\", { value: \"name\", children: \"Sort by Name\" })] }), _jsx(Button, { variant: \"outline\", size: \"sm\", onClick: toggleSortOrder, className: \"rounded-l-none border-l-0 px-2\", children: sortOrder === 'desc' ? _jsx(SortDesc, { className: \"w-4 h-4\" }) : _jsx(SortAsc, { className: \"w-4 h-4\" }) })] })] })] }), availableTags.length > 0 && (_jsxs(\"div\", { className: \"flex flex-wrap gap-2\", children: [_jsxs(\"span\", { className: \"text-sm text-gray-500 dark:text-gray-400 flex items-center\", children: [_jsx(Tag, { className: \"w-4 h-4 mr-1\" }), \"Filter by tags:\"] }), availableTags.map(tag => (_jsxs(Badge, { variant: selectedTags.includes(tag) ? \"default\" : \"outline\", className: \"cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900\", onClick: () => {\n                                            setSelectedTags(current => current.includes(tag)\n                                                ? current.filter(t => t !== tag)\n                                                : [...current, tag]);\n                                        }, children: [tag, selectedTags.includes(tag) && (_jsx(X, { className: \"w-3 h-3 ml-1\" }))] }, tag))), selectedTags.length > 0 && (_jsx(Button, { variant: \"ghost\", size: \"sm\", onClick: () => setSelectedTags([]), className: \"text-xs h-6\", children: \"Clear filters\" }))] }))] })] }), _jsx(\"div\", { className: \"space-y-4\", children: filteredHistory.length === 0 ? (_jsx(Card, { className: \"p-12\", children: _jsxs(\"div\", { className: \"text-center text-gray-500 dark:text-gray-400\", children: [_jsx(History, { className: \"w-12 h-12 mx-auto mb-4 opacity-50\" }), _jsx(\"h3\", { className: \"text-lg font-medium mb-2\", children: queryHistory.length === 0\n                                    ? 'No Query History'\n                                    : 'No Matching History' }), _jsx(\"p\", { children: queryHistory.length === 0\n                                    ? 'Execute some queries to see them appear here.'\n                                    : 'Try adjusting your search terms or filters.' })] }) })) : (filteredHistory.map(item => {\n                    const savedQuery = getSavedQueryForHistory(item);\n                    const usageStats = getUsageStatsForHistory(item);\n                    const isFavorite = savedQuery?.isFavorite || false;\n                    return (_jsxs(Card, { className: \"p-6 hover:shadow-md transition-shadow\", children: [_jsxs(\"div\", { className: \"flex items-start justify-between\", children: [_jsxs(\"div\", { className: \"flex-1\", children: [_jsx(\"div\", { className: \"flex items-center space-x-3 mb-2\", children: _jsxs(\"div\", { className: \"flex items-center space-x-2\", children: [item.success ? (_jsx(CheckCircle, { className: \"w-5 h-5 text-green-500\" })) : (_jsx(XCircle, { className: \"w-5 h-5 text-red-500\" })), _jsx(Badge, { variant: \"outline\", className: \"text-xs\", children: item.index }), _jsx(Badge, { variant: item.queryMode === 'visual' ? 'default' : 'secondary', className: \"text-xs\", children: item.queryMode.toUpperCase() }), usageStats && usageStats.usageCount > 1 && (_jsxs(Badge, { variant: \"outline\", className: \"text-xs flex items-center space-x-1\", children: [_jsx(TrendingUp, { className: \"w-3 h-3\" }), _jsxs(\"span\", { children: [usageStats.usageCount, \"x used\"] })] })), isFavorite && (_jsxs(Badge, { variant: \"default\", className: \"text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\", children: [_jsx(Star, { className: \"w-3 h-3 mr-1\" }), \"Favorite\"] }))] }) }), savedQuery && (_jsxs(\"div\", { className: \"mb-2\", children: [_jsx(\"h4\", { className: \"text-sm font-medium text-gray-900 dark:text-white\", children: savedQuery.name }), savedQuery.description && (_jsx(\"p\", { className: \"text-xs text-gray-500 dark:text-gray-400\", children: savedQuery.description }))] })), _jsx(\"div\", { className: \"mb-3\", children: _jsx(\"p\", { className: \"text-sm text-gray-600 dark:text-gray-400 font-mono\", children: formatQueryPreview(item) }) }), savedQuery && savedQuery.tags.length > 0 && (_jsxs(\"div\", { className: \"flex flex-wrap gap-1 mb-3\", children: [savedQuery.tags.map(tag => (_jsxs(Badge, { variant: \"secondary\", className: \"text-xs\", children: [_jsx(Hash, { className: \"w-3 h-3 mr-1\" }), tag] }, tag))), showTagInput === item.id ? (_jsxs(\"div\", { className: \"flex items-center space-x-1\", children: [_jsx(Input, { value: newTag, onChange: e => setNewTag(e.target.value), placeholder: \"Add tag...\", className: \"h-6 text-xs w-20\", onKeyPress: e => {\n                                                                    if (e.key === 'Enter') {\n                                                                        handleAddTag(savedQuery.id);\n                                                                    }\n                                                                } }), _jsx(Button, { size: \"sm\", onClick: () => handleAddTag(savedQuery.id), className: \"h-6 px-2\", children: _jsx(Plus, { className: \"w-3 h-3\" }) })] })) : (_jsxs(Button, { variant: \"ghost\", size: \"sm\", onClick: () => setShowTagInput(item.id), className: \"h-6 px-2 text-xs\", children: [_jsx(Plus, { className: \"w-3 h-3 mr-1\" }), \"Add tag\"] }))] })), _jsxs(\"div\", { className: \"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-1\", children: [_jsx(Calendar, { className: \"w-4 h-4\" }), _jsx(\"span\", { children: new Date(item.executedAt).toLocaleString() })] }), _jsxs(\"div\", { className: \"flex items-center space-x-1\", children: [_jsx(Clock, { className: \"w-4 h-4\" }), _jsx(\"span\", { children: formatExecutionTime(item.executionTime) })] }), item.success && item.resultCount !== undefined && (_jsx(\"div\", { className: \"flex items-center space-x-1\", children: _jsxs(\"span\", { children: [item.resultCount.toLocaleString(), \" results\"] }) })), usageStats && usageStats.avgExecutionTime > 0 && (_jsx(\"div\", { className: \"flex items-center space-x-1\", children: _jsxs(\"span\", { children: [\"Avg: \", formatExecutionTime(usageStats.avgExecutionTime)] }) }))] }), !item.success && item.error && (_jsx(\"div\", { className: \"mt-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-md\", children: _jsx(\"p\", { className: \"text-sm text-red-600 dark:text-red-400\", children: item.error }) }))] }), _jsxs(\"div\", { className: \"flex flex-col items-end space-y-2 ml-4\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-2\", children: [_jsxs(Button, { variant: \"outline\", size: \"sm\", onClick: () => handleLoadQuery(item.id), className: \"flex items-center space-x-1\", children: [_jsx(RotateCcw, { className: \"w-4 h-4\" }), _jsx(\"span\", { children: \"Rerun\" })] }), _jsx(Button, { variant: \"ghost\", size: \"sm\", onClick: () => handleToggleFavorite(item), className: `${isFavorite ? 'text-yellow-500 hover:text-yellow-600' : 'text-gray-400 hover:text-yellow-500'}`, children: isFavorite ? _jsx(Star, { className: \"w-4 h-4 fill-current\" }) : _jsx(StarOff, { className: \"w-4 h-4\" }) }), _jsx(Button, { variant: \"ghost\", size: \"sm\", onClick: () => setShowShareDialog(item.id), className: \"text-blue-500 hover:text-blue-600\", children: _jsx(Share2, { className: \"w-4 h-4\" }) }), _jsx(Button, { variant: \"ghost\", size: \"sm\", onClick: () => handleDeleteItem(item.id), className: \"text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20\", children: _jsx(Trash2, { className: \"w-4 h-4\" }) })] }), !savedQuery && (_jsx(Button, { variant: \"outline\", size: \"sm\", onClick: () => setShowSaveDialog(item.id), className: \"text-xs\", children: \"Save Query\" }))] })] }), showShareDialog === item.id && (_jsxs(\"div\", { className: \"mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md\", children: [_jsx(\"h4\", { className: \"text-sm font-medium mb-2\", children: \"Share Query\" }), _jsxs(\"div\", { className: \"flex space-x-2\", children: [_jsxs(Button, { size: \"sm\", onClick: () => handleGenerateShareLink(item), className: \"flex items-center space-x-1\", children: [_jsx(Link, { className: \"w-4 h-4\" }), _jsx(\"span\", { children: \"Generate Link\" })] }), _jsxs(Button, { size: \"sm\", variant: \"outline\", onClick: () => {\n                                                    navigator.clipboard.writeText(JSON.stringify(item.query, null, 2));\n                                                    alert('Query copied to clipboard!');\n                                                }, className: \"flex items-center space-x-1\", children: [_jsx(Copy, { className: \"w-4 h-4\" }), _jsx(\"span\", { children: \"Copy Query\" })] }), _jsx(Button, { size: \"sm\", variant: \"ghost\", onClick: () => setShowShareDialog(null), children: \"Cancel\" })] })] })), showSaveDialog === item.id && (_jsxs(\"div\", { className: \"mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-md\", children: [_jsx(\"h4\", { className: \"text-sm font-medium mb-2\", children: \"Save Query\" }), _jsxs(\"div\", { className: \"space-y-2\", children: [_jsx(Input, { placeholder: \"Query name...\", value: saveQueryName, onChange: e => setSaveQueryName(e.target.value), className: \"text-sm\" }), _jsx(Input, { placeholder: \"Description (optional)...\", value: saveQueryDescription, onChange: e => setSaveQueryDescription(e.target.value), className: \"text-sm\" }), _jsxs(\"div\", { className: \"flex space-x-2\", children: [_jsx(Button, { size: \"sm\", onClick: () => handleSaveQuery(item), disabled: !saveQueryName.trim(), children: \"Save\" }), _jsx(Button, { size: \"sm\", variant: \"ghost\", onClick: () => {\n                                                            setShowSaveDialog(null);\n                                                            setSaveQueryName('');\n                                                            setSaveQueryDescription('');\n                                                        }, children: \"Cancel\" })] })] })] }))] }, item.id));\n                })) })] }));\n}\n", "import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport React, { useState } from 'react';\nimport { Save, Play, Trash2, Tag, Calendar, Search } from 'lucide-react';\nimport { useQueryStore } from '../../stores/query';\nimport { Button } from '../UI/Button';\nimport { Card } from '../UI/Card';\nimport { Input } from '../UI/Input';\nimport { Modal } from '../UI/Modal';\nimport { Badge } from '../UI/Badge';\nexport default function SavedQueries() {\n    const { savedQueries, selectedIndex, queryMode, visualQuery, dslQuery, saveQuery, loadSavedQuery, deleteSavedQuery, } = useQueryStore();\n    const [isModalOpen, setIsModalOpen] = useState(false);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [saveForm, setSaveForm] = useState({\n        name: '',\n        description: '',\n        tags: '',\n    });\n    const filteredQueries = savedQueries.filter(query => query.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        query.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        query.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())));\n    const handleSaveQuery = () => {\n        if (!selectedIndex || !saveForm.name.trim())\n            return;\n        const tags = saveForm.tags\n            .split(',')\n            .map(tag => tag.trim())\n            .filter(tag => tag.length > 0);\n        saveQuery(saveForm.name.trim(), saveForm.description.trim() || undefined, tags);\n        setSaveForm({ name: '', description: '', tags: '' });\n        setIsModalOpen(false);\n    };\n    const handleLoadQuery = (queryId) => {\n        loadSavedQuery(queryId);\n    };\n    const handleDeleteQuery = (queryId) => {\n        if (confirm('Are you sure you want to delete this saved query?')) {\n            deleteSavedQuery(queryId);\n        }\n    };\n    const canSaveQuery = selectedIndex &&\n        ((queryMode === 'visual' && visualQuery.conditions.length > 0) ||\n            (queryMode === 'dsl' &&\n                dslQuery.trim() !== '{\\n  \"query\": {\\n    \"match_all\": {}\\n  }\\n}'));\n    return (_jsxs(\"div\", { className: \"space-y-6\", children: [_jsxs(Card, { className: \"p-6\", children: [_jsxs(\"div\", { className: \"flex items-center justify-between mb-4\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-3\", children: [_jsx(Save, { className: \"w-6 h-6 text-blue-500\" }), _jsx(\"h2\", { className: \"text-xl font-semibold text-gray-900 dark:text-white\", children: \"Saved Queries\" })] }), _jsxs(Button, { onClick: () => setIsModalOpen(true), disabled: !canSaveQuery, className: \"flex items-center space-x-2\", children: [_jsx(Save, { className: \"w-4 h-4\" }), _jsx(\"span\", { children: \"Save Current Query\" })] })] }), _jsxs(\"div\", { className: \"relative\", children: [_jsx(Search, { className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" }), _jsx(Input, { placeholder: \"Search saved queries...\", value: searchTerm, onChange: e => setSearchTerm(e.target.value), className: \"pl-10\" })] })] }), _jsx(\"div\", { className: \"space-y-4\", children: filteredQueries.length === 0 ? (_jsx(Card, { className: \"p-12\", children: _jsxs(\"div\", { className: \"text-center text-gray-500 dark:text-gray-400\", children: [_jsx(Save, { className: \"w-12 h-12 mx-auto mb-4 opacity-50\" }), _jsx(\"h3\", { className: \"text-lg font-medium mb-2\", children: savedQueries.length === 0\n                                    ? 'No Saved Queries'\n                                    : 'No Matching Queries' }), _jsx(\"p\", { children: savedQueries.length === 0\n                                    ? 'Save your frequently used queries for quick access.'\n                                    : 'Try adjusting your search terms.' })] }) })) : (filteredQueries.map(query => (_jsx(Card, { className: \"p-6\", children: _jsxs(\"div\", { className: \"flex items-start justify-between\", children: [_jsxs(\"div\", { className: \"flex-1\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-3 mb-2\", children: [_jsx(\"h3\", { className: \"text-lg font-medium text-gray-900 dark:text-white\", children: query.name }), _jsx(Badge, { variant: \"outline\", className: \"text-xs\", children: query.index })] }), query.description && (_jsx(\"p\", { className: \"text-gray-600 dark:text-gray-400 mb-3\", children: query.description })), _jsxs(\"div\", { className: \"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400\", children: [_jsxs(\"div\", { className: \"flex items-center space-x-1\", children: [_jsx(Calendar, { className: \"w-4 h-4\" }), _jsx(\"span\", { children: new Date(query.createdAt).toLocaleDateString() })] }), query.tags.length > 0 && (_jsxs(\"div\", { className: \"flex items-center space-x-2\", children: [_jsx(Tag, { className: \"w-4 h-4\" }), _jsx(\"div\", { className: \"flex space-x-1\", children: query.tags.map(tag => (_jsx(Badge, { variant: \"secondary\", className: \"text-xs\", children: tag }, tag))) })] }))] })] }), _jsxs(\"div\", { className: \"flex items-center space-x-2 ml-4\", children: [_jsxs(Button, { variant: \"outline\", size: \"sm\", onClick: () => handleLoadQuery(query.id), className: \"flex items-center space-x-1\", children: [_jsx(Play, { className: \"w-4 h-4\" }), _jsx(\"span\", { children: \"Load\" })] }), _jsx(Button, { variant: \"ghost\", size: \"sm\", onClick: () => handleDeleteQuery(query.id), className: \"text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20\", children: _jsx(Trash2, { className: \"w-4 h-4\" }) })] })] }) }, query.id)))) }), _jsx(Modal, { isOpen: isModalOpen, onClose: () => setIsModalOpen(false), title: \"Save Query\", children: _jsxs(\"div\", { className: \"space-y-4\", children: [_jsxs(\"div\", { children: [_jsx(\"label\", { className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\", children: \"Query Name *\" }), _jsx(Input, { placeholder: \"Enter a name for this query\", value: saveForm.name, onChange: e => setSaveForm({ ...saveForm, name: e.target.value }) })] }), _jsxs(\"div\", { children: [_jsx(\"label\", { className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\", children: \"Description\" }), _jsx(Input, { placeholder: \"Optional description\", value: saveForm.description, onChange: e => setSaveForm({ ...saveForm, description: e.target.value }) })] }), _jsxs(\"div\", { children: [_jsx(\"label\", { className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\", children: \"Tags\" }), _jsx(Input, { placeholder: \"Enter tags separated by commas\", value: saveForm.tags, onChange: e => setSaveForm({ ...saveForm, tags: e.target.value }) }), _jsx(\"p\", { className: \"text-xs text-gray-500 mt-1\", children: \"Example: analytics, daily-report, user-data\" })] }), _jsxs(\"div\", { className: \"flex justify-end space-x-3 pt-4\", children: [_jsx(Button, { variant: \"outline\", onClick: () => setIsModalOpen(false), children: \"Cancel\" }), _jsx(Button, { onClick: handleSaveQuery, disabled: !saveForm.name.trim(), children: \"Save Query\" })] })] }) })] }));\n}\n", "export { default as QueryBuilder } from './QueryBuilder';\nexport { default as VisualQueryBuilder } from './VisualQueryBuilder';\nexport { default as DSLQueryEditor } from './DSLQueryEditor';\nexport { default as QueryCondition } from './QueryCondition';\nexport { default as SavedQueries } from './SavedQueries';\nexport { default as QueryHistory } from './QueryHistory';\n"], "names": [], "sourceRoot": ""}