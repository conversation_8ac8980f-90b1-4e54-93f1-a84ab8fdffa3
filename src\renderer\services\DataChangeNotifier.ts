/**
 * 数据变更通知系统
 * 提供实时数据变更通知和事件广播功能
 */

import { EventEmitter } from 'events';
import { IndexInfo, TabInfo, DataOperationHistory } from '../stores/unifiedDataState';

// 数据变更事件类型
export type DataChangeEventType =
  | 'index:created'
  | 'index:updated'
  | 'index:deleted'
  | 'index:selected'
  | 'index:favorite:added'
  | 'index:favorite:removed'
  | 'index:pinned:added'
  | 'index:pinned:removed'
  | 'tab:created'
  | 'tab:updated'
  | 'tab:closed'
  | 'tab:activated'
  | 'data:refreshed'
  | 'operation:completed'
  | 'cache:updated'
  | 'cache:cleared'
  | 'sync:started'
  | 'sync:completed'
  | 'sync:failed';

// 数据变更事件数据
export interface DataChangeEvent {
  type: DataChangeEventType;
  timestamp: Date;
  source: string;
  data: any;
  metadata?: {
    userId?: string;
    sessionId?: string;
    connectionId?: string;
    version?: number;
  };
}

// 通知配置
export interface NotificationConfig {
  enableRealTimeNotifications: boolean;
  enablePersistentNotifications: boolean;
  maxNotificationHistory: number;
  notificationTimeout: number;
  enableBroadcast: boolean;
  broadcastChannel?: string;
}

// 通知监听器
export type NotificationListener = (event: DataChangeEvent) => void;

// 通知过滤器
export type NotificationFilter = (event: DataChangeEvent) => boolean;

export class DataChangeNotifier extends EventEmitter {
  private static instance: DataChangeNotifier;
  private config: NotificationConfig;
  private notificationHistory: DataChangeEvent[] = [];
  private activeListeners = new Map<string, Set<NotificationListener>>();
  private filters = new Map<string, NotificationFilter>();
  private broadcastChannel?: BroadcastChannel;

  private constructor(config: Partial<NotificationConfig> = {}) {
    super();
    
    this.config = {
      enableRealTimeNotifications: true,
      enablePersistentNotifications: true,
      maxNotificationHistory: 1000,
      notificationTimeout: 5000,
      enableBroadcast: false,
      broadcastChannel: 'es-client-data-changes',
      ...config
    };

    this.setupBroadcastChannel();
    this.setupCleanupTimer();
  }

  public static getInstance(config?: Partial<NotificationConfig>): DataChangeNotifier {
    if (!DataChangeNotifier.instance) {
      DataChangeNotifier.instance = new DataChangeNotifier(config);
    }
    return DataChangeNotifier.instance;
  }

  /**
   * 发送数据变更通知
   */
  public notify(
    type: DataChangeEventType,
    data: any,
    source = 'unknown',
    metadata?: DataChangeEvent['metadata']
  ): void {
    const event: DataChangeEvent = {
      type,
      timestamp: new Date(),
      source,
      data,
      metadata
    };

    // 添加到历史记录
    if (this.config.enablePersistentNotifications) {
      this.addToHistory(event);
    }

    // 实时通知
    if (this.config.enableRealTimeNotifications) {
      this.processNotification(event);
    }

    // 广播通知
    if (this.config.enableBroadcast && this.broadcastChannel) {
      this.broadcastChannel.postMessage(event);
    }
  }

  /**
   * 添加事件监听器
   */
  public addListener(
    eventType: DataChangeEventType | DataChangeEventType[],
    listener: NotificationListener,
    listenerId?: string
  ): string {
    const id = listenerId || this.generateListenerId();
    const types = Array.isArray(eventType) ? eventType : [eventType];

    types.forEach(type => {
      if (!this.activeListeners.has(type)) {
        this.activeListeners.set(type, new Set());
      }
      this.activeListeners.get(type)!.add(listener);
      
      // 使用EventEmitter的监听器
      super.on(type, listener);
    });

    return id;
  }

  /**
   * 移除事件监听器
   */
  public removeListener(
    eventType: DataChangeEventType | DataChangeEventType[],
    listener: NotificationListener
  ): void {
    const types = Array.isArray(eventType) ? eventType : [eventType];

    types.forEach(type => {
      const listeners = this.activeListeners.get(type);
      if (listeners) {
        listeners.delete(listener);
        if (listeners.size === 0) {
          this.activeListeners.delete(type);
        }
      }
      
      // 移除EventEmitter的监听器
      super.off(type, listener);
    });
  }

  /**
   * 添加通知过滤器
   */
  public addFilter(filterId: string, filter: NotificationFilter): void {
    this.filters.set(filterId, filter);
  }

  /**
   * 移除通知过滤器
   */
  public removeFilter(filterId: string): void {
    this.filters.delete(filterId);
  }

  /**
   * 获取通知历史
   */
  public getNotificationHistory(
    filter?: {
      type?: DataChangeEventType;
      source?: string;
      since?: Date;
      limit?: number;
    }
  ): DataChangeEvent[] {
    let history = [...this.notificationHistory];

    if (filter) {
      if (filter.type) {
        history = history.filter(event => event.type === filter.type);
      }
      if (filter.source) {
        history = history.filter(event => event.source === filter.source);
      }
      if (filter.since) {
        history = history.filter(event => event.timestamp >= filter.since!);
      }
      if (filter.limit) {
        history = history.slice(-filter.limit);
      }
    }

    return history.reverse(); // 最新的在前面
  }

  /**
   * 清除通知历史
   */
  public clearHistory(): void {
    this.notificationHistory = [];
  }

  /**
   * 获取活动监听器统计
   */
  public getListenerStats(): {
    totalListeners: number;
    listenersByType: Record<string, number>;
    activeFilters: number;
  } {
    const listenersByType: Record<string, number> = {};
    let totalListeners = 0;

    this.activeListeners.forEach((listeners, type) => {
      listenersByType[type] = listeners.size;
      totalListeners += listeners.size;
    });

    return {
      totalListeners,
      listenersByType,
      activeFilters: this.filters.size
    };
  }

  /**
   * 批量通知
   */
  public batchNotify(notifications: Array<{
    type: DataChangeEventType;
    data: any;
    source?: string;
    metadata?: DataChangeEvent['metadata'];
  }>): void {
    notifications.forEach(({ type, data, source, metadata }) => {
      this.notify(type, data, source, metadata);
    });
  }

  /**
   * 索引相关通知方法
   */
  public notifyIndexCreated(index: IndexInfo, source = 'system'): void {
    this.notify('index:created', { index }, source);
  }

  public notifyIndexUpdated(indexName: string, updates: Partial<IndexInfo>, source = 'system'): void {
    this.notify('index:updated', { indexName, updates }, source);
  }

  public notifyIndexDeleted(indexName: string, source = 'system'): void {
    this.notify('index:deleted', { indexName }, source);
  }

  public notifyIndexSelected(indexName: string, source = 'user'): void {
    this.notify('index:selected', { indexName }, source);
  }

  public notifyIndexFavoriteAdded(indexName: string, source = 'user'): void {
    this.notify('index:favorite:added', { indexName }, source);
  }

  public notifyIndexFavoriteRemoved(indexName: string, source = 'user'): void {
    this.notify('index:favorite:removed', { indexName }, source);
  }

  public notifyIndexPinnedAdded(indexName: string, source = 'user'): void {
    this.notify('index:pinned:added', { indexName }, source);
  }

  public notifyIndexPinnedRemoved(indexName: string, source = 'user'): void {
    this.notify('index:pinned:removed', { indexName }, source);
  }

  /**
   * 标签页相关通知方法
   */
  public notifyTabCreated(tab: TabInfo, source = 'user'): void {
    this.notify('tab:created', { tab }, source);
  }

  public notifyTabUpdated(tabId: string, updates: Partial<TabInfo>, source = 'user'): void {
    this.notify('tab:updated', { tabId, updates }, source);
  }

  public notifyTabClosed(tabId: string, source = 'user'): void {
    this.notify('tab:closed', { tabId }, source);
  }

  public notifyTabActivated(tabId: string, source = 'user'): void {
    this.notify('tab:activated', { tabId }, source);
  }

  /**
   * 数据操作相关通知方法
   */
  public notifyDataRefreshed(indexName?: string, source = 'user'): void {
    this.notify('data:refreshed', { indexName }, source);
  }

  public notifyOperationCompleted(operation: DataOperationHistory, source = 'system'): void {
    this.notify('operation:completed', { operation }, source);
  }

  /**
   * 缓存相关通知方法
   */
  public notifyCacheUpdated(indexName: string, data: any, source = 'system'): void {
    this.notify('cache:updated', { indexName, data }, source);
  }

  public notifyCacheCleared(indexName?: string, source = 'system'): void {
    this.notify('cache:cleared', { indexName }, source);
  }

  /**
   * 同步相关通知方法
   */
  public notifySyncStarted(source = 'system'): void {
    this.notify('sync:started', {}, source);
  }

  public notifySyncCompleted(source = 'system'): void {
    this.notify('sync:completed', {}, source);
  }

  public notifySyncFailed(error: string, source = 'system'): void {
    this.notify('sync:failed', { error }, source);
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<NotificationConfig>): void {
    this.config = { ...this.config, ...config };
    
    if (config.enableBroadcast !== undefined) {
      if (config.enableBroadcast && !this.broadcastChannel) {
        this.setupBroadcastChannel();
      } else if (!config.enableBroadcast && this.broadcastChannel) {
        this.broadcastChannel.close();
        this.broadcastChannel = undefined;
      }
    }
  }

  // 私有方法

  /**
   * 处理通知
   */
  private processNotification(event: DataChangeEvent): void {
    // 应用过滤器
    const shouldNotify = Array.from(this.filters.values()).every(filter => filter(event));
    
    if (!shouldNotify) {
      return;
    }

    // 发送事件
    this.emit(event.type, event);
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(event: DataChangeEvent): void {
    this.notificationHistory.push(event);
    
    // 限制历史记录大小
    if (this.notificationHistory.length > this.config.maxNotificationHistory) {
      this.notificationHistory = this.notificationHistory.slice(-this.config.maxNotificationHistory);
    }
  }

  /**
   * 生成监听器ID
   */
  private generateListenerId(): string {
    return `listener-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 设置广播通道
   */
  private setupBroadcastChannel(): void {
    if (this.config.enableBroadcast && this.config.broadcastChannel) {
      try {
        this.broadcastChannel = new BroadcastChannel(this.config.broadcastChannel);
        
        this.broadcastChannel.addEventListener('message', (event) => {
          const dataChangeEvent = event.data as DataChangeEvent;
          if (dataChangeEvent && dataChangeEvent.type) {
            this.processNotification(dataChangeEvent);
          }
        });
      } catch (error) {
        console.warn('无法创建广播通道:', error);
      }
    }
  }

  /**
   * 设置清理定时器
   */
  private setupCleanupTimer(): void {
    // 每小时清理一次过期的历史记录
    setInterval(() => {
      const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24小时前
      this.notificationHistory = this.notificationHistory.filter(
        event => event.timestamp > cutoffTime
      );
    }, 60 * 60 * 1000); // 1小时
  }
}

export default DataChangeNotifier;
